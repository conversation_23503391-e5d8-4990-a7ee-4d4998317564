# Account API - V2

## Deployment
Deployment steps are mentioned in [DEPLOYMENT.md](DEPLOYMENT.md)

## Development

### Using virtualenv
Make sure `python3.7 -V` is installed
```sh
$ pip3 install virtualenv
$ virtualenv -p python3.7 venv
$ source ./venv/bin/activate
```

Install Dependencies
```sh
$(venv) pip install -r requirements/local.txt
```

Start Server
```sh 
$(venv) python manage.py runserver 0.0.0.0:8000
```

Initialize git repository then setup pre-commit hooks to run `black` and `flake8` before commit
```sh
git init
pre-commit install
```

### Using Docker

Build Image
```sh 
$ docker-compose -f docker-compose.dev.yml build
```

Run container
```sh
$ docker-compose -f docker-compose.dev.yml up -d
$ docker-compose logs -f servicename
```

### Type checks

Running type checks with mypy:

```sh
$ mypy accounts
```

### Test coverage
To run the tests, check your test coverage, and generate an HTML coverage report::
```sh
$ coverage run -m pytest
$ coverage html
$ open htmlcov/index.html
```
To check the report in console:
```sh
$ coverage report -m
```

### Running tests with pytest
Use [pytest-django](https://pytest-django.readthedocs.io/en/latest/index.html) to write your test cases
```sh
$ pytest
```
