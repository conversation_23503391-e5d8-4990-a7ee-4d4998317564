[flake8]
max-line-length = 80
exclude = .tox,.git,*/migrations/*,*/static/CACHE/*,docs,venv
ignore = E203, E266, E501, W503, F403, F401, F405, W293
select = B,C,E,F,W,T4,B9
max-complexity = 18

[pycodestyle]
max-line-length = 80
exclude = .tox,.git,*/migrations/*,*/static/CACHE/*,docs

[mypy]
python_version = 3.7
check_untyped_defs = True
ignore_missing_imports = True
warn_unused_ignores = True
warn_redundant_casts = True
warn_unused_configs = True

[mypy.plugins.django-stubs]
django_settings_module = config.settings.test

[mypy-*.migrations.*]
# Django migrations should not produce any errors:
ignore_errors = True
