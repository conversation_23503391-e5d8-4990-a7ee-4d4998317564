pipeline {
  agent {
    label 'slave'
  }

  options {
    timestamps()
    parallelsAlwaysFailFast()
    disableConcurrentBuilds()
    gitLabConnection('gitlab')
    buildDiscarder(logRotator(numToKeepStr: '10', artifactNumToKeepStr: '10'))
  }

  stages {
    stage('Build') {
      steps {
          slackSend channel: 'jenkins-stage', message: """
            Build started `${currentBuild.fullDisplayName}`. (<${env.BUILD_URL}|Open>) (<${env.CHANGE_URL ?: env.GIT_URL}|Repo>)
          """, color: "warning"
        }
    }

    stage('test and lint') {
      parallel {

        stage('test - 3.10') {
          agent {
            docker {
              label 'slave'
              image 'python:3.10-buster'
              args '-u 0'
            }
          }
          steps {
            updateGitlabCommitStatus name: 'test - 3.10', state: 'pending'
            sh 'pip install -r requirements/local.txt'
            sh 'pytest'
          }
          post {
            success {
              updateGitlabCommitStatus name: 'test - 3.10', state: 'success'
            }
            unsuccessful {
              updateGitlabCommitStatus name: 'test - 3.10', state: 'failed'
            }
            cleanup {
              sh "rm -rf .pytest_cache reports/"
            }
          }
        }

        stage('lint - 3.10') {
          agent {
            docker {
              label 'slave'
              image 'python:3.10-buster'
              args '-u 0'
            }
          }
          steps {
            updateGitlabCommitStatus name: 'lint - 3.10', state: 'pending'
            sh 'cat requirements/local.txt | grep "^flake8" | sed "s/\\s*#.*$//" | xargs pip install'
            sh 'flake8'
          }

          post {
            success {
              updateGitlabCommitStatus name: 'lint - 3.10', state: 'success'
            }
            unsuccessful {
              updateGitlabCommitStatus name: 'lint - 3.10', state: 'failed'
            }
          }
        }
      }
    }

    stage('Approval') {
      when {
        anyOf {
          branch pattern: "release/[\\w.]+", comparator: "REGEXP"
          branch "main"
          buildingTag()
          changeRequest branch: 'release/[\\w.]+', comparator: "REGEXP"
        }
      }
      options {
        timeout(time: 1, unit: 'DAYS')
      }

      steps {
        updateGitlabCommitStatus name: 'approve_deploy', state: 'pending'
        script {
          user_inp = input id: 'deployer', message: 'Select deploy env', parameters: [string(defaultValue: 'current', description: 'Enter APP version number (eg. v1, v2, v3) \n Note: when envionment select is "blue", the version is always "beta"', name: 'app_version', trim: true), choice(choices: ["staging", "blue", "green", "None"], name: 'deploy_env')]
          env.DEPLOY_TO = user_inp.deploy_env
          env.APP_VERSION = user_inp.app_version
        }
      }
    }

    stage('Deploy (staging)') {
      when {
        environment name: 'DEPLOY_TO', value: 'staging'
      }
      options {
        withAWS(credentials: 'AWS-Credentials-Stage', region: 'ap-south-1')
      }
      environment {
        GIT_CREDS = credentials('gitlab-http')
        WORKSPACE = "stage"
      }
      steps {
        updateGitlabCommitStatus name: 'staging_deploy', state: 'pending'
        dir("deploy/ansible") {
          sh "./env-manager.sh -i account_api_v2/${env.APP_VERSION} fetch"
          sh "GITLAB_USER=$GIT_CREDS_USR GITLAB_TOKEN=$GIT_CREDS_PSW ansible-playbook -i inventory/staging.aws_ec2.yml staging.yml -e git_branch=$GIT_BRANCH -e app_version=${env.APP_VERSION} -e git_repo=${env.GIT_URL}"
        }

        dir("deploy/terraform/aws") {
          s3Download(file: './terraform.tfvars', bucket: 'stage-ecs-env-myop', path: 'account_api_v2/terraform.tfvars')
          withAWS(credentials: 'AWS-Credentials-Stage', region: 'ap-south-1') {
            sh "terraform init"
            sh "./workspace.sh"
          }
          script {
            def terraformPlanExitCode = sh(
                script: "terraform plan -detailed-exitcode",
                returnStatus: true
            )
            // sh "terraform plan -detailed-exitcode"
            if (terraformPlanExitCode == 2) {
              input message: "Terraform plan reviewed?", ok: "Yes"
              sh "terraform apply -auto-approve"
            } else if (terraformPlanExitCode == 0) {
              echo "No changes in Terraform plan. Skipping apply step."
            } else {
              error "Terraform plan failed or encountered an error. Skipping apply step."
            }
          }
        }
      }
      post {
        success {
          updateGitlabCommitStatus name: 'staging_deploy', state: 'success'
          slackSend channel: 'jenkins-stage', message: "STAGING deployment complete: `${currentBuild.fullDisplayName}` (<${env.BUILD_URL}|Open>)", color: 'good'
        }
        failure {
          updateGitlabCommitStatus name: 'staging_deploy', state: 'failed'
          slackSend channel: 'jenkins-stage', message: "STAGING deployment failed: `${currentBuild.fullDisplayName}` (<${env.BUILD_URL}|Open>)", color: 'danger'
        }
        aborted {
          updateGitlabCommitStatus name: 'staging_deploy', state: 'success'
          slackSend channel: 'jenkins-stage', message: "STAGING deployment cancelled: `${currentBuild.fullDisplayName}` (<${env.BUILD_URL}|Open>)", color: 'good'
        }
      }
    }

    stage('Deploy (blue)') {
      when {
        environment name: 'DEPLOY_TO', value: 'blue'
      }
      options {
        withAWS(credentials: 'AWS-Credentials', region: 'ap-south-1')
      }
      environment {
          GIT_CREDS = credentials('gitlab-http')
      }
      steps {
        updateGitlabCommitStatus name: 'blue_deploy', state: 'pending'
        dir("deploy/ansible") {
          sh "./env-manager.sh -i account_api_v2/current fetch"
          sh "GITLAB_USER=$GIT_CREDS_USR GITLAB_TOKEN=$GIT_CREDS_PSW ansible-playbook -i inventory/prod.aws_ec2.yml prod.yml -e git_branch=$GIT_BRANCH -e git_repo=${env.GIT_URL}"
        }
      }

      post {
          success {
            updateGitlabCommitStatus name: 'blue_deploy', state: 'success'
            slackSend channel: 'jenkins-stage', message: "BLUE deployment complete: `${currentBuild.fullDisplayName}` (<${env.BUILD_URL}|Open>)", color: 'good'
          }
          failure {
            updateGitlabCommitStatus name: 'blue_deploy', state: 'failed'
            slackSend channel: 'jenkins-stage', message: "BLUE deployment failed: `${currentBuild.fullDisplayName}` (<${env.BUILD_URL}|Open>)", color: 'danger'
          }
          aborted {
            updateGitlabCommitStatus name: 'blue_deploy', state: 'success'
            slackSend channel: 'jenkins-stage', message: "BLUE deployment cancelled: `${currentBuild.fullDisplayName}` (<${env.BUILD_URL}|Open>)", color: 'good'
          }
      }
    }

    stage('Deploy (green)') {
      when {
        environment name: 'DEPLOY_TO', value: 'green'
      }
      options {
        withAWS(credentials: 'AWS-Credentials', region: 'ap-south-1')
      }
      environment {
        GIT_CREDS = credentials('gitlab-http')
        WORKSPACE = "prod"
      }
      steps {
        updateGitlabCommitStatus name: 'green_deploy', state: 'pending'
        dir("deploy/ansible") {
          sh "./env-manager.sh -i account_api_v2/current fetch"
          sh "GITLAB_USER=$GIT_CREDS_USR GITLAB_TOKEN=$GIT_CREDS_PSW ansible-playbook -i inventory/prod.aws_ec2.yml prod.yml -e git_branch=$GIT_BRANCH -e git_repo=${env.GIT_URL} -e app_version=${env.APP_VERSION}"
        }

        dir("deploy/terraform/aws") {
          s3Download(file: './terraform.tfvars', bucket: 'prod-ecs-env-myop', path: 'account_api_v2/terraform.tfvars')
          withAWS(credentials: 'AWS-Credentials-Stage', region: 'ap-south-1') {
            sh "terraform init"
            sh "./workspace.sh"
          }
          script {
            // sh "terraform plan -detailed-exitcode"
            // def terraformPlanExitCode = sh(script: 'echo $?', returnStdout: true).trim().toInteger()
            def terraformPlanExitCode = sh(
                script: "terraform plan -detailed-exitcode",
                returnStatus: true
            )
            if (terraformPlanExitCode == 2) {
              input message: "Terraform plan reviewed?", ok: "Yes"
              sh "terraform apply -auto-approve"
            } else if (terraformPlanExitCode == 0) {
              echo "No changes in Terraform plan. Skipping apply step."
            } else {
              error "Terraform plan failed or encountered an error. Skipping apply step."
            }
          }
        }
      }

      post {
        success {
          updateGitlabCommitStatus name: 'green_deploy', state: 'success'
          slackSend channel: 'jenkins-stage', message: "GREEN deployment complete: `${currentBuild.fullDisplayName}` (<${env.BUILD_URL}|Open>)", color: 'good'
        }
        failure {
          updateGitlabCommitStatus name: 'green_deploy', state: 'failed'
          slackSend channel: 'jenkins-stage', message: "GREEN deployment failed: `${currentBuild.fullDisplayName}` (<${env.BUILD_URL}|Open>)", color: 'danger'
        }
        aborted {
          updateGitlabCommitStatus name: 'green_deploy', state: 'success'
          slackSend channel: 'jenkins-stage', message: "GREEN deployment cancelled: `${currentBuild.fullDisplayName}` (<${env.BUILD_URL}|Open>)", color: 'good'
        }
      }
    }
  }
  
  post {
    aborted {
      slackSend channel: 'jenkins-stage', message: "Job Cancelled: `${currentBuild.fullDisplayName}` | <${env.BUILD_URL}|Open>", color: '#e8e6e3'
    }

    failure {
      slackSend channel: 'jenkins-stage', message: "Job Failed: `${currentBuild.fullDisplayName}` | <${env.BUILD_URL}|Open>", color: 'danger'
    }

    success {
        slackSend channel: 'jenkins-stage', message: """
          Build succeeded `${currentBuild.fullDisplayName}`. (<${env.BUILD_URL}|Open>) (<${env.CHANGE_URL ?: env.GIT_URL}|Repo>)
          """, color: "good"
		}

    always {
      cleanWs()
    }
  }
}
