version: '3'

services:
  app:
    build:
      context: .
      dockerfile: ./compose/production/django/Dockerfile
    env_file:
      - .env
    image: myop/account_api_v2:latest
    volumes:
      - ".:/app"
      - "${LOG_PATH:-/var/log/app/account_api_v2}:/var/log/app/"
    ports:
      - "${DOCKER_PORT:-8000}:8000"
    command: /start

  celery:
    image: myop/account_api_v2:latest
    volumes:
      - ".:/app"
      - "${LOG_PATH_CELERY:-/var/log/app/account_api_v2/celery}:/var/log/app/"
    ports: []
    command: celery -A config.celery.app worker --loglevel=INFO
