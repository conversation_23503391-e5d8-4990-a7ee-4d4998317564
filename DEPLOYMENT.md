# Deployment

### Create log directory

```sh
$ mkdir -p /var/log/app/account_api_v2/ && chmod -R 777 /var/log/app/account_api_v2/
```

### Create environment configuration

Put the following in `.env` file

```dotenv
DJANGO_SETTINGS_MODULE=config.settings.production

DJANGO_SECRET_KEY=<random_generated_key>
DJANGO_ALLOWED_HOSTS=localhost,127.0.0.1
DJANGO_SECURE_SSL_REDIRECT=False
DJANGO_SECURE_HSTS_INCLUDE_SUBDOMAINS=False
DJANGO_SECURE_HSTS_PRELOAD=False
DJANGO_SECURE_CONTENT_TYPE_NOSNIFF=False
LOGGING_PATH=/var/log/app/account_api_v2
DATABASE_URL=mysql://user:password@localhost:3306/dbname

# Redis Config
REDIS_URL=redis://<redis_host>:6379/<database>
```

You can create random key for django using:
```sh
python -c 'import random; result = "".join([random.choice("abcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*(-_=+)") for i in range(50)]); print(result)'
```

### Clone the project

Try to clone in some standard location. DO NOT CLONE in `/var/www/html`

Deploy in `/var/app/python/account_api_v2/` directory.

```sh
$ git clone https://gitlab.com/VoiceTree/account_api_v2.git .
```

### Install Dependencies
Use pip3 or pip for python3 only

```sh
$ pip3 install -r requirements/production.txt
```
