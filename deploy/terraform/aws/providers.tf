locals {
  common_tags = {
    group     = "accounts"
    CreatedBy = "terraform"
  }
}

terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  backend "s3" {
    bucket = "stage-myop-terraform-state"
    key    = "accounts_v2/accounts_v2.tfstate"
    region = "ap-south-1"
  }
}

provider "aws" {
  region  = var.region
#  profile = "dev" # uncomment this when using locally
  default_tags {
    tags = local.common_tags
  }
}