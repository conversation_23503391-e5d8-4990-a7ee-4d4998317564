resource "aws_sns_topic_subscription" "reactivation_process" {
  topic_arn              = data.aws_sns_topic.accounts_sns.arn
  protocol               = "https"
  endpoint               = var.REACTIVATION_PROCESS_URL
  raw_message_delivery   = true
  endpoint_auto_confirms = true
  filter_policy = jsonencode(
    {
      "namespace" : [
        "accounts"
      ],
      "service_type" : [
        "heyo"
      ],
      "action" : [
        "service_reactivated"
      ]
    }
  )
}

resource "aws_sns_topic_subscription" "process_payment_failure_attempt" {
  topic_arn              = data.aws_sns_topic.accounts_sns.arn
  protocol               = "https"
  endpoint               = var.PROCESS_PAYMENT_FAILURE_ATTEMPT_URL
  raw_message_delivery   = true
  endpoint_auto_confirms = true
  filter_policy = jsonencode(
    {
      "namespace" : [
        "accounts"
      ],
      "service_type" : [
        "heyo"
      ],
      "action" : [
        "payment_failed"
      ]
    }
  )
}


resource "aws_sns_topic_subscription" "process_activation" {
  topic_arn              = data.aws_sns_topic.accounts_sns.arn
  protocol               = "https"
  endpoint               = var.PROCESS_ACTIVATION_URL
  raw_message_delivery   = true
  endpoint_auto_confirms = true
  filter_policy = jsonencode(
    {
      "namespace" : [
        "accounts"
      ],
      "service_type" : [
        "heyo"
      ],
      "action" : [
        "payment_succeeded"
      ]
    }
  )
}


resource "aws_sns_topic_subscription" "feature_process" {
  topic_arn              = data.aws_sns_topic.accounts_sns.arn
  protocol               = "https"
  endpoint               = var.FEATURE_PROCESS_URL
  raw_message_delivery   = true
  endpoint_auto_confirms = true
  filter_policy = jsonencode(
    {
      "namespace" : [
        "accounts"
      ],
      "service_type" : [
        "heyo"
      ],
      "action" : [
        "payment_succeeded"
      ]
    }
  )
}


resource "aws_sns_topic_subscription" "changed_process" {
  topic_arn              = data.aws_sns_topic.accounts_sns.arn
  protocol               = "https"
  endpoint               = var.CHANGED_PROCESS_URL
  raw_message_delivery   = true
  endpoint_auto_confirms = true
  filter_policy = jsonencode(
    {
      "namespace" : [
        "accounts"
      ],
      "service_type" : [
        "heyo"
      ],
      "action" : [
        "package_changed"
      ]
    }
  )
}


resource "aws_sns_topic_subscription" "cancel_payment_subscription" {
  topic_arn              = data.aws_sns_topic.accounts_sns.arn
  protocol               = "https"
  endpoint               = var.CANCEL_PAYMENT_SUBSCRIPTION_URL
  raw_message_delivery   = true
  endpoint_auto_confirms = true
  filter_policy = jsonencode(
    {
      "namespace" : [
        "accounts"
      ],
      "service_type" : [
        "heyo"
      ],
      "action" : [
        "payment_subscription_deactivated"
      ]
    }
  )
}

resource "aws_sns_topic_subscription" "event_process" {
  topic_arn              = data.aws_sns_topic.accounts_sns.arn
  protocol               = "https"
  endpoint               = var.EVENT_PROCESS_URL
  raw_message_delivery   = true
  endpoint_auto_confirms = true
  filter_policy = jsonencode(
    {
      "namespace" : [
        "accounts"
      ],
      "service_type" : [
        "heyo"
      ],
      "action" : [
        "payment_succeeded"
      ]
    }
  )
}