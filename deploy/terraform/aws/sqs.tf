resource "aws_sqs_queue" "event_billing_sqs" {
  name                        = var.EVENT_BILLING_SQS_NAME
  message_retention_seconds   = 3600
  visibility_timeout_seconds  = 30

  policy = templatefile("./files/sqs_event_billing_policy.json", {
    sqs_policy = var.EVENT_BILLING_SQS_NAME,
    region     = var.region,
    account_id = data.aws_caller_identity.current.account_id
  })

  tags = {
    Name = var.EVENT_BILLING_SQS_NAME
  }
}
