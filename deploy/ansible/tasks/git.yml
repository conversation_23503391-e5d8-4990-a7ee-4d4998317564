---
- name: Check if release folder is empty
  find:
    paths: "{{ release_dir }}"
  register: filesFound
- name: git clone
  ansible.builtin.expect:
    chdir: "{{ release_dir }}"
    command: "git clone {{ git_repo }} {{ release_dir }}"
    responses:
      (?i)Username.*: "{{ lookup('env','GITLAB_USER') }}"
      (?i)Password.*: "{{ lookup('env','GITLAB_TOKEN') }}"
  when: filesFound.matched <= 0
  # you don't want to show passwords in your logs
  no_log: false
- name: git fetch
  ansible.builtin.expect:
    chdir: "{{ release_dir }}"
    command: git fetch origin
    responses:
      (?i)Username.*: "{{ lookup('env','GITLAB_USER') }}"
      (?i)Password.*: "{{ lookup('env','GITLAB_TOKEN') }}"
  no_log: false
- name: git checkout
  shell: git checkout {{ git_branch }}
  args:
    chdir: "{{ release_dir }}"
- name: git pull
  ansible.builtin.expect:
    chdir: "{{ release_dir }}"
    command: git pull origin {{ git_branch }}
    responses:
      (?i)Username.*: "{{ lookup('env','GITLAB_USER') }}"
      (?i)Password.*: "{{ lookup('env','GITLAB_TOKEN') }}"
  # you don't want to show passwords in your logs
  no_log: false
