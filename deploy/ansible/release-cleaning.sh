#!/bin/bash

set -e

APP_DIR="$project_base"
RELEASE_DIR="$project_base/releases"
# 1. Print directories where symlinks are pointed inside RELEASE_DIR
echo "Symlink Targets:"
SYMLINK_TARGETS=$(find $APP_DIR -maxdepth 1 -type l -exec readlink -f {} \;)
echo "$SYMLINK_TARGETS"


echo ""

# 2. Print directories inside RELEASE_DIR only (excluding RELEASE_DIR itself)
echo "Directories inside releases directory:"
DIRECTORIES=$(find releases/* -maxdepth 0 -type d)
echo "$DIRECTORIES"
echo ""

# 3. Collect directories that are NOT symlink targets
NON_SYMLINK_DIRS=()
for dir in $DIRECTORIES; do
    if ! echo "$SYMLINK_TARGETS" | grep -q "$dir"; then
        NON_SYMLINK_DIRS+=("$dir")
    fi
done

echo "Directories not symlinked to anyone:"
for dir in "${NON_SYMLINK_DIRS[@]}"; do
    echo "$dir"
done

echo ""

# 4. Sort the directories by their timestamp (YYYYMMDDHHMM format) and keep the latest 2
NUM_DIRS=${#NON_SYMLINK_DIRS[@]}
if [ "$NUM_DIRS" -le 2 ]; then
    echo "There are only $NUM_DIRS non-symlinked directories, no need to delete."
else
    # Sort directories based on the timestamp in their names
    NON_SYMLINK_DIRS_SORTED=($(printf '%s\n' "${NON_SYMLINK_DIRS[@]}" | sort -r))

    # Keep the last 2 directories
    DIRS_TO_KEEP=("${NON_SYMLINK_DIRS_SORTED[@]:0:2}")

    # Delete the rest
    DIRS_TO_DELETE=("${NON_SYMLINK_DIRS_SORTED[@]:2}")

    echo "Keeping directories:"
    for dir in "${DIRS_TO_KEEP[@]}"; do
        echo "$dir"
    done

    echo ""

    echo "Deleting directories:"
    for dir in "${DIRS_TO_DELETE[@]}"; do
        echo "Removing $dir"
        rm -rf "$dir"
    done
fi
