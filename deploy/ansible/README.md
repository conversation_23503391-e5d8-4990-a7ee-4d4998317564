## Ansible deployment

### Prerequisites:

This ansible assumes that you have:

- Python 3.10+ installed on remote instances
- Remote EC2 instances are properly tagged
- Ansible [aws_ec2 inventory plugin](https://docs.ansible.com/ansible/latest/collections/amazon/aws/aws_ec2_inventory.html) is installed
- Ansible uses python3+

### Usage

Set the following environment vars:

```env
AWS_PROFILE=<your aws profile>
GITLAB_USER=root
GITLAB_TOKEN=glpat-***
```

You can use by executing:

```sh
# To deploy on staging env
AWS_PROFILE=stage GITLAB_USER=root GITLAB_TOKEN=glpat-*** ansible-playbook -i inventory/staging.aws_ec2.yml staging.yml -e "git_branch=main"

# To deploy on prod
AWS_PROFILE=prod GITLAB_USER=root GITLAB_TOKEN=glpat-*** ansible-playbook -i inventory/prod.aws_ec2.yml prod.yml -e "git_branch=main"
```

However, if you wish to only prepare the server (but not restart services), you can skip the `green` tag:

```sh
# To deploy on staging env
AWS_PROFILE=stage GITLAB_USER=root GITLAB_TOKEN=glpat-*** ansible-playbook -i inventory/staging.aws_ec2.yml staging.yml -e "git_branch=main" --skip-tags green

# To deploy on prod
AWS_PROFILE=prod GITLAB_USER=root GITLAB_TOKEN=glpat-*** ansible-playbook -i inventory/prod.aws_ec2.yml prod.yml -e "git_branch=main" --skip-tags green
```

And to only run the green tag

```sh
# To deploy on staging env
AWS_PROFILE=stage GITLAB_USER=root GITLAB_TOKEN=glpat-*** ansible-playbook -i inventory/staging.aws_ec2.yml staging.yml -e "git_branch=main" -t green

# To deploy on prod
AWS_PROFILE=prod GITLAB_USER=root GITLAB_TOKEN=glpat-*** ansible-playbook -i inventory/prod.aws_ec2.yml prod.yml -e "git_branch=main" -t green
```
