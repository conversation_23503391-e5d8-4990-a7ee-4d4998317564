[uwsgi]
plugins-dir = /usr/local/lib/uwsgi
plugin = python310

project = {{ app_version_name }}
log_dir = {{ log_dir }}
base = {{ project_base }}/{{ app_version }}

uid = www-data
gid = www-data

# general
chdir = %(base)
virtualenv = %(base)/venv
socket = /tmp/%(project).sock
;module = config.wsgi:application
strict = false
master = true
enable-threads = true
vacuum =  true ; Delete sockets during shutdown
single-interpreter = true
die-on-term = true                     ; Shutdown when receiving SIGTERM (default is respawn)
need-app = true
safe-pidfile = /run/uwsgi/%(project).pid

#logging
log-master = true
disable-logging = false
log-4xx = true
log-5xx = true
log-date = %%Y-%%m-%%d %%H:%%M:%%S
logformat-strftime = true
log-format = %(addr) - %(pid) - [%(ltime)] "%(method) %(uri) %(proto)" %(status) %(size) %(msecs) "%(referer)" "%(uagent)"
daemonize = %(log_dir)/uwsgi.log
;req-logger = file:%(log_dir)/uwsgi-request.log
logger = application file:%(log_dir)/app.log
log-route = application uid

# concurrency
processes = 2
max-requests = 2000                  ; Restart workers after this many requests
max-worker-lifetime = 3600           ; Restart workers after this many seconds
reload-on-rss = 2048                 ; Restart workers after this much resident memory
worker-reload-mercy = 10             ; How long to wait before forcefully killing workers
harakiri = 20                        ; forcefully kill workers after 60 seconds
post-buffering = 4096                ; post buffering config

# process - pid
auto-procname = true                 ; Better names for workers so we can kill them manually
procname-prefix = %(project)

# graceful chained reload
lazy-apps = true
touch-chain-reload = %p
hook-post-fork = chdir:%(base)
chmod-socket = 664
chown-socket = www-data:www-data
buffer-size=65535

eval = import newrelic.agent; from config.wsgi import application; newrelic.agent.initialize('/etc/newrelic-infra/newrelic.ini'); application = newrelic.agent.WSGIApplicationWrapper(application)