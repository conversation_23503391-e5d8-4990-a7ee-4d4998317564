[group:{{ app_version_name }}]
programs={{ app_version_name }}_celery,{{ app_version_name }}_billing_event_consumer

; Celery Worker
[program:{{ app_version_name }}_celery]
directory={{ project_base }}/{{ app_version }}/
command={{ project_base }}/{{ app_version }}/venv/bin/newrelic-admin run-program {{ project_base }}/{{ app_version }}/venv/bin/celery -A config.celery.app worker --loglevel=DEBUG -n worker@{{ app_version_name }} -Q accounts --concurrency=1 --without-gossip --without-mingle --without-heartbeat -Ofair

environment=NEW_RELIC_CONFIG_FILE=/etc/newrelic-infra/newrelic.ini,NEW_RELIC_ENVIRONMENT=production

user=www-data
numprocs=1
stdout_logfile={{ log_dir }}/supervisor/celery_worker.log
stderr_logfile={{ log_dir }}/supervisor/celery_worker_error.log
autostart=true
autorestart=true
startsecs=0
killasgroup=true
; Need to wait for currently executing tasks to finish at shutdown. Increase this if you have very long running tasks.
stopwaitsecs = 600
; Causes supervisor to send the termination signal (SIGTERM) to the whole process group.
stopasgroup=true


; Billing Event Consumer
[program:{{ app_version_name }}_billing_event_consumer]
environment=LOGGING_PATH={{ log_dir }}/billing_event_consumer.log
directory={{ project_base }}/{{ app_version }}/
command={{ project_base }}/{{ app_version }}/venv/bin/python manage.py billing_event_consumer --limit=10 --settings=config.settings.production
user=www-data
numprocs=1
stdout_logfile={{ log_dir }}/sqs_worker.log
stderr_logfile={{ log_dir }}/sqs_worker_error.log
autostart=true
autorestart=true
startsecs=0
killasgroup=true
