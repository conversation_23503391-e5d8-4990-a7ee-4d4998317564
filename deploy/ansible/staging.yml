---
- hosts: group_account
  remote_user: ubuntu
  become: True
  become_method: sudo
  become_user: root
  vars:
    ansible_python_interpreter: /usr/bin/python3.10
    app_name: account_api_v2
    app_version: current
    app_version_name: "{{ (app_version == 'current')|ternary(app_name, app_name+'_'+app_version) }}"
    git_repo:
    git_branch:
    project_base: "/var/app/python/{{ app_name }}"
    deploy_name: "{{ ansible_date_time.year }}{{ ansible_date_time.month }}{{ ansible_date_time.day}}{{ ansible_date_time.hour }}"
    release_dir: "{{ project_base }}/releases/{{ deploy_name }}"
    pip_requirement_file: "{{ release_dir }}/requirements/production.txt"
    venv_dir: "{{ release_dir }}/venv"
    base_log_dir: "/var/log/app/{{ app_name }}"
    uwsgi_pid_file: "/run/uwsgi/{{ app_name }}.pid"
    log_dir: "{{ base_log_dir }}/{{ (app_version == 'current')|ternary('', app_version) }}"
  tasks:
    - name: Create directory
      file:
        path: "{{ release_dir }}"
        state: directory
        mode: 0755
        group: root
        owner: root
    - import_tasks: tasks/git.yml
      name: Git sync tasks
    - name: Install virtualenv and install requirements
      pip:
        requirements: "{{ pip_requirement_file }}"
        virtualenv: "{{ venv_dir }}"
        virtualenv_command: /usr/bin/python3.10 -m venv
        extra_args: "--trusted-host pip.voicetree.biz --extra-index-url http://pip.voicetree.biz:8090"
    - name: copying ENV file
      copy:
        src: .env
        dest: "{{ project_base }}/releases/{{ deploy_name }}/.env"
    - name: Create log path
      file:
        path: "{{ log_dir }}"
        state: directory
        mode: 0777
    - name: Create supervisor log path
      file:
        path: "{{ log_dir }}/supervisor"
        state: directory
        mode: 0777
    - name: Create uWSGI ini file
      template:
        src: uwsgi.ini.j2
        dest: "{{ release_dir }}/deploy/ansible/uwsgi.ini"
    - name: Create supervisor file
      template:
        src: supervisor.conf.j2
        dest: "{{ release_dir }}/deploy/ansible/supervisor.conf"
    - name: Execute the release cleanup script
      ansible.builtin.shell: releases/{{ deploy_name }}/deploy/ansible/release-cleaning.sh
      register: script_output
      args:
        chdir: "{{ project_base }}"
    - name: Release cleaning script output
      ansible.builtin.debug:
        var: script_output.stdout_lines
    # Start symlinks
    - name: Create version symlink
      shell: ln -sfn "releases/{{ deploy_name }}" "{{ app_version }}"
      args:
        chdir: "{{ project_base }}"
    - name: Symlink uWSGI
      shell: ln -sfn "{{ project_base }}/{{ app_version }}/deploy/ansible/uwsgi.ini" "{{ app_version_name }}.ini"
      args:
        chdir: /etc/uwsgi/sites
    - name: Symlink supervisor
      shell: ln -sfn "{{ project_base }}/{{ app_version }}/deploy/ansible/supervisor.conf" "{{ app_version_name }}.conf"
      args:
        chdir: /etc/supervisor/conf.d
    - name: Restart uWSGI
      shell: "touch {{ project_base }}/{{ app_version }}/deploy/reloadFile"
    - name: Restart supervisor
      shell: "supervisorctl reread; supervisorctl update; supervisorctl restart {{ app_version_name }}:"


