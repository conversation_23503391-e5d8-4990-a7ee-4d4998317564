# User Authentication

Authorization Header:
```
Authorization: Token <api_key>
```

A user can have multiple API keys and those keys can be used to verify the user authentication.

We need to store this in `api_keys` table and map them with `user_profiles` table.

Schema:
```
id : UUID
user_profile_id: FK(user_profiles)
name: VARCHAR(50)
api_key: VARCHAR(255)
status: int(1) 0 -> InActive, 1 -> Active
created: DateTime
modified: DateTime
```

## Imp. Notes:
1. Once is user is marked as inactive in db. All keys of that user should not work.
2. Key can be soft deleted at any time via DB/UI.

## Future Scope:
This is a database operation that means it might cause delay in api response, So in this scenerio a cache machanism can help to resove the issue.
