# Demo Account Deactivation 
Its a background process to deactivate the demo accounts whose expiry date is passed.

1. List all the demo accounts from services table which are expired
2. Set `services` status as inactive and `churn_date` as current time
3. Expire the `service_package`
4. Deactivate service from myoperator db
5. Delete data from memcache
6. Free service number in `service_number`
7. Call number system to free the demo number
8. Trigger `service_deactivated` event
