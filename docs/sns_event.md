# SNS Events

This document explains the body and attributes of a sns event.

## Message Attributes:-
Account triggers multiple sns events and each event contains some basic following message attributes.

| Name          |  Data Type    | Value               | Description            |
|---------------|---------------|---------------------|------------------------|
| uid           |  String       | uuid4               | UID of event           |
| version       |  String       | 0.1.1               | Version                |
| namespace     |  String       | accounts            | Namespace              |
| action        |  String       | service_activated   | Event                  |
| service_type  |  String       | heyo/myop           | Service Type           |
| etag          |  String       | Qwerty              | Etag                   |
| created_at    |  String       | 2022-09-12 11:04:49 | DateTime of event      |

## Message Body:-
Message body contains json encoded string
```{\"company_id\":\"abcdef\"}```

A basic sns message structure looks like following:-
```
{
  "Message": "{\"company_id\":\"abcdef\"}",
  "MessageAttributes": {
    "uid": {
      "StringValue": "<uuid4>",
      "DataType": "String"
    },
    "version": {
      "StringValue": "0.1.1",
      "DataType": "String"
    },
    "namespace": {
      "StringValue": "accounts",
      "DataType": "String"
    },
    "action": {
      "StringValue": "service_activated",
      "DataType": "String"
    },
	  "service_type": {
      "StringValue": "myop",
      "DataType": "String"
    },
    "etag": {
      "StringValue": "Qwerty",
      "DataType": "String"
    },
    "created_at": {
      "StringValue": "2022-09-12 11:04:49",
      "DataType": "String"
    }
  }
}
```

Following are the sns events that are triggered by accounts.
| S.No| Action                           |  Data                             |
|-----|----------------------------------|-----------------------------------|
|1.   | `service_activated`              | `company_id`                      |
|2.   | `service_deactivated`            | `company_id`                      |
|3.   | `service_terminated`             | `company_id`                      |
|4.   | `payment_failed`                 | `billing_account_id`,`payment_id` |
|5.   | `payment_succeeded`              | `billing_account_id`,`payment_id` |
|6.   | `upcoming_service_renewal`       | `ban`,`billing_account_id`        |
|7.   | `monthly_statement_generated`    | `ban`,`billing_account_id`        |
|8.   | `service_rental_pending`         | `ban`,`billing_account_id`        |
|9.   | `flagged_fraud`                  | `ban`,`billing_account_id`        |
