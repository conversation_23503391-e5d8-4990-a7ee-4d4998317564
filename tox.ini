[tox]
envlist =
    py310

skipsdist = true

[testenv]
tox_pyenv_fallback = False

deps =
    -r{toxinidir}/requirements/local.txt

commands = python -V
           pytest
passenv =
        PYTHON_VERSION

setenv =
        PYTHONPATH = {toxinidir}
        PYTHONDONTWRITEBYTECODE=1

[testenv:lint]
skip_install = true
commands =
    flake8
deps =
    flake8

[testenv:cover]
setenv =
    PYTHONPATH = {toxinidir}
deps =
    -r{toxinidir}/requirements/local.txt

commands =
    coverage run --branch --omit='.local/*' -m pytest
    coverage xml --omit='.local/*' {posargs:}
