import json
import os
import typing as t

from django.core.cache import cache
from django.db import connection
from django.test import RequestFactory

from moto import mock_aws
import pytest
import responses as responses_
from rest_framework import status

from accounts.utils.api_services.otp import OTPApiService
from accounts.utils.api_services.surepass import SurepassApi

import boto3
from moto.core.models import override_responses_real_send


@pytest.fixture
def load_json():
    """Pass path from the folder accounts/tests/fixtures"""

    def wrap(path):
        with open(os.path.join(os.getcwd(), f"{path}")) as f:
            return json.load(f)

    return wrap


@pytest.fixture(autouse=True)
def media_storage(settings, tmpdir):
    settings.MEDIA_ROOT = tmpdir.strpath


@pytest.fixture
def request_factory() -> RequestFactory:
    return RequestFactory()


@pytest.fixture(scope="session", autouse=True)
def django_db_setup(django_db_blocker):
    with django_db_blocker.unblock():
        from django.apps import apps

        models_list = apps.get_models()
        for model in models_list:
            with connection.schema_editor() as schema_editor:
                schema_editor.create_model(model)

                if (
                    model._meta.db_table
                    not in connection.introspection.table_names()
                ):
                    raise ValueError(
                        "Table `{table_name}` is missing in test database.".format(
                            table_name=model._meta.db_table
                        )
                    )

        yield
        for model in models_list:
            with connection.schema_editor() as schema_editor:
                schema_editor.delete_model(model)


@pytest.fixture
def responses() -> t.Generator["responses_.RequestsMock", None, None]:
    with responses_.RequestsMock(assert_all_requests_are_fired=False) as res:
        override_responses_real_send(res)
        yield res
        override_responses_real_send(None)


@pytest.fixture(autouse=True)
def cache_clean():
    cache.clear()
    yield


@pytest.fixture
def mock_smsg_api(responses, load_json):
    def wrap(
        expected_response: t.Union[t.Dict, Exception, None] = None,
        status_code=status.HTTP_200_OK,
    ):
        expected_response = expected_response or load_json(
            "accounts/tests/fixtures/send_msg.json"
        )
        if isinstance(expected_response, Exception):
            return responses.add(
                responses.POST,
                OTPApiService().get_url(),
                body=expected_response,
                status=status_code,
            )
        return responses.add(
            responses.POST,
            OTPApiService().get_url(),
            json=expected_response,
            status=status_code,
        )

    yield wrap


@pytest.fixture
def mock_gst_detail_api(responses, load_json):
    def wrap(expected_response: t.Dict = None, status_code=status.HTTP_200_OK):
        expected_response = expected_response or load_json("gst_details.json")
        return responses.add(
            responses.POST,
            f"{SurepassApi().HOST}/api/v1/corporate/gstin-advanced",
            json=expected_response,
            status=status_code,
        )

    yield wrap


@pytest.fixture
def mock_aadhaar_send_otp_api(responses):
    def wrap(expected_response: t.Dict, status_code: int = status.HTTP_200_OK):
        return responses.add(
            responses.POST,
            f"{SurepassApi().HOST}/api/v1/aadhaar-v2/generate-otp",
            json=expected_response,
            status=status_code,
        )

    yield wrap


@pytest.fixture
def mock_aadhaar_verify_otp_api(responses):
    def wrap(expected_response: t.Dict, status_code: int = status.HTTP_200_OK):
        return responses.add(
            responses.POST,
            f"{SurepassApi().HOST}/api/v1/aadhaar-v2/submit-otp",
            json=expected_response,
            status=status_code,
        )

    yield wrap


@pytest.fixture
def mock_aadhaar_download_pdf_api(responses, load_json):
    def wrap(
        expected_response: t.Optional[t.Dict[str, t.Any]] = None,
        status_code: int = status.HTTP_200_OK,
    ):
        expected_response = expected_response or load_json(
            "accounts/kyc/tests/fixtures/aadhaar_download_pdf_api_200_success_response.json"
        )
        return responses.add(
            responses.POST,
            f"{SurepassApi().HOST}/api/v1/aadhaar-v2/generate-pdf",
            json=expected_response,
            status=status_code,
        )

    yield wrap


@pytest.fixture
def mock_surepass_api_gst_pdf(responses, load_json):
    def wrap(
        expected_response: t.Optional[t.Dict[str, t.Any]] = None,
        status_code: int = status.HTTP_200_OK,
    ):
        expected_response = expected_response or load_json(
            "accounts/kyc/tests/fixtures/gst_pdf_success.json"
        )
        return responses.add(
            responses.POST,
            f"{SurepassApi().HOST}/api/v1/corporate/gstin-pdf-report",
            json=expected_response,
            status=status_code,
        )

    yield wrap


@pytest.fixture
def mock_s3_bucket_download(responses, load_json, settings):
    def wrap(
        url,
        status_code: int = status.HTTP_200_OK,
    ):
        return responses.add(
            responses.GET,
            url,
            body=b"test content",
            status=status_code,
            content_type="application/pdf",
        )

    yield wrap


@pytest.fixture
def mock_s3_bucket(settings):
    """Create a mock S3 bucket."""
    with mock_aws():
        s3 = boto3.client("s3", region_name=settings.AWS_DEFAULT_REGION)
        s3.create_bucket(
            Bucket=settings.AWS_STORAGE_BUCKET_NAME,
            CreateBucketConfiguration={
                "LocationConstraint": settings.AWS_DEFAULT_REGION
            },
        )
        yield s3
