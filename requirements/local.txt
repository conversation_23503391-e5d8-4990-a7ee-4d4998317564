-r ./base.txt

# Werkzeug==0.16.0 # https://github.com/pallets/werkzeug
# ipdb==0.12.3  # https://github.com/gotcha/ipdb
# Sphinx==2.3.0  # https://github.com/sphinx-doc/sphinx

# Testing
# ------------------------------------------------------------------------------
mypy==1.4.*  # https://github.com/python/mypy
pytest==7.*  # https://github.com/pytest-dev/pytest
pytest-sugar==1.0  # https://github.com/Frozenball/pytest-sugar
pytest-env==0.*  # https://github.com/MobileDynasty/pytest-env
responses==0.25.3
freezegun==1.5.0

# Code quality
# ------------------------------------------------------------------------------
flake8==5.0.4  # https://github.com/PyCQA/flake8
coverage==7.*  # https://github.com/nedbat/coveragepy
pytest-cov==4.*
black==22.*  # https://github.com/ambv/black
isort==6.0.*  # https://pypi.org/project/isort/
pytest-django==4.9.*  # https://github.com/pytest-dev/pytest-django
pre-commit==2.17.0  # https://github.com/pre-commit/pre-commit

# Django
# ------------------------------------------------------------------------------
factory-boy==3.*  # https://github.com/FactoryBoy/factory_boy
django-coverage-plugin==1.6.0  # https://github.com/nedbat/django_coverage_plugin
pytest-django==4.9.*  # https://github.com/pytest-dev/pytest-django
django-stubs==1.*  # to support LSP with django models

# Celery
# ------------------------------------------------------------------------------

# Cache
fakeredis[lua]==2.29.*  # https://pypi.org/project/fakeredis/

moto[s3]==5.*
