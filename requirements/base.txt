pytz==2019.3  # https://github.com/stub42/pytz
redis==4.*  # https://github.com/antirez/redis
boto3==1.20.24
phonenumbers==8.13.55

# Django
# ------------------------------------------------------------------------------
django==3.2.18  # pyup: < 3.0  # https://www.djangoproject.com/
django-environ==0.4.5  # https://github.com/joke2k/django-environ
django-model-utils==4.0.0  # https://github.com/jazzband/django-model-utils
django-redis==5.4.0  # https://github.com/niwinz/django-redis
mysqlclient==2.1.*
django-filter==2.4.0

# Django REST Framework
# ------------------------------------------------------------------------------
djangorestframework==3.14.0  # https://github.com/encode/django-rest-framework
PyYAML==5.2  # https://github.com/yaml/pyyaml
coreapi==2.3.3  # https://github.com/core-api/python-client

# Request Logging
# ------------------------------------------------------------------------------
django-request-logging==0.7.5  # https://github.com/Rhumbix/django-request-logging

# MyOperator Packages
# ------------------------------------------------------------------------------
--trusted-host pip.voicetree.biz
--extra-index-url http://pip.voicetree.biz:8090

centrallog==3.0.2  # https://gitlab.com/myoperator-python/myoperator-packages/central-log

# Celery
# ------------------------------------------------------------------------------
celery==5.3.6
django-celery-results==2.5.1
# django-celery-beat==2.6.0

# Fix: botocore 1.23.54 has requirement urllib3<1.27,>=1.25.4,
# but you'll have urllib3 2.0.2 which is incompatible.
urllib3<2
requests==2.32.3
drf-flex-fields==1.*

python-statemachine==2.5.0  # https://pypi.org/project/python-statemachine/