import os
import sys

import environ
from celery import Celery, Task
from celery.signals import setup_logging, task_prerun
from django.conf import settings

from myoperator.centrallog.config import configure as config_logger
from myoperator.centrallog.config import get_config
from celery.schedules import crontab

# This allows easy placement of apps within the interior
# accounts directory.
app_path = os.path.abspath(
    os.path.join(os.path.dirname(os.path.abspath(__file__)), os.pardir)
)
sys.path.append(os.path.join(app_path, "accounts"))

env = environ.Env()
# OS environment variables take precedence over variables from .env
env.read_env(os.path.join(app_path, ".env"))

# set the default Django settings module for the 'celery' program.
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings.local")


class MyTask(Task):
    # Custom task class to set task_id of the task
    def apply_async(self, *args, **kwargs):

        if not kwargs.get("task_id"):
            config_logger()

        kwargs["task_id"] = kwargs.get("task_id") or str(get_config().uid)
        return super().apply_async(*args, **kwargs)


app = Celery("accounts", task_cls=MyTask)

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
# - namespace='CELERY' means all celery-related configuration keys
#   should have a `CELERY_` prefix.
app.config_from_object("django.conf:settings", namespace="CELERY")


@setup_logging.connect
def config_loggers(*args, **kwargs):
    from logging.config import dictConfig

    dictConfig(settings.LOGGING)


@task_prerun.connect
def set_task_logger_uid(task_id, task, *args, **kwargs):
    # this will set log uid of the worker, running the task
    config_logger(uid=task_id)


# Load task modules from all registered Django app configs.
app.autodiscover_tasks()
