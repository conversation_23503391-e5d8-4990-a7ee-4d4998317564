from myoperator.centrallog.formatter import CentralFormatter
from .base import *  # noqa
from .base import env, logging_fmt

# ------------------------------------------------------------------------------
# GENERAL
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#debug
DEBUG = True
# https://docs.djangoproject.com/en/dev/ref/settings/#secret-key
SECRET_KEY = env(
    "DJANGO_SECRET_KEY",
    default="6VOWCOd7GuesP9vrOredj5mYKwBZN2MbA9hqbIwuiBP5MgWTnt5NcMSGZSLRh0by",
)
# https://docs.djangoproject.com/en/dev/ref/settings/#allowed-hosts
ALLOWED_HOSTS = env.list(
    "DJANGO_ALLOWED_HOSTS",
    default=[
        "localhost",
        "0.0.0.0",
        "127.0.0.1",
        "example.com",
    ],
)

# ------------------------------------------------------------------------------
# FIXTURES
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#fixture-dirs
FIXTURE_DIRS = (str(APPS_DIR.path("fixtures")),)


# ------------------------------------------------------------------------------
# LOGGING
# ------------------------------------------------------------------------------
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "myoperator": {"()": CentralFormatter, "format": logging_fmt}
    },
    "handlers": {
        "console": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "formatter": "myoperator",
        }
    },
    "root": {"level": "INFO", "handlers": ["console"]},
}

# Your stuff...
# ------------------------------------------------------------------------------
