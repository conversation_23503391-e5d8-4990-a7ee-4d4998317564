from django.urls import include, path
from django.views import defaults as default_views

urlpatterns = [
    # Domain Root
    path(
        "",
        default_views.permission_denied,
        kwargs={"exception": Exception("Permission Denied")},
        name="home",
    ),
    path("core/", include("accounts.core.urls", namespace="core")),
    path("users/", include("accounts.users.urls", namespace="users")),
    path("services/", include("accounts.services.urls", namespace="services")),
    path("packages/", include("accounts.packages.urls", namespace="packages")),
    path("products/", include("accounts.products.urls", namespace="products")),
    path(
        "workflows/", include("accounts.workflows.urls", namespace="workflows")
    ),
    path("offers/", include("accounts.offers.urls", namespace="offers")),
    path(
        "billing_accounts/",
        include("accounts.billing_accounts.urls", namespace="billing_accounts"),
    ),
    path(
        "verification/",
        include("accounts.verification.urls", namespace="verification"),
    ),
    path(
        "global_packages/",
        include("accounts.global_packages.urls", namespace="global_packages"),
    ),
    path(
        "payments/",
        include("accounts.payments.urls", namespace="payments"),
    ),
    path(
        "kyc/",
        include("accounts.kyc.urls", namespace="kyc"),
    ),
]
