from collections import OrderedDict
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from accounts import error_codes


class My<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(JSONRenderer):

    default_empty_message = ""

    def render(self, data, accepted_media_type=None, renderer_context=None):
        """
        Render `data` into JSON, returning a bytestring.
        """

        response = renderer_context.get("response")
        status_code = 200
        if response and response.status_code:
            status_code = response.status_code

        code = data.get("code", status_code)
        status = self._get_status(status_code)
        message = data.get("message", self._get_message(code))
        res_dict = OrderedDict(
            status=status, code=code, message=message, pagination={}, data={}, errors={}
        )
        if status == "success":
            del res_dict["errors"]
            if "data" in data:
                res_dict["data"] = data.get("data")
        elif status == "error":
            del res_dict["data"]
            if "errors" in data:
                res_dict["errors"] = data.get("errors")

        if "pagination" not in data:
            del res_dict["pagination"]
        else:
            res_dict["pagination"] = data["pagination"]

        return super().render(res_dict, accepted_media_type, renderer_context)

    def _get_status(self, status_code):
        status = "success"
        if int(status_code / 100) in (4, 5):
            status = "error"
        return status

    def _get_message(self, code):
        errors = dict(error_codes.ERRORS)
        return errors.get(code, self.default_empty_message)
