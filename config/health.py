from django.conf import settings
from django.http import HttpResponse


class HealthCheckMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # setup healthchecks
        return self._setup_healthcheck(request)

    def _setup_healthcheck(self, request):
        health_settings = getattr(
            settings,
            "MYOP_HEALTH_CHECK",
            {
                "PATH": "/_health",
                "RETURN_STATUS_CODE": 200,
                "RETURN_BYTE_DATA": "",
            },
        )
        if request.path == health_settings["PATH"]:
            response = HttpResponse(health_settings["RETURN_BYTE_DATA"])
            response.status_code = health_settings["RETURN_STATUS_CODE"]
            return response
        return self.get_response(request)
