import typing as t
from django.db import models
from . import constants
from .queryset import BillingAccountQuerySet
from accounts.billing_accounts.enums import (
    BillingStatusEnum,
    BillingAccountCreditStatusEnum,
    DiscountBucketStatusEnum,
    ContactStatusEnums,
)
from decimal import Decimal
from django.db.models.functions import Coalesce
from django.db.models import DecimalField

from .queryset import BillingAccountCreditQuerySet

if t.TYPE_CHECKING:
    from .models import BillingAccounts, BillingAccountCredits


class BillingAccountManager(models.Manager):
    def get_queryset(self):
        return BillingAccountQuerySet(self.model, using=self._db)

    def ban(self, ac_number):
        return self.get_queryset().ban(ac_number)

    def pilot_ban(self, parent_id):
        return self.get_queryset().pilot_ban(parent_id)

    def parent_ban(self, parent_id):
        return self.get_queryset().parent_ban(parent_id)


class ActiveBillingAccountManager(BillingAccountManager):
    def get_queryset(self):
        return (
            super().get_queryset().filter(status=BillingStatusEnum.ACTIVE.value)
        )


class BillingAccountCreditManager(models.Manager):
    def get_queryset(self):
        return BillingAccountCreditQuerySet(self.model, using=self._db)

    def billing_account(self, billing_account: "BillingAccounts"):
        return self.get_queryset().billing_account(billing_account)

    def get_credit_object(
        self, billing_account: "BillingAccounts"
    ) -> t.Union["BillingAccountCredits", None]:
        return (
            self.get_queryset()
            .filter(
                billing_account=billing_account,
                status=BillingAccountCreditStatusEnum.ACTIVE.value,
            )
            .first()
        )

    def credit_amount(self) -> Decimal:
        return self.get_queryset().credit_amount()


class ServiceContactsManager(models.Manager):
    def get_queryset(self) -> models.QuerySet:
        return super().get_queryset()

    def active_contact_exists(
        self,
        billing_account_id: int,
        country_code: str,
        mobile: str,
        email: str,
        exclude_id: t.Optional[int] = None,
    ) -> bool:
        """Checks if an active contact with the same mobile and email exists."""
        queryset = self.get_queryset().filter(
            billing_account_id=billing_account_id,
            country_code=country_code,
            mobile=mobile,
            email=email,
            status=ContactStatusEnums.ACTIVE.value,
        )
        if exclude_id:
            queryset = queryset.exclude(id=exclude_id)

        return queryset.exists()
