from accounts.exceptions import BaseException
from accounts import error_codes
from rest_framework import status


class InvalidBillingAccountException(BaseException):
    message = "Invalid billing account id"


class FraudBillingAccountException(BaseException):
    error_code = error_codes.FRAUD_BILLING_ACCOUNT
    message = "Account is marked as fraud"


class AccountActivationFailedException(BaseException):
    message = "Account Activation Failed"


class AlreadyFraudMarkedException(BaseException):
    http_status_code = status.HTTP_400_BAD_REQUEST
    error_code = error_codes.ALREADY_MARKED_AS_FRAUD
    message = "Account is alreday marked as fraud"


class BillingAccountCreditNotFoundException(BaseException):
    http_status_code = status.HTTP_404_NOT_FOUND
    message = "No active Billing Account Credit found"


class GstUpdationFailedException(BaseException):
    message = "GST updation failed"


class InvalidGstNumberException(GstUpdationFailedException):
    http_status_code = status.HTTP_400_BAD_REQUEST
    message = "Invalid GSTIN number"


class DocTypeNotFoundException(BaseException):
    message = "Doc type not found"
