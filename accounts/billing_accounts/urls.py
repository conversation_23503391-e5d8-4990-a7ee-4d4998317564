from django.urls import path

from accounts.billing_accounts.views import (
    AccountReactivationProcessView,
    MarkAccountFraudAPIView,
    BillingAPIView,
    GstUpdateAPIView,
    BillingAccountListAPIView,
    UpdateCreditLimitView,
    ContactListCreateView,
    ContactUpdateView,
)

app_name = "billing_accounts"

urlpatterns = [
    path(
        "",
        view=BillingAccountListAPIView.as_view(),
        name="billing_account_list",
    ),
    path(
        "event/account_reactivation",
        view=AccountReactivationProcessView.as_view(),
        name="process",
    ),
    path(
        "<str:billing_account_id>/mark_fraud",
        view=MarkAccountFraudAPIView.as_view(),
        name="mark_fraud",
    ),
    path(
        "<str:id>",
        view=BillingAPIView.as_view(),
        name="retrieve_update",
    ),
    path(
        "<str:id>/gst",
        view=GstUpdateAPIView.as_view(),
        name="update_gst",
    ),
    path(
        "event/update_credit_limit",
        view=UpdateCreditLimitView.as_view(),
        name="process",
    ),
    path(
        "<str:billing_account_id>/contacts",
        view=ContactListCreateView.as_view(),
        name="contact_list_create",
    ),
    path(
        "<str:billing_account_id>/contacts/<str:pk>",
        view=ContactUpdateView.as_view(),
        name="contact_update",
    ),
]
