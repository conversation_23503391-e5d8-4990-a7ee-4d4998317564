from accounts.billing_accounts.models import ServiceContacts
from django_filters import rest_framework as django_filters
from django_filters import rest_framework as filters
from accounts.billing_accounts.enums import ContactStatusEnums


class ContactsFilter(django_filters.FilterSet):
    status = filters.CharFilter(method="filter_by_status")
    name = filters.CharFilter(lookup_expr="icontains")
    mobile = filters.CharFilter(lookup_expr="exact")

    def filter_by_status(self, queryset, name, value):
        status = ContactStatusEnums.dict().get(value.upper())
        return queryset.filter(status=status)

    class Meta:
        model = ServiceContacts
        fields = ["name", "mobile"]
