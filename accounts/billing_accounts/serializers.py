from rest_framework import serializers
from accounts.billing_accounts.models import BillingAccounts, ServiceContacts
from accounts.products.models import OrgTypes
from accounts.core.models import StateCodes
from accounts.billing_accounts.utils.billing_account import (
    is_valid_gst_no,
    update_gstin,
)
from accounts.users.models import UserProfiles
from django.core.validators import RegexValidator
from accounts.billing_accounts.constants import (
    BILLING_STATUS_ACTIVE,
    BILLING_STATUS_INACTIVE,
)
from django.urls import reverse
from accounts.billing_accounts.enums import (
    ContactTypeEnums,
    ContactStatusEnums,
    BillingStatusEnum,
    BillingVerificationStateEnum,
)
import phonenumbers

pan_validator = RegexValidator(
    r"^[A-Z]{5}[0-9]{4}[A-Z]{1}$",
    "Enter a valid PAN card number. The format is **********.",
)


class MarkFraudSerializer(serializers.Serializer):
    user_email = serializers.EmailField(required=False, default=None)

    def validate_user_email(self, value):
        if value and not UserProfiles.objects.filter(email=value).exists():
            raise serializers.ValidationError(
                f"User doesn't exist with {value} email"
            )
        return value


class BillingSerializer(serializers.ModelSerializer):
    business_name = serializers.CharField(
        max_length=250,
        validators=[
            RegexValidator(
                r"^[0-9a-zA-Z., &\-\_']*$",
                "Only alphanumeric and ., &-_' characters are allowed.",
            )
        ],
    )
    org_type_id = serializers.PrimaryKeyRelatedField(
        source="org_type", queryset=OrgTypes.objects.all()
    )
    parent_id = serializers.PrimaryKeyRelatedField(
        read_only=True, source="parent"
    )
    business_pan = serializers.CharField(
        validators=[pan_validator], allow_null=True, allow_blank=True
    )
    account_manager_id = serializers.PrimaryKeyRelatedField(
        source="account_manager", queryset=UserProfiles.objects.all()
    )
    discount_id = serializers.PrimaryKeyRelatedField(
        read_only=True, source="discount"
    )

    class Meta:
        model = BillingAccounts
        read_only_fields = (
            "ac_number",
            "gst_no",
            "billing_day",
            "applied_period",
            "created",
            "modified",
        )
        fields = (
            "id",
            "parent_id",
            "ac_number",
            "state_id",
            "org_type_id",
            "gst_no",
            "tan_no",
            "uan",
            "business_name",
            "billing_day",
            "credit_limit",
            "min_bal",
            "recharge_on_min_bal",
            "auto_bill_email",
            "auto_bill_sms",
            "cr_limit_email",
            "cr_limit_sms",
            "business_pan",
            "business_address",
            "business_state",
            "business_city",
            "business_pincode",
            "business_country",
            "business_type",
            "billing_property",
            "account_manager_id",
            "discount_id",
            "applied_period",
            "verification_state",
            "status",
            "created",
            "modified",
        )

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["status"] = BillingStatusEnum.get_name(
            instance.status
        ).lower()
        representation[
            "verification_state"
        ] = BillingVerificationStateEnum.get_name(
            instance.verification_state
        ).lower()
        return representation

    def to_internal_value(self, data):
        if "status" in data:
            if data["status"].upper() in BillingStatusEnum.keys():
                data["status"] = BillingStatusEnum.get_value(
                    data["status"].upper()
                )
        if "verification_state" in data:
            if (
                data["verification_state"].upper()
                in BillingVerificationStateEnum.keys()
            ):
                data[
                    "verification_state"
                ] = BillingVerificationStateEnum.get_value(
                    data["verification_state"].upper()
                )
        return super().to_internal_value(data)

    def validate_state_id(self, value):
        if self.instance.is_gst_added() and self.instance.state_id != value:
            raise serializers.ValidationError(
                "GSTIN updated, State can't be changed"
            )
        if (
            value
            and value != 0
            and not StateCodes.objects.filter(id=value).exists()
        ):
            raise serializers.ValidationError("Invalid State ID")
        return value

    def validate_business_pan(self, value):
        if self.instance.is_gst_added() and self.instance.business_pan != value:
            raise serializers.ValidationError(
                "GSTIN updated, PAN can't be changed"
            )
        return value

    def validate_business_name(self, value):
        if (
            self.instance.is_gst_added()
            and self.instance.business_name != value
        ):
            raise serializers.ValidationError(
                "GSTIN updated, Business Name can't be changed"
            )
        return value

    def validate_business_address(self, value):
        if (
            self.instance.is_gst_added()
            and self.instance.business_address != value
        ):
            raise serializers.ValidationError(
                "GSTIN updated, Address can't be changed"
            )
        return value

    def validate_business_city(self, value):
        if (
            self.instance.is_gst_added()
            and self.instance.business_city != value
        ):
            raise serializers.ValidationError(
                "GSTIN updated, City can't be changed"
            )
        return value

    def validate_business_pincode(self, value):
        if (
            self.instance.is_gst_added()
            and self.instance.business_pincode != value
        ):
            raise serializers.ValidationError(
                "GSTIN updated, Pincode can't be changed"
            )
        return value

    def validate_business_country(self, value):
        if (
            self.instance.is_gst_added()
            and self.instance.business_country != value
        ):
            raise serializers.ValidationError(
                "GSTIN updated, Country can't be changed"
            )
        return value

    def validate_verification_state(self, value):
        if value == BillingVerificationStateEnum.FRAUD.value:
            fraud_api_url = reverse(
                "billing_accounts:mark_fraud",
                kwargs={"billing_account_id": ":id"},
            )
            raise serializers.ValidationError(
                f"Unable to set verification state to 'fraud', Please use the {fraud_api_url} API."
            )
        return value


class SearchFilterSerializer(serializers.Serializer):

    STATUS_CHOICES = [
        (BILLING_STATUS_ACTIVE, "active"),
        (BILLING_STATUS_INACTIVE, "inactive"),
    ]

    sort = serializers.ChoiceField(
        required=False,
        choices=["ac_number", "business_name", "status", "created"],
        default="created",
    )
    order = serializers.ChoiceField(
        required=False, choices=["asc", "desc"], default="asc"
    )
    ac_number = serializers.CharField(
        required=False, max_length=6, default=None
    )
    business_name = serializers.CharField(
        required=False, max_length=250, default=None
    )
    status = serializers.ChoiceField(
        required=False,
        choices=STATUS_CHOICES,
        default=None,
    )

    def to_internal_value(self, data):
        duplicated_data = data.copy()
        if "status" in duplicated_data and duplicated_data["status"] in [
            "active",
            "inactive",
        ]:
            duplicated_data["status"] = (
                1 if duplicated_data["status"] == "active" else 0
            )

        return super().to_internal_value(duplicated_data)


class GstUpdateSerializer(serializers.ModelSerializer):
    gst_no = serializers.CharField(max_length=15)

    class Meta:
        model = BillingAccounts
        fields = ["gst_no"]

    def validate_gst_no(self, value):
        if not is_valid_gst_no(value):
            raise serializers.ValidationError("Invalid GSTIN number.")
        return value

    def update(self, instance, validated_data):
        update_gstin(instance, validated_data["gst_no"])
        return instance


class ContactSerializer(serializers.ModelSerializer):
    mobile = serializers.CharField(min_length=6, max_length=15)
    status = serializers.ChoiceField(
        choices=ContactStatusEnums.values(),
        default=ContactStatusEnums.ACTIVE.value,
    )
    email = serializers.EmailField()
    name = serializers.CharField(min_length=3, max_length=50)
    country_code = serializers.CharField(
        max_length=5,
        validators=[
            RegexValidator(
                regex=r"^\+\d+$",
                message="Country code must start with '+' followed by digits.",
            )
        ],
    )
    address = serializers.CharField()
    contact_type = serializers.ListField(
        child=serializers.CharField(), write_only=True
    )

    class Meta:
        model = ServiceContacts
        fields = [
            "id",
            "name",
            "country_code",
            "mobile",
            "email",
            "address",
            "contact_type",
            "status",
            "created",
            "modified",
        ]

    def validate_contact_type(self, contact_type_list):
        """Validates and converts a list of contact type strings into an integer value."""
        contact_type_value = 0
        for contact_type in contact_type_list:
            contact_enum = ContactTypeEnums.get_value(contact_type.upper())
            if contact_enum is None:
                raise serializers.ValidationError(
                    f"Invalid contact type: {contact_type}"
                )
            contact_type_value |= (
                contact_enum  # Bitwise OR to accumulate values
            )

        return contact_type_value

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["status"] = ContactStatusEnums.get_name(
            instance.status
        ).lower()

        representation["contact_type"] = instance.get_contact_type_names()

        return representation

    def to_internal_value(self, data):
        # Convert status string to corresponding enum value
        status = data.get("status")
        if status:
            data["status"] = ContactStatusEnums.get_value(status.upper())

        return super().to_internal_value(data)

    def validate(self, data):
        """
        Validates the contact details:
            - Ensures no duplicate active contact exists for the same mobile and email.
            - Checks if the mobile number is valid.
            - Prevents deactivating the last active contact.
        """
        mobile = data.get("mobile")
        status = data.get("status")
        email = data.get("email")
        billing_account_id = self.context.get("billing_account_id")
        country_code = data.get("country_code")
        instance = self.instance  # update case

        # Check if an active contact with the same mobile and email already exists
        if ServiceContacts.objects.active_contact_exists(
            billing_account_id=billing_account_id,
            country_code=country_code,
            mobile=mobile,
            email=email,
            exclude_id=instance.id if instance else None,
        ):
            raise serializers.ValidationError(
                "A contact with this mobile number and email already exists."
            )

        # mobile number validation
        if country_code and mobile:
            try:
                parsed_number = phonenumbers.parse(
                    f"+{country_code}{mobile}", None
                )
                if not phonenumbers.is_valid_number(parsed_number):
                    raise serializers.ValidationError("Invalid mobile number.")
            except phonenumbers.NumberParseException:
                raise serializers.ValidationError("Invalid mobile number.")

        # At least one contact must be active
        if (
            instance
            and instance.status == ContactStatusEnums.ACTIVE.value
            and status == ContactStatusEnums.INACTIVE.value
        ):
            active_contacts_count = (
                ServiceContacts.objects.filter(
                    billing_account_id=billing_account_id,
                    status=ContactStatusEnums.ACTIVE.value,
                )
                .exclude(id=instance.id)
                .count()
            )  # Exclude self in count

            if active_contacts_count == 0:
                raise serializers.ValidationError(
                    "At least one contact must be active."
                )

        return data

    def create(self, validated_data):
        validated_data["billing_account_id"] = self.context.get(
            "billing_account_id"
        )
        return super().create(validated_data)
