import logging
import typing as t
from decimal import Decimal

from django.core.exceptions import ObjectDoesNotExist
from django.db import models, transaction

from accounts.billing_accounts import constants
from accounts.billing_accounts.enums import (
    BillingStatusEnum,
    BillingVerificationStateEnum,
    ContactStatusEnums,
    ContactTypeEnums,
)
from accounts.billing_accounts.exceptions import DocTypeNotFoundException
from accounts.billing_accounts.managers import (
    ActiveBillingAccountManager,
    BillingAccountCreditManager,
    BillingAccountManager,
    ServiceContactsManager,
)
from accounts.core.models import DocTypes
from accounts.discounts.managers import ActiveDiscountBucketManager
from accounts.discounts.models import Discounts
from accounts.products.models import OrgTypes
from accounts.users.models import UserProfiles
from accounts.utils.common import uuid

from .enums import (
    BillingAccountCreditStatusEnum,
    BillingAccountCreditTransType,
    DiscountBucketApplyOnEnum,
    DocTypeEnums,
)

if t.TYPE_CHECKING:
    from accounts.kyc.models import KYC

logger = logging.getLogger(__name__)


class BillingAccounts(models.Model):
    id = models.CharField(max_length=36, primary_key=True, default=uuid)
    ac_number = models.CharField(max_length=6, unique=True)
    state_id = models.IntegerField(default=0)
    gst_no = models.CharField(max_length=20, null=True)
    uan = models.CharField(max_length=15, null=True)
    tan_no = models.CharField(max_length=15, null=True)
    business_name = models.CharField(max_length=250)
    business_address = models.TextField()
    org_type = models.ForeignKey(OrgTypes, on_delete=models.DO_NOTHING)
    billing_day = models.IntegerField(default=0)
    parent = models.ForeignKey(
        "self",
        on_delete=models.DO_NOTHING,
        blank=True,
        null=True,
        related_name="child",
    )
    credit_limit = models.IntegerField(default=0)
    min_bal = models.FloatField(default=0)
    recharge_on_min_bal = models.FloatField(default=0)
    auto_bill_email = models.SmallIntegerField(default=1)
    auto_bill_sms = models.SmallIntegerField(default=1)
    cr_limit_email = models.SmallIntegerField(default=1)
    cr_limit_sms = models.SmallIntegerField(default=1)
    business_pan = models.CharField(max_length=10, null=True)
    business_city = models.CharField(max_length=200)
    business_state = models.CharField(max_length=200)
    business_pincode = models.CharField(max_length=50)
    business_country = models.CharField(max_length=36)
    business_type = models.CharField(max_length=200, null=True)
    billing_property = models.IntegerField(
        help_text="1: misdial, 2: non-tollfree, 3: tollfree"
    )
    account_manager = models.ForeignKey(
        UserProfiles,
        on_delete=models.SET_NULL,
        db_column="account_manager",
        null=True,
        default=None,
    )
    discount = models.ForeignKey(
        Discounts, on_delete=models.SET_NULL, null=True, default=None
    )
    applied_period = models.IntegerField(default=0)
    total_pending = models.DecimalField(
        default=0.00,
        help_text="total pending amount for a billing account in terms of amount in respect to currency",
        decimal_places=2,
        max_digits=10,
    )
    verification_state = models.SmallIntegerField(
        default=BillingVerificationStateEnum.UNVERIFIED.value,
        choices=BillingVerificationStateEnum.choices(),
    )
    status = models.IntegerField(
        choices=BillingStatusEnum.choices(),
        default=BillingStatusEnum.ACTIVE.value,
    )
    created = models.DateTimeField(auto_now_add=models.DateField())
    modified = models.DateTimeField(auto_now=models.DateField())

    objects = BillingAccountManager()
    active = ActiveBillingAccountManager()

    class Meta:
        db_table = "billing_accounts"
        managed = True
        indexes = [
            models.Index(fields=["parent_id"]),
            models.Index(fields=["org_type_id"]),
            models.Index(fields=["status"]),
            models.Index(fields=["billing_day"]),
            models.Index(fields=["account_manager"]),
        ]

    def is_fraud(self):
        """
        Check if the verification state of the billing is classified as fraud.

        Returns:
            bool: True if the verification state is classified as fraud, False otherwise.
        """
        return (
            self.verification_state == BillingVerificationStateEnum.FRAUD.value
        )

    def fraud(self):
        """
        Sets the verification state to 'fraud' and the status to 'inactive' for the current billing. Saves the changes.

        Parameters:
            None

        Returns:
            None
        """
        self.verification_state = constants.BILLING_VERIFICATION_STATE_FRAUD
        self.status = constants.BILLING_STATUS_INACTIVE
        self.save()

    def is_gst_added(self):
        """
        Check if gst_no is added (i.e. not null and not "NA" in db).

        Returns:
            bool: True if the gst_no is added, False otherwise.
        """
        if self.gst_no not in [None, "NA"]:
            return True
        return False

    def is_child_ban(self):
        return (
            self.parent_id is not None
            and self.parent_id != ""
            and self.parent_id != 0
        )

    def is_parent_ban(self):
        return self.child.exists()

    def parent_ban(self):
        """
        Returns the parent/corporate billing account if it exists, otherwise returns the current billing account.
        If the current billing account is a parent/corporate ban, it returns itself.
        Returns:
            tuple: A tuple containing the billing account and a boolean indicating if it is a parent/corporate ban.
        """
        if self.is_parent_ban():
            return self, True
        elif self.is_child_ban():
            return self.parent, True
        return self, False

    @classmethod
    def get_by_id(
        cls, billing_account_id: str
    ) -> t.Optional["BillingAccounts"]:
        try:
            return cls.objects.get(id=billing_account_id)
        except ObjectDoesNotExist:
            return None

    def get_docs(self):
        return self.billingaccountdocs_set.all()  # type: ignore


class BillingAccountCredits(models.Model):
    id = models.CharField(primary_key=True, max_length=36, default=uuid)
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE
    )
    credit_amount = models.DecimalField(
        max_digits=10, decimal_places=2, default=0.00
    )
    trans_type = models.CharField(max_length=10)
    description = models.CharField(max_length=250, null=True)
    status = models.SmallIntegerField(
        default=BillingAccountCreditStatusEnum.ACTIVE.value
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()
    entries = BillingAccountCreditManager()

    class Meta:
        db_table = "billing_account_credits"
        indexes = [
            models.Index(fields=["status"]),
            models.Index(fields=["billing_account_id", "status"]),
        ]

    @classmethod
    def add_credit_amount(
        cls, billing_account: BillingAccounts, amount: Decimal, description: str
    ) -> Decimal:
        billing_credit = cls.entries.get_credit_object(
            billing_account=billing_account
        )

        if billing_credit:
            credit_amt = Decimal(billing_credit.credit_amount) + Decimal(amount)
            # disable the previous credit row and create a new one
            billing_credit.status = (
                BillingAccountCreditStatusEnum.INACTIVE.value
            )
            billing_credit.save()
        else:
            credit_amt = Decimal(amount)

        credit_obj = cls.objects.create(
            billing_account=billing_account,
            credit_amount=credit_amt,
            trans_type=BillingAccountCreditTransType.CREDIT.value,
            description=description,
        )
        return credit_obj.credit_amount

    @classmethod
    def deduct_credit_amount(
        cls,
        billing_account: BillingAccounts,
        amount: Decimal,
        description: str,
    ) -> Decimal:
        if billing_account.parent:
            logger.warning(
                "Expected parent billing id but child given in input"
            )
            billing_account = billing_account.parent

        billing_credit = cls.entries.get_credit_object(
            billing_account=billing_account
        )
        if not billing_credit:
            raise ValueError("No active credit found")

        billing_credit.status = BillingAccountCreditStatusEnum.INACTIVE.value
        billing_credit.save()

        # subtract amount from current amount
        credit_amt = Decimal(billing_credit.credit_amount) - Decimal(amount)

        credit_obj = cls.objects.create(
            billing_account=billing_account,
            credit_amount=credit_amt,
            trans_type=BillingAccountCreditTransType.DEBIT.value,
            description=description,
        )
        return credit_obj.credit_amount

    @classmethod
    def calc_outstanding_amount(cls, credit_amount: Decimal) -> Decimal:
        if credit_amount < 0:
            return abs(credit_amount)
        return Decimal(0)

    @classmethod
    def calc_advance_amount(cls, credit_amount: Decimal) -> Decimal:
        if credit_amount > 0:
            return credit_amount
        return Decimal(0)


class BillingAccountDocs(models.Model):
    id = models.CharField(primary_key=True, max_length=36, default=uuid)
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE
    )
    kyc = models.ForeignKey(
        "kyc.KYC", on_delete=models.CASCADE, default=None, null=True
    )
    doc_type = models.ForeignKey(DocTypes, on_delete=models.CASCADE)

    doc_number = models.CharField(max_length=200)
    doc_path = models.CharField(max_length=250)
    doc_name = models.CharField(max_length=250)
    doc_ext = models.CharField(max_length=20)
    status = models.SmallIntegerField(default=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "billing_account_docs"
        indexes = [
            models.Index(fields=["status"]),
            models.Index(fields=["billing_account_id", "status"]),
        ]

    @classmethod
    @transaction.atomic
    def create_doc(
        cls,
        billing_account: BillingAccounts,
        kyc: "KYC",
        url: str,
        doc_type_enum: DocTypeEnums,
        doc_name: str,
        doc_ext: str,
        doc_number: t.Optional[str] = None,
    ) -> "BillingAccountDocs":
        """Creates a new document.

        Args:
            billing_account (BillingAccounts): The billing account.
            kyc (KYC): The KYC.
            url (str): The URL of the document.
            doc_type_enum (DocTypeEnums): The type of document.
            doc_name (str): The name of the document without extension.
            doc_ext (str): The extension of the document.
            doc_number (t.Optional[str], optional): The number of the document. Defaults to None.

        Returns:
            BillingAccountDocs: The created document.
        """

        doc_type = DocTypes.get_doc_type_by_short_code(doc_type_enum)
        if not doc_type:
            logger.critical(
                f"Doc type not found for short code: {doc_type_enum.value}",
                exc_info=True,
            )
            raise DocTypeNotFoundException()
        return cls.objects.create(
            billing_account=billing_account,
            kyc=kyc,
            doc_type=doc_type,
            doc_number=doc_number,
            doc_path=url,
            doc_name=doc_name,
            doc_ext=doc_ext,
        )


class BillingAccountLogs(models.Model):
    id = models.AutoField(primary_key=True)
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE
    )
    state_id = models.CharField(max_length=20, null=True)
    gst_no = models.CharField(max_length=20, null=True)
    business_name = models.CharField(max_length=250, null=True)
    business_address = models.TextField()
    business_pan = models.CharField(max_length=20, null=True)
    business_country = models.CharField(max_length=250, null=True)
    business_state = models.CharField(max_length=250, null=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "billing_account_logs"
        indexes = [models.Index(fields=["billing_account_id"])]


class CreditAdditions(models.Model):
    id = models.CharField(primary_key=True, max_length=36, default=uuid)
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE
    )
    name = models.CharField(max_length=200)
    code = models.CharField(max_length=6, unique=True)
    value = models.DecimalField(max_digits=10, decimal_places=3, default=0.000)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "credit_additions"


class MonthlyStatements(models.Model):
    id = models.CharField(primary_key=True, max_length=36, default=uuid)
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE
    )
    advice_number = models.CharField(max_length=20)
    from_date = models.DateTimeField()
    to_date = models.DateTimeField()
    no_of_months = models.IntegerField()
    service_rent = models.DecimalField(max_digits=10, decimal_places=2)
    feature_rent_data = models.TextField(
        help_text="feature rent json data applied on each feature"
    )
    feature_usages_data = models.TextField(help_text="feature_usages json data")
    total_amount_data = models.TextField(
        help_text="total amount data which can be sum of rent amount, usages amount and total amount in term of json data"
    )
    payable_amount = models.DecimalField(
        max_digits=10, decimal_places=2, help_text="total amount"
    )
    opening_bal = models.DecimalField(
        max_digits=10, decimal_places=2, default=0.00
    )
    closing_bal = models.DecimalField(
        max_digits=10, decimal_places=2, default=0.00
    )
    type = models.CharField(max_length=20)
    pdf_url = models.CharField(max_length=250)
    payment_url = models.TextField()
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "monthly_statements"
        indexes = [
            models.Index(fields=["created"]),
            models.Index(fields=["billing_account_id", "type", "created"]),
        ]


class DiscountBuckets(models.Model):
    id = models.AutoField(primary_key=True)
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE
    )
    discount = models.ForeignKey(Discounts, on_delete=models.CASCADE)
    value = models.DecimalField(max_digits=10, decimal_places=3)
    apply_on = models.CharField(
        max_length=1,
        choices=DiscountBucketApplyOnEnum.choices(),
        help_text="R - apply on rent, A - apply on final amount. U - apply on usage (all will be applicable before tax)",
    )
    monthly_statement = models.ForeignKey(
        MonthlyStatements, on_delete=models.CASCADE
    )
    status = models.SmallIntegerField(default=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()
    active = ActiveDiscountBucketManager()

    class Meta:
        db_table = "discount_bucket"
        indexes = [
            models.Index(fields=["status"]),
            models.Index(fields=["billing_account_id", "status"]),
        ]

    def __str__(self):
        return str(self.discount.id)

    def discount_amount(self, amount: "Decimal") -> "Decimal":
        value = Decimal(self.value)
        if amount > value:
            return value
        elif amount < value:
            return amount


class ServiceContacts(models.Model):
    id = models.CharField(primary_key=True, max_length=36, default=uuid)
    billing_account = models.ForeignKey(
        BillingAccounts,
        on_delete=models.CASCADE,
    )

    contact_type = models.IntegerField(
        help_text="1 => billing, 2 => general, 4 => tech"
    )
    name = models.CharField(max_length=250)
    job_roll = models.CharField(max_length=150, null=True)
    dob = models.DateField(null=True)
    gender = models.CharField(
        max_length=6, choices=(("Male", "Male"), ("Female", "Female"))
    )
    address = models.TextField(null=True)
    city = models.CharField(max_length=200, null=True)
    state = models.CharField(max_length=200, null=True)
    pincode = models.IntegerField(null=True)
    landmark = models.CharField(max_length=250, null=True)
    country_code = models.CharField(max_length=20)
    mobile = models.BigIntegerField()
    phone = models.BigIntegerField(null=True)
    ext = models.IntegerField(null=True)
    email = models.CharField(max_length=250)
    aadhar_card_number = models.CharField(max_length=36, null=True)
    status = models.SmallIntegerField(default=ContactStatusEnums.ACTIVE.value)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = ServiceContactsManager()

    class Meta:
        db_table = "service_contacts"
        indexes = [
            models.Index(fields=["billing_account_id"]),
            models.Index(fields=["contact_type"]),
            models.Index(fields=["mobile"]),
            models.Index(fields=["email"]),
            models.Index(fields=["status"]),
        ]

    def inactive(self):
        """
        Sets the status to `inactive`. Saves the changes.
        Parameters:
            None

        Returns:
            None
        """
        self.status = ContactStatusEnums.INACTIVE.value
        self.save()

    def get_contact_type_names(self) -> list[str]:
        """Returns a list of contact type names based on the stored integer value."""
        return [
            enum.name.lower()
            for enum in ContactTypeEnums
            if self.contact_type & enum.value
        ]


class StatementReports(models.Model):
    id = models.CharField(primary_key=True, max_length=50, default=uuid)
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE
    )
    ban = models.CharField(max_length=6, null=True)
    business_name = models.CharField(max_length=250)
    product_name = models.CharField(max_length=250)
    advice_number = models.CharField(max_length=20)
    from_date = models.DateTimeField()
    to_date = models.DateTimeField()
    rent_amount = models.DecimalField(max_digits=10, decimal_places=3)
    discounted_rent_amount = models.DecimalField(
        max_digits=10, decimal_places=3, default=0.000
    )
    usage_amount = models.DecimalField(max_digits=10, decimal_places=3)
    usage_discount = models.DecimalField(
        max_digits=10, decimal_places=3, default=0.000
    )
    other_charges = models.DecimalField(max_digits=10, decimal_places=3)
    total_amount = models.DecimalField(max_digits=10, decimal_places=3)
    all_discount = models.DecimalField(
        max_digits=10, decimal_places=3, default=0.000
    )
    credit_addition = models.DecimalField(
        max_digits=10, decimal_places=3, default=0.000
    )
    pdf_url = models.CharField(max_length=250)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "statement_report"
