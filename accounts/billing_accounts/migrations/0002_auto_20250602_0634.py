# Generated by Django 3.2.18 on 2025-06-02 06:34

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('kyc', '0001_initial'),
        ('billing_accounts', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='billingaccountdocs',
            name='kyc',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='kyc.kyc'),
        ),
        migrations.AlterField(
            model_name='billingaccounts',
            name='status',
            field=models.IntegerField(choices=[(0, 'INACTIVE'), (1, 'ACTIVE')], default=1),
        ),
        migrations.AlterField(
            model_name='billingaccounts',
            name='verification_state',
            field=models.SmallIntegerField(choices=[(1, 'UNVERIFIED'), (2, 'SEMI_VERIFIED'), (3, 'VERIFIED'), (4, 'SPAM'), (5, 'FRAUD')], default=1),
        ),
    ]
