# Generated by Django 3.2.18 on 2025-03-19 11:54

import accounts.utils.common
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('users', '0001_initial'),
        ('discounts', '0001_initial'),
        ('core', '0001_initial'),
        ('products', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='BillingAccounts',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, max_length=36, primary_key=True, serialize=False)),
                ('ac_number', models.CharField(max_length=6, unique=True)),
                ('state_id', models.IntegerField(default=0)),
                ('gst_no', models.CharField(max_length=20, null=True)),
                ('uan', models.CharField(max_length=15, null=True)),
                ('tan_no', models.CharField(max_length=15, null=True)),
                ('business_name', models.CharField(max_length=250)),
                ('business_address', models.TextField()),
                ('billing_day', models.IntegerField(default=0)),
                ('credit_limit', models.IntegerField(default=0)),
                ('min_bal', models.FloatField(default=0)),
                ('recharge_on_min_bal', models.FloatField(default=0)),
                ('auto_bill_email', models.SmallIntegerField(default=1)),
                ('auto_bill_sms', models.SmallIntegerField(default=1)),
                ('cr_limit_email', models.SmallIntegerField(default=1)),
                ('cr_limit_sms', models.SmallIntegerField(default=1)),
                ('business_pan', models.CharField(max_length=10, null=True)),
                ('business_city', models.CharField(max_length=200)),
                ('business_state', models.CharField(max_length=200)),
                ('business_pincode', models.CharField(max_length=50)),
                ('business_country', models.CharField(max_length=36)),
                ('business_type', models.CharField(max_length=200, null=True)),
                ('billing_property', models.IntegerField(help_text='1: misdial, 2: non-tollfree, 3: tollfree')),
                ('applied_period', models.IntegerField(default=0)),
                ('total_pending', models.DecimalField(decimal_places=2, default=0.0, help_text='total pending amount for a billing account in terms of amount in respect to currency', max_digits=10)),
                ('verification_state', models.SmallIntegerField(choices=[(1, 'UNVERIFIED'), (2, 'SEMI_VERIFIED'), (3, 'VERIFIED'), (4, 'SPAM'), (5, 'FRAUD')], default=1)),
                ('status', models.IntegerField(choices=[(0, 'INACTIVE'), (1, 'ACTIVE')], default=1)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('account_manager', models.ForeignKey(db_column='account_manager', default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, to='users.userprofiles')),
                ('discount', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.SET_NULL, to='discounts.discounts')),
                ('org_type', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='products.orgtypes')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='child', to='billing_accounts.billingaccounts')),
            ],
            options={
                'db_table': 'billing_accounts',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='StatementReports',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, max_length=50, primary_key=True, serialize=False)),
                ('ban', models.CharField(max_length=6, null=True)),
                ('business_name', models.CharField(max_length=250)),
                ('product_name', models.CharField(max_length=250)),
                ('advice_number', models.CharField(max_length=20)),
                ('from_date', models.DateTimeField()),
                ('to_date', models.DateTimeField()),
                ('rent_amount', models.DecimalField(decimal_places=3, max_digits=10)),
                ('discounted_rent_amount', models.DecimalField(decimal_places=3, default=0.0, max_digits=10)),
                ('usage_amount', models.DecimalField(decimal_places=3, max_digits=10)),
                ('usage_discount', models.DecimalField(decimal_places=3, default=0.0, max_digits=10)),
                ('other_charges', models.DecimalField(decimal_places=3, max_digits=10)),
                ('total_amount', models.DecimalField(decimal_places=3, max_digits=10)),
                ('all_discount', models.DecimalField(decimal_places=3, default=0.0, max_digits=10)),
                ('credit_addition', models.DecimalField(decimal_places=3, default=0.0, max_digits=10)),
                ('pdf_url', models.CharField(max_length=250)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('billing_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.billingaccounts')),
            ],
            options={
                'db_table': 'statement_report',
            },
        ),
        migrations.CreateModel(
            name='ServiceContacts',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, max_length=36, primary_key=True, serialize=False)),
                ('contact_type', models.IntegerField(help_text='1 => billing, 2 => general, 4 => tech')),
                ('name', models.CharField(max_length=250)),
                ('job_roll', models.CharField(max_length=150, null=True)),
                ('dob', models.DateField(null=True)),
                ('gender', models.CharField(choices=[('Male', 'Male'), ('Female', 'Female')], max_length=6)),
                ('address', models.TextField(null=True)),
                ('city', models.CharField(max_length=200, null=True)),
                ('state', models.CharField(max_length=200, null=True)),
                ('pincode', models.IntegerField(null=True)),
                ('landmark', models.CharField(max_length=250, null=True)),
                ('country_code', models.CharField(max_length=20)),
                ('mobile', models.BigIntegerField()),
                ('phone', models.BigIntegerField(null=True)),
                ('ext', models.IntegerField(null=True)),
                ('email', models.CharField(max_length=250)),
                ('aadhar_card_number', models.CharField(max_length=36, null=True)),
                ('status', models.SmallIntegerField(default=1)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('billing_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.billingaccounts')),
            ],
            options={
                'db_table': 'service_contacts',
            },
        ),
        migrations.CreateModel(
            name='MonthlyStatements',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, max_length=36, primary_key=True, serialize=False)),
                ('advice_number', models.CharField(max_length=20)),
                ('from_date', models.DateTimeField()),
                ('to_date', models.DateTimeField()),
                ('no_of_months', models.IntegerField()),
                ('service_rent', models.DecimalField(decimal_places=2, max_digits=10)),
                ('feature_rent_data', models.TextField(help_text='feature rent json data applied on each feature')),
                ('feature_usages_data', models.TextField(help_text='feature_usages json data')),
                ('total_amount_data', models.TextField(help_text='total amount data which can be sum of rent amount, usages amount and total amount in term of json data')),
                ('payable_amount', models.DecimalField(decimal_places=2, help_text='total amount', max_digits=10)),
                ('opening_bal', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('closing_bal', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('type', models.CharField(max_length=20)),
                ('pdf_url', models.CharField(max_length=250)),
                ('payment_url', models.TextField()),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('billing_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.billingaccounts')),
            ],
            options={
                'db_table': 'monthly_statements',
            },
        ),
        migrations.CreateModel(
            name='DiscountBuckets',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('value', models.DecimalField(decimal_places=3, max_digits=10)),
                ('apply_on', models.CharField(choices=[('R', 'RENTAL'), ('U', 'USAGES'), ('A', 'ALL')], help_text='R - apply on rent, A - apply on final amount. U - apply on usage (all will be applicable before tax)', max_length=1)),
                ('status', models.SmallIntegerField(default=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('billing_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.billingaccounts')),
                ('discount', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='discounts.discounts')),
                ('monthly_statement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.monthlystatements')),
            ],
            options={
                'db_table': 'discount_bucket',
            },
        ),
        migrations.CreateModel(
            name='CreditAdditions',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, max_length=36, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('code', models.CharField(max_length=6, unique=True)),
                ('value', models.DecimalField(decimal_places=3, default=0.0, max_digits=10)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('billing_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.billingaccounts')),
            ],
            options={
                'db_table': 'credit_additions',
            },
        ),
        migrations.CreateModel(
            name='BillingAccountLogs',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('state_id', models.CharField(max_length=20, null=True)),
                ('gst_no', models.CharField(max_length=20, null=True)),
                ('business_name', models.CharField(max_length=250, null=True)),
                ('business_address', models.TextField()),
                ('business_pan', models.CharField(max_length=20, null=True)),
                ('business_country', models.CharField(max_length=250, null=True)),
                ('business_state', models.CharField(max_length=250, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('billing_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.billingaccounts')),
            ],
            options={
                'db_table': 'billing_account_logs',
            },
        ),
        migrations.CreateModel(
            name='BillingAccountDocs',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, max_length=36, primary_key=True, serialize=False)),
                ('doc_number', models.CharField(max_length=200)),
                ('doc_path', models.CharField(max_length=250)),
                ('doc_name', models.CharField(max_length=250)),
                ('doc_ext', models.CharField(max_length=20)),
                ('status', models.SmallIntegerField(default=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('billing_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.billingaccounts')),
                ('doc_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.doctypes')),
            ],
            options={
                'db_table': 'billing_account_docs',
            },
        ),
        migrations.CreateModel(
            name='BillingAccountCredits',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, max_length=36, primary_key=True, serialize=False)),
                ('credit_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('trans_type', models.CharField(max_length=10)),
                ('description', models.CharField(max_length=250, null=True)),
                ('status', models.SmallIntegerField(default=1)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('billing_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.billingaccounts')),
            ],
            options={
                'db_table': 'billing_account_credits',
            },
        ),
        migrations.AddIndex(
            model_name='servicecontacts',
            index=models.Index(fields=['billing_account_id'], name='service_con_billing_7b02ff_idx'),
        ),
        migrations.AddIndex(
            model_name='servicecontacts',
            index=models.Index(fields=['contact_type'], name='service_con_contact_16b07d_idx'),
        ),
        migrations.AddIndex(
            model_name='servicecontacts',
            index=models.Index(fields=['mobile'], name='service_con_mobile_17e157_idx'),
        ),
        migrations.AddIndex(
            model_name='servicecontacts',
            index=models.Index(fields=['email'], name='service_con_email_a3b56b_idx'),
        ),
        migrations.AddIndex(
            model_name='servicecontacts',
            index=models.Index(fields=['status'], name='service_con_status_779195_idx'),
        ),
        migrations.AddIndex(
            model_name='monthlystatements',
            index=models.Index(fields=['created'], name='monthly_sta_created_4d0fa1_idx'),
        ),
        migrations.AddIndex(
            model_name='monthlystatements',
            index=models.Index(fields=['billing_account_id', 'type', 'created'], name='monthly_sta_billing_16e60f_idx'),
        ),
        migrations.AddIndex(
            model_name='discountbuckets',
            index=models.Index(fields=['status'], name='discount_bu_status_2a7221_idx'),
        ),
        migrations.AddIndex(
            model_name='discountbuckets',
            index=models.Index(fields=['billing_account_id', 'status'], name='discount_bu_billing_b33272_idx'),
        ),
        migrations.AddIndex(
            model_name='billingaccounts',
            index=models.Index(fields=['parent_id'], name='billing_acc_parent__643737_idx'),
        ),
        migrations.AddIndex(
            model_name='billingaccounts',
            index=models.Index(fields=['org_type_id'], name='billing_acc_org_typ_a19798_idx'),
        ),
        migrations.AddIndex(
            model_name='billingaccounts',
            index=models.Index(fields=['status'], name='billing_acc_status_2d1635_idx'),
        ),
        migrations.AddIndex(
            model_name='billingaccounts',
            index=models.Index(fields=['billing_day'], name='billing_acc_billing_e9518d_idx'),
        ),
        migrations.AddIndex(
            model_name='billingaccounts',
            index=models.Index(fields=['account_manager'], name='billing_acc_account_2fa772_idx'),
        ),
        migrations.AddIndex(
            model_name='billingaccountlogs',
            index=models.Index(fields=['billing_account_id'], name='billing_acc_billing_0a0143_idx'),
        ),
        migrations.AddIndex(
            model_name='billingaccountdocs',
            index=models.Index(fields=['status'], name='billing_acc_status_c6267c_idx'),
        ),
        migrations.AddIndex(
            model_name='billingaccountdocs',
            index=models.Index(fields=['billing_account_id', 'status'], name='billing_acc_billing_e67a6c_idx'),
        ),
        migrations.AddIndex(
            model_name='billingaccountcredits',
            index=models.Index(fields=['status'], name='billing_acc_status_3cb54f_idx'),
        ),
        migrations.AddIndex(
            model_name='billingaccountcredits',
            index=models.Index(fields=['billing_account_id', 'status'], name='billing_acc_billing_dc7d43_idx'),
        ),
    ]
