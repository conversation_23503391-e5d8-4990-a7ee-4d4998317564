import logging

from django.core.management.base import BaseCommand, CommandError

from accounts.billing_accounts.events import UpcomingServiceRenewalEvent

from accounts.billing_accounts.models import BillingAccounts

logger = logging.getLogger("service_renewal")


class Command(BaseCommand):
    help = "Service Renewal"

    def add_arguments(self, parser):
        parser.add_argument("ban", type=str, help="ban")

    def handle(self, *args, **options):
        logger.title("Service Renewal").info(options)
        ban = options["ban"]
        if not ban:
            raise CommandError("ban can't be none")

        return self.handle_process(ban)

    def handle_process(self, ban):
        try:
            ban = ban.upper()
            billing_account = BillingAccounts.objects.filter(
                ac_number=ban
            ).first()
            if billing_account:
                logger.title("Service Renewal").info(
                    "billing_account id: " + billing_account.id
                )
                response = (
                    UpcomingServiceRenewalEvent()
                    .billing_account_id(billing_account.id)
                    .ban(ban)
                    .send()
                )
                logger.title("Service Renewal").info(
                    "response: " + str(response)
                )
        except Exception as e:
            logger.title("Service Renewal Failed").critical(e, exc_info=True)
