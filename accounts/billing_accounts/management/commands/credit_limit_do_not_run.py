import logging
from django.core.management.base import BaseCommand
from django.db.models import Q
from django.db.models.query import QuerySet
from typing import List, Dict, Any
from accounts.billing_accounts.models import BillingAccounts
from accounts.billing_accounts.utils.credit_limit_check import (
    credit_limit_check,
)

logger = logging.getLogger("credit_limit")


class Command(BaseCommand):

    help = "credit limit check"

    def handle(self, *args, **options):
        try:
            billing_accounts = self.fetch_billing_accounts()
            for billing_account in billing_accounts:
                logger.title("Credit Limit Check").info(
                    "billing_account id: " + billing_account["id"]
                )
                response = credit_limit_check(billing_account["id"])
                if response:
                    logger.title("Credit Limit Check").info(
                        "Credit limit check for billing account id: "
                        + billing_account["id"]
                        + " is successful"
                    )
                else:
                    logger.title("Credit Limit Check").error(
                        "Credit limit check for billing account id: "
                        + billing_account["id"]
                        + " failed"
                    )

        except Exception as e:
            logger.title("Credit Limit Check").critical(e, exc_info=True)

    def fetch_billing_accounts(self) -> QuerySet[Dict[str, Any]]:
        billing_accounts: QuerySet[
            Dict[str, Any]
        ] = BillingAccounts.objects.filter(
            Q(parent_id="0") | Q(parent_id="") | Q(parent_id__isnull=True)
        ).values(
            "id"
        )

        logger.title("query #get_ban_for_credit_limit").info(
            str(billing_accounts.query)
        )
        return billing_accounts
