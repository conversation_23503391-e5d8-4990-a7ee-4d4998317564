import logging

from django.core.management.base import BaseCommand, CommandError

from accounts.billing_accounts.utils.account_reactivation import (
    reactivate_account,
)
from accounts.billing_accounts.exceptions import (
    AccountActivationFailedException,
    FraudBillingAccountException,
    InvalidBillingAccountException,
)
from accounts.services.exceptions import InvalidServiceException

logger = logging.getLogger("account_reactivation")


class Command(BaseCommand):
    help = "Reactivate service and number"

    def add_arguments(self, parser):
        parser.add_argument(
            "billing_account_id", type=str, help="billing account id"
        )

    def handle(self, *args, **options):
        logger.title("Account Reactivation Input").info(options)
        billing_account_id = options["billing_account_id"]
        if not billing_account_id:
            raise CommandError("billing_account_id can't be none")

        return self.handle_process(billing_account_id)

    def handle_process(self, billing_account_id):
        try:
            reactivate_account(billing_account_id)
            self.stdout.write(
                self.style.SUCCESS(
                    f"Successfully Reactivated: {billing_account_id}"
                )
            )
        except (
            FraudBillingAccountException,
            InvalidServiceException,
            InvalidBillingAccountException,
            AccountActivationFailedException,
        ) as e:
            logger.title("Account Reactivation Failed").error(e, exc_info=True)
        except Exception as e:
            logger.title("Account Reactivation Failed").critical(
                e, exc_info=True
            )
