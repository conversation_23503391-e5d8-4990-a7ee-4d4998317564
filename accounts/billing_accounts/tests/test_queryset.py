from decimal import Decimal
from django.test import TestCase
from accounts.billing_accounts.tests.factories import (
    BillingAccountFactory,
    BillingAccountCreditsFactory,
)
from accounts.billing_accounts.enums import (
    BillingStatusEnum,
    BillingAccountCreditStatusEnum,
)
from accounts.billing_accounts.models import BillingAccountCredits


class TestBillingAccountCreditQuerySet(TestCase):
    def setUp(self):
        self.parent_account = BillingAccountFactory()
        self.billing_account1 = BillingAccountFactory(
            parent=self.parent_account
        )

        # Create billing account credit for parent ban
        self.billing_account_credit1 = BillingAccountCreditsFactory(
            billing_account=self.parent_account,
            credit_amount=100,
        )

        # Create billing account credit for normal ban
        self.billing_account2 = BillingAccountFactory()
        self.billing_account_credit2 = BillingAccountCreditsFactory(
            billing_account=self.billing_account2,
            credit_amount=100,
        )

    def test_billing_account_queryset(self):
        qs = BillingAccountCredits.entries.billing_account(
            self.billing_account1
        )
        self.assertNotIn(self.billing_account_credit1, qs)
        self.assertNotIn(self.billing_account_credit2, qs)
        self.assertEqual(qs.count(), 0)

        qs = BillingAccountCredits.entries.billing_account(
            self.billing_account2
        )
        self.assertIn(self.billing_account_credit2, qs)
        self.assertNotIn(self.billing_account_credit1, qs)
        self.assertEqual(qs.count(), 1)

    def test_billing_account_returns_all_rows(self):
        """Test that billing_account filter returns all the rows (active/inactive) for a given billing account"""
        billing_account = BillingAccountFactory()
        credit_inactive_row = BillingAccountCreditsFactory(
            billing_account=billing_account,
            credit_amount=100,
            status=BillingAccountCreditStatusEnum.INACTIVE.value,
        )
        credit_active_row = BillingAccountCreditsFactory(
            billing_account=billing_account,
            credit_amount=100,
            status=BillingAccountCreditStatusEnum.ACTIVE.value,
        )
        qs = BillingAccountCredits.entries.billing_account(billing_account)
        self.assertIn(credit_inactive_row, qs)
        self.assertIn(credit_active_row, qs)
        self.assertEqual(qs.count(), 2)

    def test_credit_amount(self):
        """Test get credit_amount of active row"""
        billing_account = BillingAccountFactory()
        BillingAccountCreditsFactory(
            billing_account=billing_account,
            credit_amount=50,
            status=BillingAccountCreditStatusEnum.INACTIVE.value,
        )
        BillingAccountCreditsFactory(
            billing_account=billing_account,
            credit_amount=100,
            status=BillingAccountCreditStatusEnum.ACTIVE.value,
        )
        credit_amount = BillingAccountCredits.entries.filter(
            billing_account=billing_account
        ).credit_amount()
        self.assertEqual(credit_amount, 100)
        self.assertIsInstance(credit_amount, Decimal)

    def test_credit_amount_zero_when_active_row_does_not_exist(self):
        billing_account = BillingAccountFactory()
        BillingAccountCreditsFactory(
            billing_account=billing_account,
            credit_amount=50,
            status=BillingAccountCreditStatusEnum.INACTIVE.value,
        )
        credit_amount = BillingAccountCredits.entries.filter(
            billing_account=billing_account
        ).credit_amount()
        self.assertEqual(credit_amount, 0)
        self.assertIsInstance(credit_amount, Decimal)
