import pytest
from django.test import TestCase
import unittest.mock as mock

from accounts.billing_accounts.events import (
    UpcomingServiceRenewalEvent,
    MonthlyStatementGeneratedEvent,
    ServiceRentalPendingEvent,
)

from accounts.billing_accounts.tests.factories import BillingAccountFactory
from accounts.core.tests.factories import CountryFactory
from accounts.products.tests.factories import ProductFactory
from accounts.services.tests.factories import ServiceFactory


class TestEvents(TestCase):
    def setUp(self):
        self.country = CountryFactory.create()
        self.product = ProductFactory.create(
            name="MyOperator India", country=self.country, short_code="myopin"
        )
        self.product_2 = ProductFactory.create(
            name="Heyo India", country=self.country, short_code="heyoin"
        )
        self.account = BillingAccountFactory.create(ac_number="ABCXYZ")
        ServiceFactory.create(
            billing_account=self.account,
            product_id=self.product.id,
            gsn="abc123",
            status=1,
            live_status=1,
        )

        self.account_2 = BillingAccountFactory.create()
        ServiceFactory.create(
            billing_account=self.account_2,
            product_id=self.product_2.id,
        )

    @classmethod
    def sns_client(cls):
        sns_client = mock.MagicMock()
        sns_client.publish.return_value = {"MessageId": "1234"}
        return sns_client

    def test_upcoming_service_renewal_event_success(self):
        sns_client = self.sns_client()
        event = UpcomingServiceRenewalEvent()
        event.billing_account_id(self.account.id)
        event.ban("ABCXYZ")
        event.sns = sns_client
        response = event.send()
        assert response == "1234"
        assert event.EVENT_ACTION == "upcoming_service_renewal"
        assert event.service_type == "myopin"
        assert event.data["ban"] == "ABCXYZ"
        assert event.data["billing_account_id"] == self.account.id

    def test_upcoming_service_renewal_event_failure(self):
        event = UpcomingServiceRenewalEvent()
        with pytest.raises(TypeError):
            event.billing_account_id()
        with pytest.raises(TypeError):
            event.ban()

    def test_monthly_statement_generation_event_success(self):
        sns_client = self.sns_client()
        event = MonthlyStatementGeneratedEvent()
        event.billing_account_id(self.account.id)
        event.ban("ABCXYZ")
        event.sns = sns_client
        response = event.send()
        assert response == "1234"
        assert event.service_type == "myopin"
        assert event.data["ban"] == "ABCXYZ"
        assert event.data["billing_account_id"] == self.account.id

    def test_monthly_statement_generation_event_failure(self):
        event = MonthlyStatementGeneratedEvent()
        with pytest.raises(TypeError):
            event.billing_account_id()
        with pytest.raises(TypeError):
            event.ban()

    def test_service_rental_pending_event_success(self):
        sns_client = self.sns_client()
        event = ServiceRentalPendingEvent()
        event.billing_account_id(self.account.id)
        event.ban("ABCXYZ")
        event.sns = sns_client
        response = event.send()
        assert response == "1234"
        assert event.EVENT_ACTION == "service_rental_pending"
        assert event.service_type == "myopin"
        assert event.data["ban"] == "ABCXYZ"
        assert event.data["billing_account_id"] == self.account.id

    def test_service_rental_pending_event_failure(self):
        event = ServiceRentalPendingEvent()
        with pytest.raises(TypeError):
            event.billing_account_id()

        with pytest.raises(TypeError):
            event.ban()
