from django.urls import reverse
from rest_framework import status
from django.utils import timezone
from rest_framework.test import APITestCase
from accounts.billing_accounts.tests.factories import BillingAccountFactory
import unittest.mock as mock
from accounts.services.tests.factories import (
    ServiceFactory,
    ServiceNumberFactory,
    ServicePackageFactory,
)
from accounts.users.tests.factories import UserProfileFactory
from accounts.billing_accounts.models import BillingAccounts
from accounts.services.models import Services
from accounts.core.tests.factories import StateCodeFactory
from accounts.billing_accounts.constants import (
    BILLING_VERIFICATION_STATE_SEMI_VERIFIED,
)
from accounts.billing_accounts.enums import (
    BillingVerificationStateEnum,
    BillingStatusEnum,
)


class TestBillingAccountRetrieveUpdateAPIView(APITestCase):
    def setUp(self):
        state = StateCodeFactory.create()
        self.billing_account = BillingAccountFactory.create(
            state_id=state.id,
            account_manager_id=None,
            verification_state=BillingVerificationStateEnum.VERIFIED.value,
        )
        self.url = reverse(
            "billing_accounts:retrieve_update",
            kwargs={"id": self.billing_account.id},
        )

    def test_fetch_billing_account_details(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_json = response.json()
        expected_data = {
            "id": self.billing_account.id,
            "ac_number": self.billing_account.ac_number,
            "state_id": self.billing_account.state_id,
            "gst_no": self.billing_account.gst_no,
            "uan": self.billing_account.uan,
            "tan_no": self.billing_account.tan_no,
            "business_name": self.billing_account.business_name,
            "business_address": self.billing_account.business_address,
            "org_type_id": self.billing_account.org_type_id,
            "billing_day": self.billing_account.billing_day,
            "parent_id": self.billing_account.parent_id,
            "credit_limit": self.billing_account.credit_limit,
            "min_bal": float(round(self.billing_account.min_bal, 1)),
            "recharge_on_min_bal": float(
                round(self.billing_account.recharge_on_min_bal, 1)
            ),
            "auto_bill_email": self.billing_account.auto_bill_email,
            "auto_bill_sms": self.billing_account.auto_bill_sms,
            "cr_limit_email": self.billing_account.cr_limit_email,
            "cr_limit_sms": self.billing_account.cr_limit_sms,
            "status": BillingStatusEnum.ACTIVE.name.lower(),
            "business_pan": self.billing_account.business_pan,
            "business_city": self.billing_account.business_city,
            "business_state": self.billing_account.business_state,
            "business_pincode": self.billing_account.business_pincode,
            "business_country": self.billing_account.business_country,
            "business_type": self.billing_account.business_type,
            "billing_property": self.billing_account.billing_property,
            "account_manager_id": self.billing_account.account_manager_id,
            "discount_id": self.billing_account.discount_id,
            "applied_period": self.billing_account.applied_period,
            "verification_state": BillingVerificationStateEnum.VERIFIED.name.lower(),
            "created": self.billing_account.created.strftime(
                "%Y-%m-%dT%H:%M:%SZ"
            ),
            "modified": self.billing_account.modified.strftime(
                "%Y-%m-%dT%H:%M:%SZ"
            ),
        }

        self.assertEqual(response_json["data"].keys(), expected_data.keys())
        self.assertDictEqual(response_json["data"], expected_data)

    def test_fetch_billing_account_details_not_found(self):
        invalid_url = reverse(
            "billing_accounts:retrieve_update", kwargs={"id": 123}
        )
        response = self.client.get(invalid_url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.json()["status"], "error")
        self.assertEqual(response.json()["message"], "Not found.")
        self.assertNotIn("data", response.json())

    def test_edit_billing_account_details_not_found(self):
        invalid_url = reverse(
            "billing_accounts:retrieve_update", kwargs={"id": 123}
        )
        response = self.client.patch(invalid_url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.json()["status"], "error")
        self.assertEqual(response.json()["message"], "Not found.")
        self.assertNotIn("data", response.json())

    def test_edit_billing_account_details_validation_error(self):
        # test with business name with characters not allowed
        response = self.client.patch(
            self.url, data={"business_name": "some $name"}, format="json"
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json()["message"],
            "Validation Failed, Please check errors field for details",
        )

    def test_edit_billing_account_with_verification_state_fraud(self):
        # test with verification state marked as fraud should not be saved
        fraud_api_url = reverse(
            "billing_accounts:mark_fraud",
            kwargs={"billing_account_id": ":id"},
        )
        response = self.client.patch(
            self.url, data={"verification_state": "fraud"}, format="json"
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json()["message"],
            "Validation Failed, Please check errors field for details",
        )
        self.assertEqual(
            response.json()["errors"]["verification_state"],
            [
                f"Unable to set verification state to 'fraud', Please use the {fraud_api_url} API."
            ],
        )

    def test_patch_billing_account_details(self):
        busines_name = "New name Pvt. Ltd"
        business_address = "New address"
        response = self.client.patch(
            self.url,
            data={
                "business_name": busines_name,
                "business_address": business_address,
                "verification_state": BillingVerificationStateEnum.SEMI_VERIFIED.name.lower(),
            },
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            response.json()["message"], "Billing details updated successfully."
        )

        self.billing_account.refresh_from_db()
        self.assertEqual(self.billing_account.business_name, busines_name)
        self.assertEqual(
            self.billing_account.business_address, business_address
        )
        self.assertEqual(
            self.billing_account.verification_state,
            BillingVerificationStateEnum.SEMI_VERIFIED.value,
        )
