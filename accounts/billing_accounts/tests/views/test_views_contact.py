import unittest.mock as mock
from freezegun import freeze_time
from django.utils import timezone
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

from accounts.billing_accounts.tests.factories import (
    BillingAccountFactory,
    ServiceContactsFactory,
)
from accounts.billing_accounts.models import ServiceContacts
from accounts.billing_accounts.enums import ContactStatusEnums


class TestContactListCreateViewAPIView(APITestCase):
    def setUp(self):
        self.billing = BillingAccountFactory.create()

    def test_contact_list_404(
        self,
    ):
        url = reverse(
            "billing_accounts:contact_list_create",
            kwargs={"billing_account_id": "1234"},
        )

        response = self.client.get(
            url,
            format="json",
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.json()["status"] == "error"
        assert response.json()["message"] == "Not found."

    @freeze_time("2023-04-01T18:35:00Z")
    def test_contact_list_success(
        self,
    ):
        contact = ServiceContactsFactory.create(billing_account=self.billing)
        url = reverse(
            "billing_accounts:contact_list_create",
            kwargs={"billing_account_id": self.billing.id},
        )

        response = self.client.get(
            url,
            format="json",
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"
        assert response.json()["data"][0] == {
            "id": contact.id,
            "name": contact.name,
            "email": contact.email,
            "mobile": str(contact.mobile),
            "country_code": contact.country_code,
            "contact_type": ["billing", "general", "tech"],
            "address": contact.address,
            "status": ContactStatusEnums.ACTIVE.name.lower(),
            "created": "2023-04-01T18:35:00Z",
            "modified": "2023-04-01T18:35:00Z",
        }

    def test_contact_list_with_filter_success(
        self,
    ):
        ServiceContactsFactory.create(
            billing_account=self.billing, mobile=**********
        )
        ServiceContactsFactory.create(
            billing_account=self.billing, mobile=**********
        )

        query_params = {
            "mobile": "**********",
        }
        url = reverse(
            "billing_accounts:contact_list_create",
            kwargs={"billing_account_id": self.billing.id},
        )

        response = self.client.get(
            url,
            data=query_params,
            format="json",
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"
        assert response.json()["data"][0]["mobile"] == "**********"

    @mock.patch(
        "accounts.billing_accounts.utils.contact_notifier.ContactNotifier.created"
    )
    def test_contact_create_success(self, mock_contact_notifier):
        mock_contact_notifier.return_value = True
        url = reverse(
            "billing_accounts:contact_list_create",
            kwargs={"billing_account_id": self.billing.id},
        )

        payload = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "mobile": "**********",
            "country_code": "+91",
            "contact_type": ["tech"],
            "address": "Goa",
            "status": "active",
        }

        response = self.client.post(
            url,
            data=payload,
            format="json",
        )

        assert response.status_code == status.HTTP_201_CREATED
        assert response.json()["status"] == "success"
        assert response.json()["message"] == "Contact added successfully"

        service_contact = ServiceContacts.objects.first()
        assert service_contact is not None
        assert service_contact.name == "John Doe"
        assert service_contact.country_code == "+91"
        assert service_contact.mobile == **********
        assert service_contact.contact_type == 4
        assert service_contact.address == "Goa"
        assert service_contact.status == ContactStatusEnums.ACTIVE.value

    def test_contact_create_already_exists_400(self):
        ServiceContactsFactory.create(
            billing_account=self.billing,
            status=1,
            mobile="**********",
            email="<EMAIL>",
        )
        url = reverse(
            "billing_accounts:contact_list_create",
            kwargs={"billing_account_id": self.billing.id},
        )

        payload = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "mobile": "**********",
            "country_code": "+91",
            "contact_type": ["tech"],
            "address": "Goa",
        }

        response = self.client.post(
            url,
            data=payload,
            format="json",
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["status"] == "error"
        assert response.json()["errors"]["non_field_errors"] == [
            "A contact with this mobile number and email already exists."
        ]

    def test_contact_create_invalid_mobile_400(self):
        url = reverse(
            "billing_accounts:contact_list_create",
            kwargs={"billing_account_id": self.billing.id},
        )

        payload = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "mobile": "*********",
            "country_code": "+91",
            "contact_type": ["tech"],
            "address": "Goa",
        }

        response = self.client.post(
            url,
            data=payload,
            format="json",
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["status"] == "error"
        assert response.json()["errors"]["non_field_errors"] == [
            "Invalid mobile number."
        ]

    def test_contact_create_404(self):
        url = reverse(
            "billing_accounts:contact_list_create",
            kwargs={"billing_account_id": "1234"},
        )

        payload = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "mobile": "**********",
            "country_code": "+91",
            "contact_type": ["tech"],
            "address": "Goa",
        }

        response = self.client.post(
            url,
            data=payload,
            format="json",
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.json()["status"] == "error"


class TestContactUpdateView(APITestCase):
    def setUp(self):
        self.billing = BillingAccountFactory.create()
        self.contact = ServiceContactsFactory.create(
            billing_account=self.billing,
            status=1,
        )

    @mock.patch(
        "accounts.billing_accounts.utils.contact_notifier.ContactNotifier.updated"
    )
    def test_contact_update_success(self, mock_contact_notifier):
        mock_contact_notifier.return_value = True
        contact = ServiceContactsFactory.create(
            billing_account=self.billing,
            status=1,
            mobile="**********",
            email="<EMAIL>",
            contact_type=1,
            country_code="+1",
        )
        url = reverse(
            "billing_accounts:contact_update",
            kwargs={"billing_account_id": self.billing.id, "pk": contact.id},
        )

        payload = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "mobile": **********,
            "country_code": "+91",
            "contact_type": ["billing", "tech", "general"],
            "address": "Goa",
            "status": "active",
        }

        response = self.client.put(
            url,
            data=payload,
            format="json",
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"
        assert response.json()["message"] == "Contact updated successfully"

        service_contact = ServiceContacts.objects.get(id=contact.id)
        assert service_contact is not None
        assert service_contact.name == payload["name"]
        assert service_contact.email == payload["email"]
        assert service_contact.mobile == payload["mobile"]
        assert service_contact.country_code == payload["country_code"]
        assert service_contact.contact_type == 7
        assert service_contact.status == 1

    def test_contact_update_invalid_country_code(self):

        contact = ServiceContactsFactory.create(
            billing_account=self.billing,
            status=1,
            mobile="**********",
            email="<EMAIL>",
            contact_type=1,
            country_code="+1",
        )
        url = reverse(
            "billing_accounts:contact_update",
            kwargs={"billing_account_id": self.billing.id, "pk": contact.id},
        )

        payload = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "mobile": **********,
            "country_code": "+999",
            "contact_type": ["billing", "tech", "general"],
            "address": "Goa",
            "status": "active",
        }

        response = self.client.put(
            url,
            data=payload,
            format="json",
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["status"] == "error"
        assert response.json()["errors"]["non_field_errors"] == [
            "Invalid mobile number."
        ]

    def test_contact_update_cannot_mark_inactive(self):
        """Cannot mark inactive since atleast one contact must be active"""
        billing = BillingAccountFactory.create()
        contact = ServiceContactsFactory.create(
            billing_account=billing,
            status=1,
            mobile="**********",
            email="<EMAIL>",
            contact_type=1,
            country_code="+19",
        )
        url = reverse(
            "billing_accounts:contact_update",
            kwargs={"billing_account_id": billing.id, "pk": contact.id},
        )

        payload = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "mobile": **********,
            "country_code": "+91",
            "contact_type": ["billing", "tech", "general"],
            "address": "Goa",
            "status": "inactive",
        }

        response = self.client.put(
            url,
            data=payload,
            format="json",
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["status"] == "error"
        assert response.json()["errors"]["non_field_errors"] == [
            "At least one contact must be active."
        ]

    def test_contact_update_404(self):
        url = reverse(
            "billing_accounts:contact_update",
            kwargs={"billing_account_id": "1234", "pk": 123},
        )

        payload = {
            "name": "John Doe",
            "email": "<EMAIL>",
            "mobile": "**********",
            "country_code": "+91",
            "contact_type": ["tech"],
            "address": "Goa",
        }

        response = self.client.put(
            url,
            data=payload,
            format="json",
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.json()["status"] == "error"
