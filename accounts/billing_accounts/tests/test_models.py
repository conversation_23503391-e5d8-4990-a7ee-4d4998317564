from django.test import TestCase

from accounts.billing_accounts.models import (
    BillingAccounts,
    BillingAccountCredits,
)
from accounts.billing_accounts.tests.factories import (
    BillingAccountFactory,
    BillingAccountCreditsFactory,
)
from accounts.core.tests.factories import CountryFactory
from accounts.products.tests.factories import OrgTypesFactory, ProductFactory
from accounts.billing_accounts.enums import (
    BillingAccountCreditStatusEnum,
    BillingAccountCreditTransType,
)


class TestBillingAccountModels(TestCase):
    def setUp(self):
        self.ac_number = "123456"
        self.country = CountryFactory.create()

        self.product = ProductFactory.create(
            country=self.country,
        )
        self.org_type = OrgTypesFactory.create(product=self.product)

        self.account = BillingAccountFactory.create(
            ac_number=self.ac_number,
            org_type=self.org_type,
        )
        # create faker with status active
        BillingAccountFactory.create_batch(
            5,
            discount=None,
            org_type=self.org_type,
        )

        # create faker with status inactive
        BillingAccountFactory.create_batch(
            5, discount=None, org_type=self.org_type, status=0
        )

    def test_billing_account_manager(self):
        # test case with data found
        queryset = BillingAccounts.objects.ban(self.ac_number).first()
        assert queryset == self.account
        assert queryset.ac_number == self.account.ac_number

        # test case with data not found
        queryset = BillingAccounts.objects.ban("456789").first()
        assert queryset is None

    def test_active_billing_account_manager(self):
        active_count = BillingAccounts.active.count()
        assert active_count == 6


class TestBillingAccountCreditsModel(TestCase):
    def test_add_credit_amount_success(self):
        billing_account = BillingAccountFactory.create()
        billing_account_credit = BillingAccountCreditsFactory.create(
            billing_account=billing_account
        )

        assert billing_account_credit.credit_amount == 0
        assert (
            billing_account_credit.status
            == BillingAccountCreditStatusEnum.ACTIVE.value
        )

        BillingAccountCredits.add_credit_amount(
            billing_account, 100, "test amount"
        )

        billing_account_credit.refresh_from_db()
        assert billing_account_credit.credit_amount == 0
        assert (
            billing_account_credit.status
            == BillingAccountCreditStatusEnum.INACTIVE.value
        )

        billing_account_credit = (
            BillingAccountCredits.entries.get_credit_object(billing_account)
        )
        assert billing_account_credit.credit_amount == 100
        assert (
            billing_account_credit.status
            == BillingAccountCreditStatusEnum.ACTIVE.value
        )
        assert billing_account_credit.description == "test amount"
        assert (
            billing_account_credit.trans_type
            == BillingAccountCreditTransType.CREDIT.value
        )

    def test_deduct_credit_amount_success(self):
        billing_account = BillingAccountFactory.create()
        billing_account_credit = BillingAccountCreditsFactory.create(
            billing_account=billing_account, credit_amount=200
        )

        assert billing_account_credit.credit_amount == 200
        assert (
            billing_account_credit.status
            == BillingAccountCreditStatusEnum.ACTIVE.value
        )

        BillingAccountCredits.deduct_credit_amount(
            billing_account, 100, "test amount"
        )

        billing_account_credit.refresh_from_db()
        assert billing_account_credit.credit_amount == 200
        assert (
            billing_account_credit.status
            == BillingAccountCreditStatusEnum.INACTIVE.value
        )

        billing_account_credit = (
            BillingAccountCredits.entries.get_credit_object(billing_account)
        )
        assert billing_account_credit.credit_amount == 100
        assert (
            billing_account_credit.status
            == BillingAccountCreditStatusEnum.ACTIVE.value
        )
        assert billing_account_credit.description == "test amount"
        assert (
            billing_account_credit.trans_type
            == BillingAccountCreditTransType.DEBIT.value
        )
