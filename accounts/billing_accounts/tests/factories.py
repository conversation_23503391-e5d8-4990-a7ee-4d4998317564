import random
import string

from django.utils import timezone
from factory import Faker, Sequence, SubFactory
from factory.django import DjangoModelFactory

from accounts.billing_accounts.models import (
    BillingAccounts,
    MonthlyStatements,
    ServiceContacts,
    BillingAccountCredits,
)
from accounts.billing_accounts.enums import (
    ContactStatusEnums,
    BillingVerificationStateEnum,
)
from accounts.users.tests.factories import UserProfileFactory
from accounts.products.tests.factories import OrgTypesFactory


class BillingAccountFactory(DjangoModelFactory):
    ac_number = Sequence(
        lambda n: "".join(
            random.choices(string.ascii_uppercase + string.digits, k=6)
        ).upper()
    )
    state_id = 0
    gst_no = "NA"
    tan_no = "NA"
    uan = Faker("pystr", min_chars=15, max_chars=15)
    business_name = Faker("company")
    business_address = Faker("address")
    org_type = SubFactory(OrgTypesFactory)
    billing_day = Faker("random_int", min=1, max=31)
    parent = None
    credit_limit = Faker("random_int", min=0, max=1000000)
    min_bal = Faker("random_int", min=0, max=1000000)
    recharge_on_min_bal = Faker("random_int", min=0, max=1000000)
    auto_bill_email = Faker("random_int", min=0, max=1)
    auto_bill_sms = Faker("random_int", min=0, max=1)
    cr_limit_email = Faker("random_int", min=0, max=1)
    cr_limit_sms = Faker("random_int", min=0, max=1)
    business_pan = Faker("pystr", min_chars=10, max_chars=15)
    business_city = Faker("city")
    business_state = Faker("state")
    business_pincode = Faker("pystr", min_chars=5, max_chars=10)
    business_country = "IN"
    business_type = Faker("word")
    billing_property = Faker("random_int", min=1, max=3)
    account_manager = SubFactory(UserProfileFactory)
    discount = None
    applied_period = Faker("random_int", min=0, max=12)
    total_pending = Faker(
        "pydecimal", left_digits=4, right_digits=2, positive=True
    )
    verification_state = BillingVerificationStateEnum.UNVERIFIED.value

    class Meta:
        model = BillingAccounts


class MonthlyStatementFactory(DjangoModelFactory):
    billing_account = SubFactory(BillingAccountFactory)
    advice_number = Faker("random_number", digits=8)
    from_date = timezone.now()
    to_date = timezone.now()
    no_of_months = Faker("random_int", min=1, max=12)
    service_rent = Faker(
        "pydecimal", left_digits=4, right_digits=2, positive=True
    )
    feature_rent_data = Faker("text")
    feature_usages_data = Faker("text")
    total_amount_data = Faker("text")
    payable_amount = Faker(
        "pydecimal", left_digits=4, right_digits=2, positive=True
    )
    opening_bal = Faker(
        "pydecimal", left_digits=4, right_digits=2, positive=True
    )
    closing_bal = Faker(
        "pydecimal", left_digits=4, right_digits=2, positive=True
    )
    type = "auto"
    pdf_url = Faker("url")
    payment_url = Faker("url")

    class Meta:
        model = MonthlyStatements


class ServiceContactsFactory(DjangoModelFactory):

    billing_account = SubFactory(BillingAccountFactory)
    contact_type = 7
    name = Faker("name")
    job_roll = Faker("job")
    dob = Faker("date_of_birth", minimum_age=18, maximum_age=65)
    gender = Faker("random_element", elements=["Male", "Female"])
    address = Faker("address")
    city = Faker("city")
    state = Faker("state")
    pincode = Faker("random_int", min=100000, max=999999, step=1)
    landmark = Faker("word")
    country_code = "+91"
    mobile = Faker("random_int", min=**********, max=**********, step=1)
    phone = Faker("random_int", min=**********, max=**********, step=1)
    ext = Faker("random_int", min=100, max=999)
    email = Faker("email")
    aadhar_card_number = Faker("uuid4")
    status = ContactStatusEnums.ACTIVE.value

    class Meta:
        model = ServiceContacts


class BillingAccountCreditsFactory(DjangoModelFactory):
    billing_account = SubFactory(BillingAccountFactory)

    class Meta:
        model = BillingAccountCredits
