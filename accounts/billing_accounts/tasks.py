import logging

from django.conf import settings

import celery
import celery.states

from accounts.billing_accounts.exceptions import (
    AccountActivationFailedException,
    FraudBillingAccountException,
    InvalidBillingAccountException,
)
from accounts.services.exceptions import InvalidServiceException

from accounts.billing_accounts.utils.account_reactivation import (
    reactivate_account,
)
from accounts.billing_accounts.utils.billing_account import (
    update_credit_limit,
)
from accounts.payments.exceptions import InvalidPaymentSubscriptionException

logger = logging.getLogger(__name__)


@celery.shared_task(bind=True, queue=settings.CELERY_TASK_DEFAULT_QUEUE)
def account_reactivation(
    task: celery.Task,
    billing_account_id: str,
):
    task.update_state(state=celery.states.STARTED)
    logger.title("Account Reactivation").info(
        {"billing_account_id": billing_account_id}
    )
    try:
        reactivate_account(billing_account_id)
        task.update_state(state=celery.states.SUCCESS)
    except (
        FraudBillingAccountException,
        InvalidServiceException,
        InvalidBillingAccountException,
        AccountActivationFailedException,
    ) as e:
        logger.title("Account Reactivation").error(e, exc_info=True)
        task.update_state(state=celery.states.FAILURE)
        raise
    except Exception as e:
        logger.title("Account Reactivation").critical(e, exc_info=True)
        task.update_state(state=celery.states.FAILURE)
        raise


@celery.shared_task(bind=True, queue=settings.CELERY_TASK_DEFAULT_QUEUE)
def update_ban_credit_limit(
    task: celery.Task,
    billing_account_id: str,
):
    task.update_state(state=celery.states.STARTED)
    logger.title("Update Ban Credit Limit").info(
        {"billing_account_id": billing_account_id}
    )
    try:
        update_credit_limit(billing_account_id)
        task.update_state(state=celery.states.SUCCESS)
    except (
        InvalidPaymentSubscriptionException,
        InvalidBillingAccountException,
    ) as e:
        logger.title("Update Ban Credit Limit").error(e, exc_info=True)
        task.update_state(state=celery.states.FAILURE)
        raise
    except Exception as e:
        logger.title("Update Ban Credit Limit").critical(e, exc_info=True)
        task.update_state(state=celery.states.FAILURE)
        raise
