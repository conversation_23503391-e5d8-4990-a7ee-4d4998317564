from datetime import datetime
from typing import Op<PERSON>, <PERSON><PERSON>, Dict
import logging
from accounts.billing_accounts.enums import ContactStatusEnums
from accounts.billing_accounts.models import BillingAccounts
from accounts.services.models import Services
from accounts.utils.api_services.bubble import BubbleApiService
from accounts.billing_accounts.models import ServiceContacts
from django.conf import settings


logger = logging.getLogger(__name__)


class ContactNotifier:
    """Handles adding or updating a contact and syncing it with Bubble API."""

    def __init__(self, contact: ServiceContacts) -> None:
        self.contact = contact

    def created(self) -> Optional[Dict[str, str]]:
        """Syncs newly created contact with Bubble API."""
        return self._notify("created")

    def updated(self) -> Optional[Dict[str, str]]:
        """Syncs updated contact with Bubble API."""
        return self._notify("updated")

    def _notify(self, action: str) -> Optional[Dict[str, str]]:
        """Handles contact sync logic."""

        data = self._prepare_data(action)
        try:
            return BubbleApiService(
                settings.BUBBLE_API_HOST_CONFIG["BUBBLE_CONTACT_SYNC"]
            ).sync_contact(data)
        except Exception as e:
            logger.error(
                f"Contact sync failed for {action}: {str(e)}", exc_info=True
            )
            return None

    def _prepare_data(self, action: str) -> Dict[str, str]:
        """Prepares the contact data payload for Bubble API."""
        return {
            "ban": self.contact.billing_account.ac_number,
            "company_id": self._get_company_id(),
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "number": f"{self.contact.country_code}{self.contact.mobile}",
            "email": self.contact.email,
            "name": self.contact.name,
            "for": self.contact.get_contact_type_names(),
            "status": ContactStatusEnums.get_name(self.contact.status).lower(),
            "action": action,
        }

    def _get_company_id(
        self,
    ) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """
        Retrieves the Company ID from the services.

        Logic:
        - If the given billing account is Corporate BAN, fetch the first service (by activation date ascending)
        - Otherwise, fetch the service directly associated to billing_account.

        Returns:
            Optional[str]: The GSN if found, otherwise None.
        """
        if BillingAccounts.objects.filter(
            parent=self.contact.billing_account
        ).exists():
            queryset = Services.objects.filter(
                billing_account__parent=self.contact.billing_account
            ).order_by("activation_date")
        else:
            queryset = Services.objects.filter(
                billing_account=self.contact.billing_account
            )
        return queryset.only("gsn").values_list("gsn", flat=True).first()
