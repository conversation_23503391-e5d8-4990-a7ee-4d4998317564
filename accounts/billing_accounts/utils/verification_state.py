import logging
from django.conf import settings
from accounts.billing_accounts.models import BillingAccounts
from accounts.services.utils.activity_note import (
    add_activity_notes,
)
from accounts.services.models import Services
from accounts.billing_accounts.exceptions import (
    InvalidBillingAccountException,
    AlreadyFraudMarkedException,
)
from accounts.services.utils.service_deactivation import deactivate_service
from accounts.billing_accounts.events import FlaggedFraudEvent
from accounts.services.exceptions import InvalidServiceException
from accounts.services.constants import (
    SERVICE_STATUS_INACTIVE,
)

logger = logging.getLogger(__name__)


def mark_as_fraud(billing_account_id: str, user_id: str) -> bool:
    """
    Marks a billing account as fraud.

    :param billing_account_id: The ID of the billing account to mark as fraud.
    :type billing_account_id: str
    :param user_id: The ID of the user performing the action.
    :type user_id: str
    :return: True if the billing account was successfully marked as fraud, False otherwise.
    :rtype: bool
    """
    billing = BillingAccounts.objects.filter(id=billing_account_id).first()
    if not billing:
        raise InvalidBillingAccountException()

    # check if account is already marked as fraud
    if billing.is_fraud():
        raise AlreadyFraudMarkedException()

    service = Services.objects.filter(
        billing_account=billing
    ).first()
    if not service:
        raise InvalidServiceException("No service found for related ban")

    # deactivate service if not inactive
    if service.status != SERVICE_STATUS_INACTIVE:
        deactivate_service(service.id, user_id)

    # set verification status as fraud
    billing.fraud()

    # add activity note
    add_activity_notes(
        billing.id,
        service.id,
        settings.FRAUD_NOTE,
        user_id,
    )

    # trigger SNS account fraud event
    FlaggedFraudEvent().billing_account_id(billing.id).ban(
        billing.ac_number
    ).send()

    return True
