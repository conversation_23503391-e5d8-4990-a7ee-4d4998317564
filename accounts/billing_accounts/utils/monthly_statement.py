import logging
from datetime import timedelta

from django.db.models import Q

from accounts.billing_accounts.models import MonthlyStatements

logger = logging.getLogger(__name__)


def is_last_statement_missing(
    billing_account_id, activation_date, current_date
):
    before_28_days = current_date - timedelta(days=28)

    # check if statement is generated for last month
    activation_date = activation_date

    if activation_date < before_28_days and not fetch_monthly_statement(
        billing_account_id, before_28_days
    ):
        return True
    return False


def fetch_monthly_statement(billing_account_id, date):
    query = MonthlyStatements.objects.filter(
        Q(billing_account_id=billing_account_id)
        & Q(type__contains="auto")
        & Q(created__gte=date)
    )
    if query.exists():
        logger.info(f"monthly statement exists  {query.values()}")
        return query.values()
    else:
        return False
