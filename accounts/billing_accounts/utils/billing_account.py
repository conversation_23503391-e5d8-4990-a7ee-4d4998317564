import re
import typing as t
import logging
from django.conf import settings
from django.db import transaction
from accounts.billing_accounts.models import BillingAccounts, BillingAccountLogs
from accounts.core.models import StateCodes
from accounts.services.utils.service import get_service_details_f_ban
from accounts.products.utils import get_product_details
from typing import Dict, Optional
from django.db.models import Q, Model
from accounts.billing_accounts.constants import BILLING_VERIFICATION_STATE_FRAUD
from dateutil.relativedelta import relativedelta
from django.utils import timezone
import calendar
from accounts.utils.common import convert_utc_to_local, convert_to_utc
from datetime import datetime
from accounts.exceptions import ExternalAPIException
from accounts.billing_accounts.exceptions import (
    InvalidBillingAccountException,
    GstUpdationFailedException,
    InvalidGstNumberException,
)
from accounts.payments.models import (
    PaymentSubscriptions,
)
from accounts.payments.exceptions import InvalidPaymentSubscriptionException
from accounts.payments.enums import PaymentSubscriptionStatusEnum
from accounts.utils.api_services.surepass import SurepassApi

if t.TYPE_CHECKING:
    from accounts.billing_accounts.utils.gst_parser import GSTDetail


logger = logging.getLogger(__name__)


def get_billing_account_details(id: str):
    billing_account = BillingAccounts.objects.filter(id=id).first()
    return billing_account


def get_pilot_billing_account_details(ban_id: str):
    pilot_account = BillingAccounts.objects.pilot_ban(ban_id)
    return pilot_account


def get_product_short_code_from_ban(billing_account_id: str) -> str:
    short_code = None
    # get pilot ban, if billing_account_id is a corporate account
    pilot_ban_details = get_pilot_billing_account_details(billing_account_id)
    if pilot_ban_details:
        billing_account_id = pilot_ban_details["id"]

    service = get_service_details_f_ban(billing_account_id)
    if service:
        product = get_product_details(service.product_id)
        if product:
            short_code = product.short_code

    return short_code


def log_edited_fields(
    ban_id: str,
    old_data: Dict[str, Optional[str]],
    new_data: Dict[str, Optional[str]],
) -> None:
    log_data = {}

    fields_to_compare = [
        "gst_no",
        "state_id",
        "business_name",
        "business_address",
        "business_pan",
        "business_country",
        "business_state",
    ]
    for field in fields_to_compare:
        old_value = old_data.get(field)
        if old_value != new_data.get(field):
            log_data[field] = old_value

    if log_data:
        log_data["billing_account_id"] = ban_id
        BillingAccountLogs.objects.create(**log_data)


def is_valid_gst_no(gst_number: str) -> bool:
    """
    Args:
        gst_number (str): The GSTIN to be validated.

    Returns:
        bool: True if the GSTIN format is valid, False otherwise.

    1. The GSTIN should start with the 2 characters of the number.
    2. The characters 3 to 12 of the GSTIN should match the provided business PAN.
    3. The total length of the GSTIN should exactly 15 characters.
    """
    GST_REGEX = re.compile(
        r"^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9]{1}Z[0-9A-Z]{1}$"
    )
    return bool(GST_REGEX.match(gst_number))


def fetch_billing_account_list(sort, order, **kwargs):
    filter_args = Q()
    ac_number = kwargs.get("ac_number")
    if ac_number is not None:
        filter_args &= Q(ac_number=ac_number)

    business_name = kwargs.get("business_name")
    if business_name is not None:
        filter_args &= Q(business_name__icontains=business_name)

    status = kwargs.get("status")
    if status is not None:
        filter_args &= Q(status=status)

    queryset = BillingAccounts.objects.filter(filter_args)

    if order == "desc":
        queryset = queryset.order_by("-" + sort)
    else:
        queryset = queryset.order_by(sort)

    return queryset


def count_billing_accounts_by_gst(gst_no):
    query_set = BillingAccounts.objects.filter(
        gst_no=gst_no, parent_id__isnull=True
    )
    logger.title("Query #count_billing_accounts_by_gst").info(query_set.query)
    return query_set.count()


def count_billing_accounts_by_gst_in_duration(gst_no, from_date, to_date):
    query_set = BillingAccounts.objects.filter(
        gst_no=gst_no,
        parent_id__isnull=True,
        created__gte=from_date,
        created__lte=to_date,
    )
    logger.title("Query #count_billing_accounts_by_gst_in_duration").info(
        query_set.query
    )
    return query_set.count()


def count_fraud_account_by_gst(gst_no):
    query_set = BillingAccounts.objects.filter(
        gst_no=gst_no,
        verification_state=BILLING_VERIFICATION_STATE_FRAUD,
        parent_id__isnull=True,
    )
    logger.title("Query #count_fraud_account_by_gst").info(query_set.query)
    return query_set.count()


def count_fraud_account_by_gst_in_duration(gst_no, from_date, to_date):
    query_set = BillingAccounts.objects.filter(
        gst_no=gst_no,
        verification_state=BILLING_VERIFICATION_STATE_FRAUD,
        parent_id__isnull=True,
        created__gte=from_date,
        created__lte=to_date,
    )
    logger.title("Query #count_fraud_account_by_gst_in_duration").info(
        query_set.query
    )
    return query_set.count()


def count_billing_accounts_by_uan(uan):
    query_set = BillingAccounts.objects.filter(uan=uan, parent_id__isnull=True)
    logger.title("Query #count_billing_accounts_by_uan").info(query_set.query)
    return query_set.count()


def count_billing_accounts_by_uan_in_duration(uan, from_date, to_date):
    query_set = BillingAccounts.objects.filter(
        uan=uan,
        parent_id__isnull=True,
        created__gte=from_date,
        created__lte=to_date,
    )
    logger.title("Query #count_billing_accounts_by_uan_in_duration").info(
        query_set.query
    )
    return query_set.count()


def count_fraud_account_by_uan(uan):
    query_set = BillingAccounts.objects.filter(
        uan=uan,
        verification_state=BILLING_VERIFICATION_STATE_FRAUD,
        parent_id__isnull=True,
    )
    logger.title("Query #count_fraud_account_by_uan").info(query_set.query)
    return query_set.count()


def count_fraud_account_by_uan_in_duration(uan, from_date, to_date):
    query_set = BillingAccounts.objects.filter(
        uan=uan,
        verification_state=BILLING_VERIFICATION_STATE_FRAUD,
        parent_id__isnull=True,
        created__gte=from_date,
        created__lte=to_date,
    )
    logger.title("Query #count_fraud_account_by_uan_in_duration").info(
        query_set.query
    )
    return query_set.count()


def get_ban_for_credit_limit_new():
    ban = BillingAccounts.objects.filter(
        Q(parent_id="0") | Q(parent_id="") | Q(parent_id__isnull=True)
    ).values(
        "id",
        "billing_day",
        "credit_limit",
        "ac_number",
        "state_id",
        "min_bal",
        "recharge_on_min_bal",
    )

    logger.title("query #get_ban_for_credit_limit").info(str(ban.query))

    return ban


def update_total_pending_amount(ban_id: str, amount: str) -> bool:
    obj: Model = BillingAccounts.objects.get(id=ban_id)
    obj.total_pending = amount
    obj.save()
    return True


def calculate_next_billing_date(
    billing_day: int, activation_datetime: timezone.datetime, time_zone: str
) -> timezone.datetime:
    current_datetime = timezone.now()

    current_datetime_local = convert_utc_to_local(current_datetime, time_zone)

    current_month_year = current_datetime_local.strftime("%Y-%m")

    # Get the last day of the current month to handle end-of-month cases
    last_day_of_month = calendar.monthrange(
        current_datetime_local.year, current_datetime_local.month
    )[1]

    # Check if we should bill in the next month:
    # 1. If today is the activation date,
    # 2. If the activation is in the current month, or
    # 3. If the billing day has already passed in the current month.
    if (
        activation_datetime.date() == current_datetime.date()
        or activation_datetime.strftime("%Y-%m")
        == current_datetime.strftime("%Y-%m")
        or billing_day <= current_datetime_local.day
    ):
        next_month_year = (
            current_datetime_local + relativedelta(months=1)
        ).strftime("%Y-%m")

        # Schedule billing for the next month on the specified billing day
        # Use the last day of the month if the billing day exceeds it
        start_date_str = (
            f"{next_month_year}-{min(billing_day, last_day_of_month)} 00:00:00"
        )
    elif billing_day > last_day_of_month:
        # Schedule billing for the current month on the specified billing day
        # Again, use the last day of the month if the billing day exceeds it
        start_date_str = f"{current_month_year}-{min(billing_day, last_day_of_month)} 00:00:00"
    else:
        start_date_str = f"{current_month_year}-{billing_day} 00:00:00"

    local_datetime = timezone.datetime.strptime(
        start_date_str, "%Y-%m-%d %H:%M:%S"
    )
    # Convert the local datetime to UTC before returning
    return convert_to_utc(local_datetime, time_zone)


def calculate_pro_rata_refund_for_plan(
    plan_amount: float, next_billing_date: timezone.datetime, time_zone: str
) -> float:
    # Convert next billing date to local timezone
    next_billing_date = convert_utc_to_local(next_billing_date, time_zone)
    # Calculate the start and end date of the current billing cycle
    billing_cycle_start_date = next_billing_date - relativedelta(months=1)
    billing_cycle_end_date = next_billing_date - timezone.timedelta(days=1)

    # Calculate the total number of days in the billing cycle
    total_days_in_cycle = (
        billing_cycle_end_date - billing_cycle_start_date
    ).days

    # Calculate the daily rate of the plan
    daily_rate = round(plan_amount / total_days_in_cycle, 2)

    # Calculate the number of days passed in the current billing cycle
    current_time = datetime.now(next_billing_date.tzinfo)
    days_since_cycle_start = max(
        (current_time - billing_cycle_start_date).days, 0
    )

    # Calculate the pro-rata refund amount
    pro_rata_amount = round(daily_rate * days_since_cycle_start, 2)

    return pro_rata_amount


def is_billing_day_passed(day, activation_date):
    # Get the current date and time
    now = timezone.now()

    # If activation_date is in the same month and year as the current date, skip the check
    if activation_date.month == now.month and activation_date.year == now.year:
        return False

    # Get the last day of the current month
    next_month = now.replace(day=28) + timezone.timedelta(
        days=4
    )  # Move to the next month
    last_day_of_month = next_month.replace(day=1) - timezone.timedelta(days=1)

    # Ensure the given day does not exceed the last day of the month
    valid_day = min(day, last_day_of_month.day)

    # Generate the datetime for the valid day
    generated_date = now.replace(
        day=valid_day, hour=0, minute=0, second=0, microsecond=0
    )

    return now > generated_date


def update_credit_limit(billing_account_id):
    billing_account = BillingAccounts.objects.filter(
        id=billing_account_id
    ).first()
    if billing_account is None:
        raise InvalidBillingAccountException()

    payment_subscription = PaymentSubscriptions.objects.filter(
        billing_account=billing_account,
        status=PaymentSubscriptionStatusEnum.ENABLED.value,
    ).first()
    if payment_subscription is None:
        raise InvalidPaymentSubscriptionException()

    product_code = get_product_short_code_from_ban(billing_account_id)
    if product_code == "heyoin":
        with transaction.atomic():
            # Set credit limit
            billing_account.credit_limit = settings.HEYO_CREDIT_LIMIT
            billing_account.save()

    return True


def update_gstin(billing_account: BillingAccounts, gst_no: str):
    """
    Update the GST details & billing address of the billing account.
    Args:
        billing_account: BillingAccounts object
        gst_no: GSTIN number
    Returns:
        None
    Raises:
        InvalidGstNumberException: If the GSTIN number is invalid.
        GstUpdationFailedException: If the GST details update fails.
    """
    if not is_valid_gst_no(gst_no):
        raise InvalidGstNumberException("Invalid GSTIN number.")

    try:
        gst_detail: "GSTDetail" = SurepassApi().gstin_details(gst_no)
        if not gst_detail.is_active():
            raise InvalidGstNumberException("GSTIN is not active.")
    except ExternalAPIException as e:
        raise GstUpdationFailedException(str(e))

    state_code = StateCodes.objects.get(code_for_gst=gst_detail.state_code)
    billing_account.state_id = state_code.id
    billing_account.gst_no = gst_detail.gstin
    billing_account.business_pan = gst_detail.pan
    billing_account.business_name = gst_detail.legal_name
    billing_account.business_address = gst_detail.address
    billing_account.business_city = state_code.name
    billing_account.business_state = state_code.name
    billing_account.business_pincode = gst_detail.pincode
    billing_account.business_country = "India"
    billing_account.modified = timezone.now()
    billing_account.save()
