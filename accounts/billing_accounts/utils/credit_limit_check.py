import logging
import json
from decimal import Decimal
from django.db import transaction
from typing import Dict, List, Any, Union, Optional
from accounts.billing_accounts.models import BillingAccounts
from accounts.services.models import Services
from accounts.billing_accounts.exceptions import InvalidBillingAccountException
from accounts.billing_accounts.utils.billing_account import (
    update_total_pending_amount,
)
from accounts.services.utils.service import (
    get_services_for_credit_limit,
    update_current_usages_service,
)
from accounts.services.utils.service_number import get_service_number_details
from accounts.utils.time import (
    calc_prev_billing_date,
    convert_utc_to_local,
    calculate_local_hour,
)
from accounts.services.utils.pending_usage import calculate_usage
from accounts.payments.utils.payment_subscription import (
    get_billing_account_subscription,
)
from accounts.billing_accounts.utils.service_contact import (
    get_billing_contact_person,
)
from accounts.users.utils.auth import auth_user
from accounts.cafs.utils.caf import get_caf_data_kyc
from accounts.core.utils.tax_calculation import calculate_tax
from accounts.core.utils.email_queue import (
    check_email_send_today,
    enqueue_email,
)
from accounts.core.utils.sms_queue import check_sms_send_today, enqueue_sms
from accounts.services.utils.activity_note import add_activity_notes
from django.utils import timezone
from accounts.utils.api_services.account_v1 import AccountApiV1
from accounts.utils.api_services.myoperator import MyOperator
from accounts.services.utils.service import sync_memcache
from accounts.services.exceptions import InvalidServiceException
from django.conf import settings
from accounts.services.events import ServiceSuspensionEvent

logger = logging.getLogger(__name__)


def credit_limit_check(billing_account_id):

    billing_account = BillingAccounts.objects.filter(
        id=billing_account_id
    ).first()

    if not billing_account:
        raise InvalidBillingAccountException("Invalid billing account id")

    billing_account_ids = []
    child_billing_ids = []

    # check for corporate
    child_accounts = BillingAccounts.objects.parent_ban(
        billing_account_id
    ).all()

    if len(child_accounts) <= 0:
        billing_account_ids.append(billing_account_id)
    else:
        for child_account in child_accounts:
            billing_account_ids.append(child_account.id)
            child_billing_ids.append(child_account.id)

    services = get_services_for_credit_limit(billing_account_ids)

    if not services:
        logger.title("Credit limit check").error("Services not found")
        return False

    total_billable = 0
    pending_other_charges = 0
    service_data = []

    for service in services:
        country_details = service.product.country

        service_number_details = get_service_number_details(service.id)
        if not service_number_details:
            logger.title("Credit limit check").error(
                f"service number not found for service id : {service.id}"
            )

        start_date = calc_prev_billing_date(
            billing_account.billing_day,
            service.activation_date,
            country_details.timezone_name,
        )
        end_date = timezone.now()

        service_data.append(
            {
                "id": service.id,
                "gsn": service.gsn,
                "short_code": country_details.short_code,
                "billing_account_id": service.billing_account.id,
            }
        )

    usage_data = calculate_usage(billing_account)

    total_billable = usage_data["current_usages"]

    other_charges = round(
        usage_data["pending_rental"]
        + usage_data["other_charges"]
        + usage_data["service_number_cost"],
        2,
    )

    if other_charges > 0:
        pending_other_charges = other_charges

    update_total_pending_amount(billing_account_id, usage_data["available"])

    is_subscribed = False
    subscription = get_billing_account_subscription(billing_account_id)
    if subscription and subscription.gateway != "razorpay_emandate":
        is_subscribed = True
        if (
            subscription.gateway == "razorpay"
            and subscription.subs_type not in ["card", "upi"]
        ):
            is_subscribed = False

    process_data = {
        "from_date": convert_utc_to_local(
            start_date, country_details.timezone_name
        ),
        "to_date": convert_utc_to_local(
            end_date, country_details.timezone_name
        ),
        "child_billing_id": child_billing_ids[0]
        if len(child_billing_ids) > 0
        else None,
        "billing_account_id": billing_account.id,
        "is_subscribed": is_subscribed,
        "wallet": usage_data["advance"],
        "pending_usages": usage_data["pending_usages"],
        "pending_other_charges": pending_other_charges,
        "total_billable": total_billable,
        "service_number": service_number_details.service_number,
        "discount": usage_data["discount"],
        "min_bal": billing_account.min_bal,
    }

    return process_credit_limit_ops(process_data, service_data)


def process_credit_limit_ops(data, service_data):
    pending = data["wallet"] - (data["total_billable"] + data["pending_usages"])

    if not (
        (data["total_billable"] + data["pending_usages"]) > 0
        and pending < data["min_bal"]
    ):
        # TODO Error message
        logger.title("Credit limit check").error("condition failed")
        return False

    data["usage_balance"] = pending

    billing_account = (
        BillingAccounts.objects.select_related("account_manager")
        .filter(id=data["billing_account_id"])
        .first()
    )
    data["state_id"] = billing_account.state_id
    data["ac_number"] = billing_account.ac_number
    data["min_bal"] = billing_account.min_bal
    data["recharge_on_min_bal"] = billing_account.recharge_on_min_bal
    data["business_name"] = billing_account.business_name
    data["account_manager"] = getattr(
        billing_account.account_manager, "id", None
    )
    data["credit_limit"] = billing_account.credit_limit
    data["cr_limit_email"] = billing_account.cr_limit_email
    data["cr_limit_sms"] = billing_account.cr_limit_sms
    # get service details
    ban_id = (  # noqa: F841
        data["child_billing_id"]
        if data.get("child_billing_id")
        else data["billing_account_id"]
    )
    service_details = Services.active.billing_account(billing_account).first()

    # get country timezone
    country_timezone = service_details.product.country
    if country_timezone.short_code == "IN":
        data["api_url"] = settings.MYOPERATOR_API_URL
        data["currency"] = "INR"
    else:
        data["api_url"] = settings.MYOPERATOR_API_URL_US
        data["currency"] = "USD"

    deduction_amount = calculate_deduction_amount(data)

    # get contact person associated with billing account
    billing_contact_person = get_billing_contact_person(
        data["billing_account_id"]
    )

    data["ac_email"] = ""
    data["ac_name"] = "Contact your account manager"

    # get user info
    user_info = auth_user(data["account_manager"])
    if not user_info:
        # if user not found get user from caf tables
        caf_data = get_caf_data_kyc(data["billing_account_id"])

        # if user not found set default
        if caf_data:
            # get user profile info
            user_profile_info = auth_user(caf_data.support_agent)
            if user_profile_info:
                data["ac_email"] = user_profile_info.email
                data["ac_name"] = user_profile_info.name
    else:
        data["ac_email"] = user_info.email
        data["ac_name"] = user_info.name

    # calculate tax
    tax = calculate_tax(
        deduction_amount, country_timezone.short_code, data["state_id"]
    )

    data["recharge_link"] = ""
    if (
        deduction_amount > 0
        and service_details.product.id != settings.HEYO_PRODUCT_ID
    ):
        # frame recharge array
        recharge_data = frame_recharge_data(
            data["billing_account_id"],
            data["state_id"],
            data["ac_number"],
            (deduction_amount + tax["tax_total"]),
            country_timezone.id,
            billing_contact_person,
        )

        # get recharge link
        recharge_link = AccountApiV1().generate_recharge_link(recharge_data)
        data["recharge_link"] = recharge_link

    # deduction process will start
    # check billing account is subscribed for recurring payment or not
    if data["is_subscribed"] and deduction_amount > 1:
        if country_timezone.short_code == "IN":
            # TODO message
            logger.title("Credit limit check").info("local hour timezone")
            return True
        auto_deduct_process(
            data,
            service_data,
            deduction_amount,
            pending,
            tax,
            billing_contact_person,
        )
    else:
        if pending <= ((-1) * data["credit_limit"]):
            handle_instant_suspension(
                data,
                service_data,
                service_details.id,
                deduction_amount,
                billing_contact_person,
            )
        else:
            handle_low_balance(
                data,
                service_details.id,
                deduction_amount,
                billing_contact_person,
            )
    return True


def set_email_data(
    type: str,
    cr_limit_email: int,
    contact_person: Optional[Dict[str, str]],
    data: Dict[str, Any],
) -> Dict[str, Any]:
    if cr_limit_email == 1:
        data["emails"] = contact_person or [
            {
                "name": settings.CREDIT_LIMIT_EMAIL_NAME,
                "email": settings.CREDIT_LIMIT_EMAIL_ID,
            }
        ]
    else:
        data["emails"] = [
            {
                "name": settings.CREDIT_LIMIT_EMAIL_NAME,
                "email": settings.CREDIT_LIMIT_EMAIL_ID,
            }
        ]
    email_json_data: str = frame_email_content(type, data)
    return email_json_data


def auto_deduct_process(
    data: Dict[str, Any],
    service_data: Dict[str, Any],
    deduction_amount: float,
    pending: float,
    tax: Dict[str, Any],
    contact_person: Dict[str, Any],
) -> bool:
    deduction_response: Dict[str, Any] = AccountApiV1().auto_deduct(
        data["billing_account_id"],
        str(deduction_amount + round(tax["tax_total"], 2)),
    )

    if (
        deduction_response["status"] == "error"
        and deduction_response["data"]["subscription_gateway"] != "razorpay"
        and deduction_response["data"]["subscription_gateway"]
        != "razorpay_emandate"
    ):
        type: int = 1
        data["deduction_amount"] = deduction_amount
        data["payment_id"] = deduction_response["data"]["recurring_resposne"][
            "payment_id"
        ]

        if pending <= ((-1) * data["credit_limit"]):
            suspend_services(
                data["billing_account_id"],
                service_data,
                data["ac_number"],
                data.get("child_billing_id"),
            )
            type = 4  # noqa

        """ email_json_data: str = set_email_data(
            type, data["cr_limit_email"], contact_person, data
        )
        email_sent_today: bool = check_email_send_today(
            data["billing_account_id"]
        )
        if not email_sent_today:
            enqueue_email(
                data["billing_account_id"],
                data["service_number"],
                email_json_data,
            ) """
    return True


def handle_low_balance(
    data: Dict[str, Any],
    service_id: str,
    deduction_amount: float,
    contact_person: Dict[str, Any],
) -> bool:
    """type: int = 3
    data["deduction_amount"] = deduction_amount
    email_json_data: str = set_email_data(
        type, data["cr_limit_email"], contact_person, data
    )
    email_sent_today: bool = check_email_send_today(data["billing_account_id"])
    if not email_sent_today:
        enqueue_email(
            data["billing_account_id"], data["service_number"], email_json_data
        )

    if data["cr_limit_sms"] == 1:
        sms_json_data: str = frame_sms_array(
            type, data["billing_account_id"], service_id, data["api_url"]
        )
        sms_sent_today: bool = check_sms_send_today(data["billing_account_id"])
        if not sms_sent_today:
            enqueue_sms(
                data["billing_account_id"],
                data["service_number"],
                sms_json_data,
            )"""
    return True


def handle_instant_suspension(
    data: Dict[str, Any],
    service_data: Dict[str, Any],
    service_id: str,
    deduction_amount: float,
    contact_person: Dict[str, Any],
) -> bool:
    suspend_services(
        data["billing_account_id"],
        service_data,
        data["ac_number"],
        data.get("child_billing_id"),
    )
    """ type: int = 2
    data["deduction_amount"] = deduction_amount
    email_json_data: str = set_email_data(
        type, data["cr_limit_email"], contact_person, data
    )
    decode_data: Dict[str, Any] = json.loads(email_json_data)
    post_data: Dict[str, str] = {"data": email_json_data}
    AccountApiV1().account_suspension(decode_data["url"], post_data)

    if data["cr_limit_sms"] == 1:
        sms_json_data: str = frame_sms_array(
            type, data["billing_account_id"], service_id, data["api_url"]
        )
        decode_data = json.loads(sms_json_data)
        for val in decode_data["api_data"]:
            sms_data = json.dumps(
                {
                    "template_slug": val["slug"],
                    "county_code": val["country_code"],
                    "send_to": val["phone_number"],
                    "app": val["app"],
                    "params": val["params"],
                }
            )
            AccountApiV1().send_sms(sms_data) """
    return True


def suspend_services(
    billing_account_id: str,
    service_data: List[Dict[str, str]],
    ac_number: str,
    child_billing_id: str,
) -> None:
    for val in service_data:
        service = Services.objects.filter(id=val["id"]).first()
        if not service:
            raise InvalidServiceException("Invalid Service")
        with transaction.atomic():
            # deactivate service
            service.suspend()
            # sync memcache
            mem_response = sync_memcache(service.id)
            if not mem_response:
                logger.title("Update Memcache").error(
                    f"unable to update memcache in case of service suspend for service id : {val['id']} #credit limit cron , Response: {mem_response}"
                )

        add_activity_notes(
            billing_account_id,
            val["id"],
            settings.SUSPEND_NOTE,
            settings.CL_CRON,
        )

        # trigger sns
        ServiceSuspensionEvent().company_id(service.gsn).ban(ac_number).send()

        """ sns_data = {
            "gsn": val['gsn'],
            "service_country_short_code": val['short_code'],
            "event": 2,
            "ban": ac_number,
            "is_corporate": 1 if child_billing_id else 0,
            "child_ban": service.billing_account.ac_number if child_billing_id else None,
        } """
        MyOperator().update_permissions({"gsn": val["gsn"]}, val["short_code"])


def frame_recharge_data(
    billing_account_id: str,
    state_id: str,
    ac_number: str,
    total_outstanding: float,
    country_id: int,
    contact_person: Any,
) -> Dict[str, Union[int, str, None]]:
    pay_data: Dict[str, Union[int, str, None]] = {
        "country": country_id,
        "amount": "0" if total_outstanding < 0 else str(total_outstanding),
        "state_id": state_id,
        "billing_account_id": billing_account_id,
        "ban_no": ac_number,
        "renewal_flag": 0,
        "renewal_services": None,
        "recharge_type": "fix",
    }

    if contact_person:
        pay_data.update(
            {
                "name": contact_person["name"],
                "email": contact_person["email"],
                "phone": contact_person["mobile"],
            }
        )
    else:
        logger.title("frame recharge data").error(
            f"contact person not found in #_process_operations for billing account id : {billing_account_id}"
        )
        pay_data.update(
            {
                "name": settings.RECHARGE_CONTACT_NAME,
                "email": settings.RECHARGE_CONTACT_EMAIL,
                "phone": settings.RECHARGE_CONTACT_NUMBER,
            }
        )

    return pay_data


def frame_email_content(
    type: int, data: Dict[str, Union[str, List[Dict[str, Union[str, int]]]]]
) -> str:
    post_arr: Dict[str, Union[str, List[Dict[str, Union[str, int]]]]] = {
        "url": data["api_url"] + "sendmail",
        "to": [
            {
                "email": customer_details["email"],
                "name": customer_details["name"],
                "type": "to",
            }
            for customer_details in [data["emails"]]
            if customer_details["email"]
        ],
        "parameters": [
            {"name": "COMPANY_NAME", "content": data["business_name"]},
            {"name": "SERVICE_NUMBER", "content": f"+{data['service_number']}"},
            {
                "name": "FROM_DATE",
                "content": data["from_date"].strftime("%Y-%m-%d"),
            },
            {
                "name": "TO_DATE",
                "content": data["to_date"].strftime("%Y-%m-%d"),
            },
            {
                "name": "CURRENT_USAGE",
                "content": str(data["total_billable"])
                if data["total_billable"]
                else "0",
            },
            {
                "name": "OUTSTANDING_USAGE",
                "content": str(data["pending_usages"]),
            },
            {"name": "PREPAID_AMOUNT", "content": str(data["wallet"])},
            {"name": "USAGE_BALANCE", "content": str(data["usage_balance"])},
            {"name": "MINIMUM_BALANCE", "content": str(data["min_bal"])},
            {
                "name": "OUTSTANDING_OTHER",
                "content": str(data["pending_other_charges"]),
            },
            {
                "name": "RECHARGE_VALUE",
                "content": str(data["deduction_amount"]),
            },
            {"name": "RECHARGE_LINK", "content": data["recharge_link"]},
            {"name": "CURRENCY", "content": data["currency"]},
            {"name": "CREDIT_LIMIT", "content": str(data["credit_limit"])},
            {"name": "AM_NAME", "content": data["ac_name"]},
            {"name": "BAN", "content": data["ac_number"]},
        ],
    }

    if type in (1, 2, 4):
        if data["ac_email"]:
            post_arr["to"].append(
                {
                    "email": data["ac_email"],
                    "name": data["ac_name"],
                    "type": "bcc",
                }
            )

    if type == 1:
        post_arr[
            "subject"
        ] = "[ALERT] Payment failure and low account balance on your MyOperator account"
        post_arr["template_name"] = settings.TEMPLATE["LOW_BALANCE"]
    elif type == 2:
        post_arr[
            "subject"
        ] = "[ATTENTION] Your MyOperator account has been suspended"
        post_arr["template_name"] = settings.TEMPLATE["SERVICE_SUSPEND"]
    elif type == 3:
        post_arr[
            "subject"
        ] = "[ALERT] Low account balance on your MyOperator account"
        post_arr["template_name"] = settings.TEMPLATE["CREDIT_LIMIT"]
    elif type == 4:
        post_arr[
            "subject"
        ] = "[ATTENTION] Your MyOperator account has been suspended- Credit card payment failure"
        post_arr["template_name"] = settings.TEMPLATE["SERVICE_SUSPEND_FAILURE"]

    if type == 1 or type == 4:
        post_arr["parameters"].append(
            {"name": "PAYMENT_ID", "content": data["payment_id"]}
        )

    return json.dumps(post_arr)


def frame_sms_array(
    type: int, ban_id: str, service_id: str, base_url: str
) -> str:

    sms_array = {"api_data": []}

    billing_contact_person: List[
        Dict[str, Union[str, int]]
    ] = get_billing_contact_person(ban_id)
    service_number_details = get_service_number_details(service_id)
    service_number = service_number_details.service_number
    if "mobile" in billing_contact_person and billing_contact_person["mobile"]:
        country_code = billing_contact_person.get("country_code", "").lstrip(
            "+"
        )

        temp = {
            "slug": "myop-credit-limit-low-balance-sms"
            if type == 3
            else "myop-credit-limit-exceeded-sms",
            "country_code": country_code,
            "phone_number": billing_contact_person["mobile"],
            "app": "myoperator.mobile"
            if country_code == "91"
            else "myoperator.international",
            "params": {"service_number": service_number},
        }

        sms_array["api_data"].append(temp)

    sms_array["sms_type"] = "limit_v2"
    return json.dumps(sms_array)


def calculate_deduction_amount(data: dict) -> float:
    total_outstanding = round(
        data["total_billable"]
        + data["pending_usages"]
        + data["pending_other_charges"]
        - data["wallet"],
        2,
    )

    if (total_outstanding + Decimal(data["min_bal"])) > Decimal(
        data["recharge_on_min_bal"]
    ):
        deduction_amount = total_outstanding + max(data["min_bal"], 0)
    else:
        deduction_amount = data["recharge_on_min_bal"]

    return deduction_amount
