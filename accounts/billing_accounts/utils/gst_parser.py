import re
from abc import ABC, abstractmethod
from typing import Optional, Dict


class GSTDetail:
    def __init__(
        self,
        data: Dict,
        gstin: str,
        legal_name: str,
        pincode: str,
        address: str,
        status: str,
    ):
        self.data = data
        self.gstin = gstin
        self.legal_name = legal_name
        self.pincode = pincode
        self.address = address
        self.status = status

    def __repr__(self):
        return f"<GSTDetail gstin={self.gstin}, status={self.status}, state={self.state_code}, pincode={self.pincode}>"

    def is_active(self) -> bool:
        return self.status.lower() == "active"

    @property
    def pan(self) -> str:
        return self.gstin[2:12]

    @property
    def state_code(self) -> str:
        return self.gstin[:2]

    @property
    def mobile(self) -> str:
        mobile = (
            self.data.get("contact_details", {})
            .get("principal", {})
            .get("mobile", "")
        )
        return mobile or ""


class BaseGSTParser(ABC):
    def __init__(self, data: Dict):
        self.data = data

    @abstractmethod
    def parse(self) -> GSTDetail:
        raise NotImplementedError("Subclass must implement this method")


class SurepassGSTParser(BaseGSTParser):
    def extract_address(self) -> Optional[str]:
        """Extracts address."""
        address = (
            self.data.get("contact_details", {})
            .get("principal", {})
            .get("address", "")
        )
        return address

    def extract_pincode(self, address: str) -> Optional[str]:
        """Extracts 6-digit pincode from address."""
        match = re.search(r"\b\d{6}\b", address)
        return match.group(0) if match else ""

    def parse(self) -> GSTDetail:
        address = self.extract_address()

        return GSTDetail(
            data=self.data,
            gstin=self.data["gstin"],
            legal_name=self.data["legal_name"],
            pincode=self.extract_pincode(address),
            address=address,
            status=self.data["gstin_status"],
        )
