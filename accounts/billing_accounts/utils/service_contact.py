import logging

from accounts.billing_accounts.models import ServiceContacts
from accounts.billing_accounts.constants import (
    BILLING_VERIFICATION_STATE_FRAUD,
    SERVICE_CONTACT_STATUS_ACTIVE,
)
from django.db.models import Q
from django.db.models.query import QuerySet

logger = logging.getLogger(__name__)


def count_service_contacts_by_aadhar(aadhar_card_number):
    """
    Retrieve the count of service contacts by aadhaar no.
    """
    query_set = ServiceContacts.objects.filter(
        aadhar_card_number=aadhar_card_number,
        status=1,
    )
    logger.title("Query #count_service_contacts_by_aadhar").info(
        query_set.query
    )
    return query_set.count()


def count_service_contacts_by_aadhar_in_duration(
    aadhar_card_number, from_date, to_date
):
    """
    Retrieve the count of service contacts by aadhaar in duration
    """
    query_set = ServiceContacts.objects.filter(
        aadhar_card_number=aadhar_card_number,
        created__gte=from_date,
        created__lte=to_date,
        status=1,
    )
    logger.title("Query #count_service_contacts_by_aadhar_in_duration").info(
        query_set.query
    )
    return query_set.count()


def count_fraud_account_by_aadhar(aadhar_card_number):
    """
    Retrieve the count of fraud billing accounts by aadhaar
    """
    query_set = ServiceContacts.objects.filter(
        aadhar_card_number=aadhar_card_number,
        billing_account__verification_state=BILLING_VERIFICATION_STATE_FRAUD,
    )
    logger.title("Query #count_fraud_account_by_aadhar").info(query_set.query)

    return query_set.count()


def count_fraud_account_by_aadhar_in_duration(
    aadhar_card_number, from_date, to_date
):
    """
    Retrieve the count of fraud billing accounts by aadhaar in duration
    """
    query_set = ServiceContacts.objects.filter(
        aadhar_card_number=aadhar_card_number,
        billing_account__verification_state=BILLING_VERIFICATION_STATE_FRAUD,
        billing_account__created__gte=from_date,
        billing_account__created__lte=to_date,
    )
    logger.title("Query #count_fraud_account_by_aadhar_in_duration").info(
        query_set.query
    )
    return query_set.count()


def get_billing_contact_person(billing_account_id: str) -> QuerySet:

    service_contact = (
        ServiceContacts.objects.filter(
            billing_account_id=billing_account_id,
            status=SERVICE_CONTACT_STATUS_ACTIVE,
            contact_type=7,
        )
        .order_by("created")
        .values()
    )

    logger.title("query #get_billing_contact_person").info(
        str(service_contact.query)
    )

    return service_contact.first()
