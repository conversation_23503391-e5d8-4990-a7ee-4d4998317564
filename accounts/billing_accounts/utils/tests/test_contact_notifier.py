from unittest.mock import patch
from django.test import TestCase
from accounts.utils.api_services.bubble import BubbleApiService
from accounts.billing_accounts.utils.contact_notifier import ContactNotifier
from datetime import datetime
from accounts.billing_accounts.tests.factories import (
    ServiceContactsFactory,
    BillingAccountFactory,
)
from accounts.services.tests.factories import ServiceFactory
from django.utils import timezone


class TestContactNotifier(TestCase):
    def setUp(self):
        self.billing = BillingAccountFactory.create(
            ac_number="BAN123", parent_id=None
        )
        ServiceFactory.create(billing_account=self.billing, gsn="comp123")
        self.contact = ServiceContactsFactory.create(
            billing_account=self.billing,
            mobile="**********",
            country_code="+91",
            email="<EMAIL>",
            name="<PERSON>",
            contact_type=1,
            status=1,
        )

    @patch.object(
        BubbleApiService, "sync_contact", return_value={"status": "success"}
    )
    def test_created_success(self, mock_sync_contact):
        """Test contact creation syncs successfully with Bubble API."""
        response = ContactNotifier(self.contact).created()

        self.assertEqual(response, {"status": "success"})
        mock_sync_contact.assert_called_once()

        expected_data = {
            "ban": "BAN123",
            "company_id": "comp123",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "number": "+************",
            "email": "<EMAIL>",
            "name": "John Doe",
            "for": ["billing"],
            "status": "active",
            "action": "created",
        }
        mock_sync_contact.assert_called_with(expected_data)

    @patch.object(
        BubbleApiService, "sync_contact", return_value={"status": "success"}
    )
    def test_created_with_corporate_success(self, mock_sync_contact):
        parent = BillingAccountFactory.create(ac_number="BAN456")
        child_billing_1 = BillingAccountFactory.create(
            ac_number="BAN789", parent=parent
        )
        child_billing_2 = BillingAccountFactory.create(
            ac_number="BAN999", parent=parent
        )
        tz = (
            timezone.get_current_timezone()
        )  # Get the active timezone in Django

        ServiceFactory.create(
            billing_account=child_billing_1,
            gsn="comp456",
            activation_date=datetime(2025, 2, 10, tzinfo=tz),
        )
        ServiceFactory.create(
            billing_account=child_billing_2,
            gsn="comp789",
            activation_date=datetime(2025, 2, 20, tzinfo=tz),
        )
        self.contact = ServiceContactsFactory.create(
            billing_account=parent,
            mobile="**********",
            country_code="+91",
            email="<EMAIL>",
            name="John Doe",
            contact_type=1,
            status=1,
        )
        response = ContactNotifier(self.contact).created()

        self.assertEqual(response, {"status": "success"})
        mock_sync_contact.assert_called_once()

        expected_data = {
            "ban": "BAN456",
            "company_id": "comp456",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "number": "+************",
            "email": "<EMAIL>",
            "name": "John Doe",
            "for": ["billing"],
            "status": "active",
            "action": "created",
        }
        mock_sync_contact.assert_called_with(expected_data)

    @patch.object(
        BubbleApiService, "sync_contact", side_effect=Exception("API Error")
    )
    def test_created_failure(self, mock_sync_contact):
        response = ContactNotifier(self.contact).created()
        self.assertIsNone(response)
        mock_sync_contact.assert_called_once()

    @patch.object(
        BubbleApiService, "sync_contact", return_value={"status": "success"}
    )
    def test_updated_success(self, mock_sync_contact):
        """Test contact update syncs successfully with Bubble API."""
        response = ContactNotifier(self.contact).updated()

        self.assertEqual(response, {"status": "success"})
        mock_sync_contact.assert_called_once()
