from django.test import TestCase
from accounts.billing_accounts.utils.credit_limit_check import (
    calculate_deduction_amount,
    suspend_services,
    auto_deduct_process,
    process_credit_limit_ops,
    credit_limit_check,
)
from accounts.services.tests.factories import (
    ServiceFactory,
    ServiceNumberFactory,
)
from unittest import mock
from accounts.services.constants import SERVICE_STATUS_SUSPENDED
from accounts.users.tests.factories import UserProfileFactory
from django.conf import settings
from accounts.services.exceptions import InvalidServiceException
from accounts.billing_accounts.tests.factories import (
    BillingAccountFactory,
    ServiceContactsFactory,
)
from django.utils import timezone
from accounts.core.tests.factories import CountryFactory
from accounts.products.tests.factories import ProductFactory
from accounts.billing_accounts.exceptions import InvalidBillingAccountException
from accounts.payments.tests.factories import PaymentSubscriptionFactory


class TestCalculateDeductionAmount(TestCase):
    def test_total_outstanding_greater_than_recharge_on_min_bal(self):
        data = {
            "total_billable": 100,
            "pending_usages": 50,
            "pending_other_charges": 20,
            "wallet": 10,
            "min_bal": 30,
            "recharge_on_min_bal": 180,
        }
        result = calculate_deduction_amount(data)
        expected = 190
        self.assertEqual(result, expected)

    def test_total_outstanding_less_than_recharge_on_min_bal(self):
        data = {
            "total_billable": 50,
            "pending_usages": 10,
            "pending_other_charges": 5,
            "wallet": 5,
            "min_bal": 20,
            "recharge_on_min_bal": 100,
        }
        result = calculate_deduction_amount(data)
        expected = 100
        self.assertEqual(result, expected)

    def test_min_bal_is_negative(self):
        data = {
            "total_billable": 100,
            "pending_usages": 50,
            "pending_other_charges": 20,
            "wallet": 10,
            "min_bal": -30,
            "recharge_on_min_bal": 180,
        }
        result = calculate_deduction_amount(data)
        expected = 180
        self.assertEqual(result, expected)

    def test_total_outstanding_equals_recharge_on_min_bal(self):
        data = {
            "total_billable": 90,
            "pending_usages": 10,
            "pending_other_charges": 5,
            "wallet": 5,
            "min_bal": 10,
            "recharge_on_min_bal": 110,
        }
        result = calculate_deduction_amount(data)
        expected = 110
        self.assertEqual(result, expected)


class TestSuspendServices(TestCase):
    def setUp(self):
        self.service = ServiceFactory.create()
        self.billing_account_id = self.service.billing_account.id
        self.service_data = [
            {
                "id": self.service.id,
                "gsn": self.service.gsn,
                "short_code": self.service.product.country.short_code,
            }
        ]
        self.ac_number = self.service.billing_account.ac_number
        self.child_billing_id = None
        self.user_profile = UserProfileFactory(id=settings.CL_CRON)

    @mock.patch(
        "accounts.utils.api_services.account_v1.AccountApiV1.update_memcache"
    )
    @mock.patch("accounts.services.events.ServiceSuspensionEvent.send")
    @mock.patch(
        "accounts.utils.api_services.myoperator.MyOperator.update_permissions"
    )
    def test_suspend_services_success(
        self, mock_operator, mock_sns_event, mock_sync_memcache
    ):
        mock_sync_memcache.return_value = True
        mock_operator.return_value = True
        mock_sns_event.return_value = True

        suspend_services(
            billing_account_id=self.billing_account_id,
            service_data=self.service_data,
            ac_number=self.ac_number,
            child_billing_id=self.child_billing_id,
        )

        # Assertions
        self.service.refresh_from_db()
        self.assertEqual(self.service.status, SERVICE_STATUS_SUSPENDED)
        mock_sync_memcache.assert_called_once()
        mock_operator.assert_called_once()
        mock_sns_event.assert_called_once()

    @mock.patch(
        "accounts.utils.api_services.account_v1.AccountApiV1.update_memcache"
    )
    def test_suspend_services_invalid_service(self, mock_sync_memcache):
        # Test with invalid service ID
        invalid_service_data = [
            {"id": "invalid_id", "gsn": "12345", "short_code": "US"}
        ]

        with self.assertRaises(InvalidServiceException) as context:
            suspend_services(
                billing_account_id=self.billing_account_id,
                service_data=invalid_service_data,
                ac_number=self.ac_number,
                child_billing_id=self.child_billing_id,
            )

        self.assertEqual(str(context.exception), "Invalid Service")
        mock_sync_memcache.assert_not_called()

    @mock.patch("accounts.services.utils.activity_note.add_activity_notes")
    def test_dsuspend_services_exception_add_activity_notes(
        self, mock_add_activity_notes
    ):
        mock_add_activity_notes.side_effect = Exception(
            "Error adding activity notes"
        )
        with self.assertRaises(Exception):
            suspend_services(
                billing_account_id=self.billing_account_id,
                service_data=self.service_data,
                ac_number=self.ac_number,
                child_billing_id=self.child_billing_id,
            )


class TestAutoDeductProcess(TestCase):
    def setUp(self):
        self.service = ServiceFactory.create()
        self.billing_account_id = self.service.billing_account.id
        self.service_data = [
            {
                "id": self.service.id,
                "gsn": self.service.gsn,
                "short_code": self.service.product.country.short_code,
            }
        ]
        self.ac_number = self.service.billing_account.ac_number
        self.child_billing_id = None
        self.user_profile = UserProfileFactory(id=settings.CL_CRON)
        self.data = {
            "billing_account_id": self.service.billing_account.id,
            "ac_number": self.service.billing_account.ac_number,
            "credit_limit": 1000,
            "child_billing_id": None,
        }
        self.deduction_amount = 500
        self.pending = 200
        self.tax = {"tax_total": 50}
        self.contact_person = {"name": "test", "email": "<EMAIL>"}

    @mock.patch(
        "accounts.utils.api_services.account_v1.AccountApiV1.auto_deduct"
    )
    @mock.patch(
        "accounts.billing_accounts.utils.credit_limit_check.suspend_services"
    )
    def test_auto_deduct_process_success(
        self, mock_suspend_services, mock_auto_deduct
    ):
        mock_auto_deduct.return_value = {
            "status": "success",
            "data": {
                "subscription_gateway": "stripe",
                "recurring_resposne": {"payment_id": "payment123"},
            },
        }

        # Call the function
        result = auto_deduct_process(
            data=self.data,
            service_data=self.service_data,
            deduction_amount=self.deduction_amount,
            pending=self.pending,
            tax=self.tax,
            contact_person=self.contact_person,
        )

        # Assertions
        self.assertTrue(result)
        mock_auto_deduct.assert_called_once()
        mock_suspend_services.assert_not_called()

    @mock.patch(
        "accounts.utils.api_services.account_v1.AccountApiV1.auto_deduct"
    )
    @mock.patch(
        "accounts.billing_accounts.utils.credit_limit_check.suspend_services"
    )
    def test_auto_deduct_process_error_and_suspend(
        self, mock_suspend_services, mock_auto_deduct
    ):
        mock_auto_deduct.return_value = {
            "status": "error",
            "data": {
                "subscription_gateway": "stripe",
                "recurring_resposne": {"payment_id": "payment123"},
            },
        }

        # Call the function
        result = auto_deduct_process(
            data=self.data,
            service_data=self.service_data,
            deduction_amount=self.deduction_amount,
            pending=-1100,
            tax=self.tax,
            contact_person=self.contact_person,
        )

        # Assertions
        self.assertTrue(result)
        mock_auto_deduct.assert_called_once()
        mock_suspend_services.assert_called_once()


class TestProcessCreditLimitOps(TestCase):
    def setUp(self):
        self.user_profile = UserProfileFactory.create()
        self.country = CountryFactory.create(country="USA", short_code="US")
        self.product = ProductFactory.create(country=self.country)
        self.billing_account = BillingAccountFactory.create(
            account_manager=self.user_profile, credit_limit=50
        )
        self.service = ServiceFactory.create(
            billing_account=self.billing_account, product=self.product
        )
        self.service_contact = ServiceContactsFactory.create(
            billing_account=self.billing_account, contact_type=7
        )
        self.service_data = [
            {
                "id": self.service.id,
                "gsn": self.service.gsn,
                "short_code": self.service.product.country.short_code,
                "billing_account_id": self.billing_account.id,
            }
        ]
        self.service_number = ServiceNumberFactory.create(
            service=self.service,
            service_number="1234",
        )
        self.data = {
            "from_date": timezone.now(),
            "to_date": timezone.now(),
            "child_billing_id": None,
            "billing_account_id": self.billing_account.id,
            "is_subscribed": True,
            "wallet": 300,
            "pending_usages": 100,
            "pending_other_charges": 100,
            "total_billable": 300,
            "service_number": self.service_number.service_number,
            "discount": 0,
            "min_bal": 250,
        }

    def test_condition_failed(self):
        data = {
            "wallet": 500,
            "total_billable": 300,
            "pending_usages": 100,
            "min_bal": 50,
            "billing_account_id": 1,
        }
        service_data = {}

        result = process_credit_limit_ops(data, service_data)
        self.assertFalse(result)

    @mock.patch(
        "accounts.utils.api_services.account_v1.AccountApiV1.generate_recharge_link"
    )
    def test_bypass_indian_timezone(self, mock_generate_recharge_link):
        country = CountryFactory.create(country="India", short_code="IN")
        product = ProductFactory.create(country=country)
        billing_account = BillingAccountFactory.create(
            account_manager=self.user_profile
        )
        service = ServiceFactory.create(
            billing_account=billing_account, product=product
        )
        ServiceContactsFactory.create(
            billing_account=billing_account, contact_type=7
        )
        service_data = [
            {
                "id": service.id,
                "gsn": service.gsn,
                "short_code": service.product.country.short_code,
                "billing_account_id": billing_account.id,
            }
        ]
        service_number = ServiceNumberFactory.create(
            service=service,
            service_number="1234",
        )
        data = {
            "from_date": timezone.now(),
            "to_date": timezone.now(),
            "child_billing_id": None,
            "billing_account_id": billing_account.id,
            "is_subscribed": True,
            "wallet": 500,
            "pending_usages": 100,
            "pending_other_charges": 100,
            "total_billable": 300,
            "service_number": service_number.service_number,
            "discount": 0,
            "min_bal": 250,
        }

        result = process_credit_limit_ops(data, service_data)
        self.assertTrue(result)
        mock_generate_recharge_link.assert_called_once()

    @mock.patch(
        "accounts.billing_accounts.utils.credit_limit_check.auto_deduct_process"
    )
    @mock.patch(
        "accounts.utils.api_services.account_v1.AccountApiV1.generate_recharge_link"
    )
    def test_auto_deduct_process_success(
        self, mock_generate_recharge_link, mock_auto_deduct_process
    ):
        mock_auto_deduct_process.return_value = True
        result = process_credit_limit_ops(self.data, self.service_data)
        self.assertTrue(result)
        mock_generate_recharge_link.assert_called_once()
        mock_auto_deduct_process.assert_called_once()

    @mock.patch(
        "accounts.billing_accounts.utils.credit_limit_check.handle_instant_suspension"
    )
    @mock.patch(
        "accounts.utils.api_services.account_v1.AccountApiV1.generate_recharge_link"
    )
    def test_handle_instant_suspension_success(
        self, mock_generate_recharge_link, mock_instant_suspension
    ):
        mock_instant_suspension.return_value = True
        self.data["is_subscribed"] = False
        result = process_credit_limit_ops(self.data, self.service_data)
        self.assertTrue(result)
        mock_generate_recharge_link.assert_called_once()
        mock_instant_suspension.assert_called_once()

    @mock.patch(
        "accounts.billing_accounts.utils.credit_limit_check.handle_low_balance"
    )
    @mock.patch(
        "accounts.utils.api_services.account_v1.AccountApiV1.generate_recharge_link"
    )
    def test_handle_low_balance_success(
        self, mock_generate_recharge_link, mock_handle_low_balance
    ):
        mock_handle_low_balance.return_value = True
        self.data["is_subscribed"] = False
        self.data["wallet"] = 500
        result = process_credit_limit_ops(self.data, self.service_data)
        self.assertTrue(result)
        mock_generate_recharge_link.assert_called_once()
        mock_handle_low_balance.assert_called_once()


class TestCreditLimitCheck(TestCase):
    def setUp(self):
        self.user_profile = UserProfileFactory.create()
        self.product = ProductFactory.create(id=settings.HEYO_PRODUCT_ID)
        self.billing_account = BillingAccountFactory.create(
            account_manager=self.user_profile, credit_limit=50
        )
        self.service = ServiceFactory.create(
            billing_account=self.billing_account,
            product=self.product,
            live_status=1,
            status=1,
        )
        self.service_contact = ServiceContactsFactory.create(
            billing_account=self.billing_account, contact_type=7
        )

        self.service_data = [
            {
                "id": self.service.id,
                "gsn": self.service.gsn,
                "short_code": self.service.product.country.short_code,
                "billing_account_id": self.billing_account.id,
            }
        ]
        self.service_number = ServiceNumberFactory.create(
            service=self.service, service_number="1234", status=1
        )
        self.usage_data = {
            "current_usages": 500,
            "pending_rental": 10,
            "other_charges": 5,
            "service_number_cost": 15,
            "advance": 200,
            "pending_usages": 50,
            "discount": 20,
        }
        PaymentSubscriptionFactory.create(
            billing_account=self.billing_account, gateway="stripe"
        )

    def test_invalid_billing_account(self):

        with self.assertRaises(InvalidBillingAccountException):
            credit_limit_check("2312556")

    def test_credit_limit_check_no_services(self):
        bac = BillingAccountFactory.create()
        result = credit_limit_check(bac.id)
        self.assertFalse(result)

    @mock.patch("accounts.services.utils.pending_usage.calculate_usage")
    @mock.patch(
        "accounts.billing_accounts.utils.credit_limit_check.process_credit_limit_ops"
    )
    def test_valid_credit_limit_check(
        self, mock_process_credit_limit_ops, mock_calculate_usage
    ):
        mock_calculate_usage.return_value = self.usage_data
        mock_process_credit_limit_ops.return_value = True
        result = credit_limit_check(self.billing_account.id)
        self.assertTrue(result)
        mock_process_credit_limit_ops.assert_called_once()
