from django.test import TestCase
import pytest

from accounts.billing_accounts.tests.factories import (
    BillingAccountFactory,
)
from accounts.billing_accounts.exceptions import (
    InvalidBillingAccountException,
    AlreadyFraudMarkedException,
)
from accounts.billing_accounts.utils.verification_state import (
    mark_as_fraud,
)
import unittest.mock as mock
from accounts.services.exceptions import InvalidServiceException
from accounts.services.tests.factories import (
    ServiceFactory,
    ServiceNumberFactory,
    ServicePackageFactory,
)
from accounts.users.tests.factories import UserProfileFactory
from accounts.billing_accounts.models import BillingAccounts
from accounts.services.models import Services
from accounts.services.models import ActivityNotes
from django.conf import settings


class TestVerificationState(TestCase):
    def setUp(self):
        user = UserProfileFactory()
        self.user_id = user.id
        pass

    def test_mark_as_fraud_invalid_billing_id(self):
        with pytest.raises(InvalidBillingAccountException):
            mark_as_fraud("1234", self.user_id)

    def test_mark_as_fraud_already_marked_fraud(self):
        billing = BillingAccountFactory.create(verification_state=5)
        with pytest.raises(AlreadyFraudMarkedException):
            mark_as_fraud(billing.id, self.user_id)

    def test_mark_as_fraud_invalid_service(self):
        billing = BillingAccountFactory.create()
        with pytest.raises(InvalidServiceException):
            mark_as_fraud(billing.id, self.user_id)

    @mock.patch(
        "accounts.utils.api_services.account_v1.AccountApiV1.update_memcache"
    )
    @mock.patch(
        "accounts.utils.api_services.number_system.NumberSystem.deactivate_service_number"
    )
    @mock.patch(
        "accounts.utils.api_services.myoperator.MyOperator.deactivate_company"
    )
    @mock.patch("accounts.services.events.ServiceDeactivationEvent.send")
    @mock.patch("accounts.billing_accounts.events.FlaggedFraudEvent.send")
    def test_mark_as_fraud_success(
        self,
        mock_flagged_fraud_event,
        mock_service_deactivation_event,
        mock_deactivate_company,
        mock_deactivate_service_number,
        mock_update_memcache,
    ):
        mock_flagged_fraud_event.return_value = True
        mock_service_deactivation_event.return_value = True
        mock_deactivate_company.return_value = True
        mock_deactivate_service_number.return_value = True
        mock_update_memcache.return_value = True
        billing = BillingAccountFactory.create(verification_state=1, status=1)
        service = ServiceFactory.create(
            billing_account=billing,
            status=1,
            live_status=1,
        )
        ServiceNumberFactory.create(
            service=service,
            service_number="1234",
        )
        ServicePackageFactory.create(
            service=service,
        )
        mark_as_fraud(billing.id, self.user_id)
        fraud_billing = BillingAccounts.objects.get(id=billing.id)
        fraud_service = Services.objects.get(id=service.id)
        activity_notes = ActivityNotes.objects.filter(
            billing_account_id=billing.id,
            service_id=service.id,
            note=settings.FRAUD_NOTE,
            created_by=self.user_id,
        ).first()
        deactivation_activity_notes = ActivityNotes.objects.filter(
            billing_account_id=billing.id,
            service_id=service.id,
            note=settings.DEACTIVATE_NOTE,
            created_by=self.user_id,
        ).first()

        mock_update_memcache.assert_called_once()
        mock_deactivate_service_number.assert_called_once()
        mock_deactivate_company.assert_called_once()
        mock_service_deactivation_event.assert_called_once()
        mock_flagged_fraud_event.assert_called_once()
        assert fraud_billing.verification_state == 5
        assert fraud_billing.status == 0
        assert fraud_service.status == 0
        assert activity_notes.billing_account_id == billing.id
        assert deactivation_activity_notes.billing_account_id == billing.id

    @mock.patch("accounts.billing_accounts.events.FlaggedFraudEvent.send")
    def test_mark_as_fraud_with_service_already_inactive(
        self,
        mock_flagged_fraud_event,
    ):
        mock_flagged_fraud_event.return_value = True
        billing = BillingAccountFactory.create(verification_state=1, status=1)
        service = ServiceFactory.create(
            billing_account=billing,
            status=0
        )
        mark_as_fraud(billing.id, self.user_id)
        fraud_billing = BillingAccounts.objects.get(id=billing.id)
        fraud_service = Services.objects.get(id=service.id)
        activity_notes = ActivityNotes.objects.filter(
            billing_account_id=billing.id,
            service_id=service.id,
            note=settings.FRAUD_NOTE,
            created_by=self.user_id,
        ).first()

        mock_flagged_fraud_event.assert_called_once()
        assert fraud_billing.verification_state == 5
        assert fraud_billing.status == 0
        assert fraud_service.status == 0
        assert activity_notes.billing_account_id == billing.id
