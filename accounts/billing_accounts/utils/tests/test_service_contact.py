from django.test import TestCase
from django.utils import timezone as tz

from datetime import datetime

from accounts.billing_accounts.tests.factories import (
    BillingAccountFactory,
    MonthlyStatementFactory,
)
from accounts.billing_accounts.utils.service_contact import (
    count_fraud_account_by_aadhar,
    count_fraud_account_by_aadhar_in_duration,
    count_service_contacts_by_aadhar,
    count_service_contacts_by_aadhar_in_duration,
)
from accounts.billing_accounts.tests.factories import ServiceContactsFactory


class TestServiceContact(TestCase):
    def setUp(self):
        self.aadhar_number = "************"
        self.from_date = tz.now() - tz.timedelta(days=7)
        self.to_date = tz.now() + tz.timedelta(days=1)
        ServiceContactsFactory.create_batch(
            3,
            aadhar_card_number=self.aadhar_number,
            status=1,
        )

    def test_count_fraud_account_by_aadhar(self):
        billing_account = BillingAccountFactory.create(
            status=0, verification_state=5
        )
        ServiceContactsFactory.create_batch(
            3,
            aadhar_card_number="************",
            billing_account=billing_account,
        )
        assert count_fraud_account_by_aadhar("************") == 3
        assert count_fraud_account_by_aadhar("111") == 0

    def test_count_fraud_account_by_aadhar_in_duration(self):
        billing_account = BillingAccountFactory.create(
            status=0, verification_state=5
        )
        ServiceContactsFactory.create_batch(
            5,
            aadhar_card_number="************",
            billing_account=billing_account,
        )
        assert (
            count_fraud_account_by_aadhar_in_duration(
                "************", self.from_date, self.to_date
            )
            == 5
        )
        assert (
            count_fraud_account_by_aadhar_in_duration(
                "2222", self.from_date, self.to_date
            )
            == 0
        )

    def test_count_service_contacts_by_aadhar_positive(self):
        count = count_service_contacts_by_aadhar(self.aadhar_number)
        self.assertEqual(count, 3)

    def test_count_service_contacts_by_aadhar_negative(self):
        count = count_service_contacts_by_aadhar("************")
        self.assertEqual(count, 0)

    def test_count_service_contacts_by_aadhar_in_duration_positive(self):
        count = count_service_contacts_by_aadhar_in_duration(
            self.aadhar_number, self.from_date, self.to_date
        )
        self.assertEqual(count, 3)

    def test_count_service_contacts_by_aadhar_in_duration_negative(self):
        from_date = self.to_date
        to_date = self.to_date + tz.timedelta(days=1)
        count = count_service_contacts_by_aadhar_in_duration(
            self.aadhar_number, from_date, to_date
        )
        self.assertEqual(count, 0)
