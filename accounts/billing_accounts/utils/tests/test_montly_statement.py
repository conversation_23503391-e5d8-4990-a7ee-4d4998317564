from datetime import timedelta
from unittest import mock

from django.test import TestCase
from django.utils import timezone as tz

from freezegun import freeze_time

from accounts.billing_accounts.tests.factories import (
    BillingAccountFactory,
    MonthlyStatementFactory,
)
from accounts.billing_accounts.utils.monthly_statement import (
    fetch_monthly_statement,
    is_last_statement_missing,
)
from accounts.core.tests.factories import CountryFactory
from accounts.products.tests.factories import OrgTypesFactory, ProductFactory
from accounts.services.tests.factories import ServiceFactory


class TestMontlyStatement(TestCase):
    def setUp(self):
        self.activation_date = tz.now() - tz.timedelta(days=30)
        self.country = CountryFactory.create()

        self.product = ProductFactory.create(
            country=self.country,
        )
        self.billing_account = BillingAccountFactory.create()

        self.service = ServiceFactory.create(
            product=self.product,
            billing_account=self.billing_account,
            country=self.country,
            churn_date=self.activation_date,
            expiry_date=(tz.now() + tz.timedelta(days=365)),
            status=0,
            live_status=1,
            renew_cycle=12,
            activation_date=self.activation_date,
        )

    @freeze_time("2023-04-01 18:35:00")
    def test_monthly_statement_generated_true(self):
        tzinfo = tz.now().tzinfo
        monthly_statement = MonthlyStatementFactory.create(
            billing_account=self.billing_account,
            type="auto",
            from_date=tz.datetime(2023, 3, 1, 18, 30, 0, tzinfo=tzinfo),
            to_date=tz.datetime(2023, 4, 1, 18, 29, 59, tzinfo=tzinfo),
            no_of_months=1,
            service_rent=3,
            payable_amount=100,
        )

        before_28_days = tz.datetime(2023, 4, 28, tzinfo=tzinfo) - timedelta(
            days=28
        )
        result = fetch_monthly_statement(
            self.billing_account.id, before_28_days
        )
        assert result[0]["id"] == monthly_statement.id

        before_28_days = tz.datetime(2023, 4, 29, tzinfo=tzinfo) - timedelta(
            days=28
        )
        result2 = fetch_monthly_statement(
            self.billing_account.id, before_28_days
        )
        assert result2[0]["id"] == monthly_statement.id

    @freeze_time("2023-04-01 18:35:00")
    def test_monthly_statement_generated_false(self):
        tzinfo = tz.now().tzinfo
        MonthlyStatementFactory.create(
            billing_account=self.billing_account,
            type="auto",
            from_date=tz.datetime(2023, 3, 1, 18, 30, 0, tzinfo=tzinfo),
            to_date=tz.datetime(2023, 4, 1, 18, 29, 59, tzinfo=tzinfo),
            no_of_months=1,
            service_rent=3,
            payable_amount=100,
        )

        before_28_days = tz.datetime(2023, 4, 30, tzinfo=tzinfo) - timedelta(
            days=28
        )
        assert (
            fetch_monthly_statement(self.billing_account.id, before_28_days)
            is False
        )

    @mock.patch(
        "accounts.billing_accounts.utils.monthly_statement.fetch_monthly_statement"
    )
    def test_is_last_statement_missing_true(self, mock_monthly_statement):
        mock_monthly_statement.return_value = False
        current_date = tz.now()

        assert (
            is_last_statement_missing(
                self.billing_account.id, self.activation_date, current_date
            )
            is True
        )

    @mock.patch(
        "accounts.billing_accounts.utils.monthly_statement.fetch_monthly_statement"
    )
    def test_is_last_statement_missing_false(self, mock_monthly_statement):
        mock_monthly_statement.return_value = True
        current_date = tz.now()

        assert (
            is_last_statement_missing(
                self.billing_account.id, self.activation_date, current_date
            )
            is False
        )
