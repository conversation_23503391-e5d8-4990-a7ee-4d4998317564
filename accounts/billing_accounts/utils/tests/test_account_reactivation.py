import json
from unittest import mock

from django.conf import settings
from django.test import TestCase, override_settings
from django.utils import timezone

import pytest
import responses

from accounts.billing_accounts.exceptions import (
    AccountActivationFailedException,
    FraudBillingAccountException,
    InvalidBillingAccountException,
)
from accounts.billing_accounts.tests.factories import (
    BillingAccountFactory,
    MonthlyStatementFactory,
)
from accounts.billing_accounts.utils.account_reactivation import (
    reactivate_account,
)
from accounts.core.tests.factories import CountryFactory
from accounts.products.tests.factories import ProductFactory
from accounts.services.exceptions import InvalidServiceException
from accounts.services.models import ServiceNumbers, Services
from accounts.users.tests.factories import UserProfileFactory
from accounts.services.tests.factories import (
    ActivityNoteFactory,
    ServiceFactory,
    ServiceNumberFactory,
)


class TestAccountReactivate(TestCase):
    def setUp(self):
        self.activation_date = timezone.now()
        self.service_number = "**********"
        self.country = CountryFactory.create()
        self.product = ProductFactory.create(
            country=self.country,
        )

        self.billing_account = BillingAccountFactory.create()
        self.service = ServiceFactory.create(
            product=self.product,
            billing_account=self.billing_account,
            country=self.country,
            churn_date=self.activation_date,
            expiry_date=(timezone.now() + timezone.timedelta(days=365)),
            status=0,
            live_status=1,
            renew_cycle=12,
            activation_date=self.activation_date,
        )

        self.MR_CRON_USER = UserProfileFactory(name="MR_CRON")
        self.AS_CRON_USER = UserProfileFactory(name="AS_CRON")

        # we need to overide the setting values to read and write in activity_notes
        settings.MR_CRON = self.MR_CRON_USER.id
        settings.AS_CRON = self.AS_CRON_USER.id

        ServiceNumberFactory.create(
            service=self.service,
            service_number=self.service_number,
            number_type=2,
            number_cost=100,
            is_paid=1,
            status=0,
        )

    @mock.patch("accounts.utils.events.sns.SnsEvent.send")
    @responses.activate
    def test_reactivation_success(self, mock_event):
        mock_event.return_value = True
        current_date = timezone.now()

        ActivityNoteFactory.create(
            billing_account=self.billing_account,
            service=self.service,
            note=settings.DEACTIVATE_NOTE,
            created_by=self.MR_CRON_USER,
            created=self.activation_date,
            renew_retain=None,
        )

        MonthlyStatementFactory.create(
            billing_account=self.billing_account,
            created=current_date,
            from_date=current_date,
            to_date=timezone.now() + timezone.timedelta(days=30),
            no_of_months=1,
            service_rent=3,
            payable_amount=100,
        )

        expected_dict = json.dumps(
            {
                "status": "success",
                "data_str": {
                    "number_status": "service_deactive",
                    "number_type": "2",
                    "number_cost": "0",
                },
                "code": 200,
            }
        )
        responses.add(
            responses.POST,
            f"{settings.NUMBER_SYSTEM_API_URL}account_apis/get_number_details?api_token={settings.NUMBER_SYSTEM_API_TOKEN}",
            body=expected_dict,
            status=200,
        )
        responses.add(
            responses.POST,
            f"{settings.NUMBER_SYSTEM_API_URL}account_apis/make_service_live?api_token={settings.NUMBER_SYSTEM_API_TOKEN}",
            body=json.dumps(
                {
                    "status": "success",
                    "dnid1": "1234",
                    "dnid2": "dummy233",
                    "service_number_new": "**********",
                    "service_number_old": "**********",
                    "company_id_myop": "1",
                    "message": "Number updated successfully",
                }
            ),
            status=200,
        )
        responses.add(
            responses.POST,
            f"{settings.MYOPERATOR_API_URL}updatednid",
            body=json.dumps({"status": "success"}),
            status=200,
        )
        responses.add(
            responses.POST,
            f"{settings.ACCOUNT_API_V1_HOST}external/update_memcache_account",
            body=json.dumps({"status": "success"}),
            status=200,
        )
        responses.add(
            responses.POST,
            f"{settings.MYOPERATOR_API_URL}feature/on_update",
            body=json.dumps({"status": "success"}),
            status=200,
        )
        reactivate_account(self.billing_account.id)

        service = Services.objects.get(
            billing_account_id=self.billing_account.id
        )
        assert service.status == 1
        assert service.live_status == self.service.live_status
        assert service.activation_date == self.service.activation_date
        assert service.expiry_date == self.service.expiry_date

        # test old service number status
        old_service_number = ServiceNumbers.objects.filter(
            service_id=service.id, status=0
        ).first()
        assert (
            old_service_number.service_number == f"{self.service_number}_free"
        )

        # test new service number status
        service_number = ServiceNumbers.objects.filter(
            service_id=self.service.id, status=1
        ).first()
        assert service_number.service_number == self.service_number
        assert service_number.status == 1

    def test_reactivation_with_invalid_billing_id(self):
        with pytest.raises(InvalidBillingAccountException):
            reactivate_account("456")

    def test_reactivation_with_invalid_service_id(self):
        billing_account = BillingAccountFactory.create()

        with pytest.raises(InvalidServiceException):
            reactivate_account(billing_account.id)

    def test_reactivation_with_fraud_account(self):
        billing_account = BillingAccountFactory.create(
            verification_state=5
        )

        service = ServiceFactory.create(
            product=self.product,
            billing_account=billing_account,
            country=self.country,
            activation_date=self.activation_date,
            churn_date=self.activation_date,
            expiry_date=(timezone.now() + timezone.timedelta(days=365)),
            status=0,
            live_status=1,
            user_profile=None,
        )

        ActivityNoteFactory.create(
            billing_account=billing_account,
            service=service,
            note=settings.DEACTIVATE_NOTE,
        )

        with pytest.raises(FraudBillingAccountException):
            reactivate_account(billing_account.id)

    def test_reactivation_with_demo_account(self):
        billing_account = BillingAccountFactory.create(status=0)

        service = ServiceFactory.create(
            product=self.product,
            billing_account=billing_account,
            country=self.country,
            activation_date=self.activation_date,
            churn_date=self.activation_date,
            expiry_date=(timezone.now() + timezone.timedelta(days=30)),
            status=0,
            live_status=3,
            user_profile=None,
        )

        ActivityNoteFactory.create(
            billing_account=billing_account,
            service=service,
            note=settings.DEACTIVATE_NOTE,
        )

        with pytest.raises(InvalidServiceException):
            reactivate_account(billing_account.id)

    def test_reactivation_with_deactivated_by_user(self):
        billing_account = BillingAccountFactory.create(status=1)

        service = ServiceFactory.create(
            product=self.product,
            billing_account=billing_account,
            country=self.country,
            activation_date=self.activation_date,
            churn_date=self.activation_date,
            expiry_date=(timezone.now() + timezone.timedelta(days=30)),
            status=0,
            live_status=1,
            user_profile=None,
        )
        ServiceNumberFactory.create(
            service=service,
            status=0,
        )

        ActivityNoteFactory.create(
            billing_account=billing_account,
            service=service,
            note=settings.DEACTIVATE_NOTE,
        )
        with pytest.raises(AccountActivationFailedException):
            reactivate_account(billing_account.id)
