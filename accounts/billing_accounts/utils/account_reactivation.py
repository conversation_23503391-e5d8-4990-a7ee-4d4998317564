import logging

from django.conf import settings
from django.db import transaction
from django.utils import timezone

from accounts.billing_accounts import constants
from accounts.billing_accounts.exceptions import (
    AccountActivationFailedException,
    FraudBillingAccountException,
    InvalidBillingAccountException,
)
from accounts.billing_accounts.models import BillingAccounts
from accounts.billing_accounts.utils.monthly_statement import (
    is_last_statement_missing,
)
from accounts.products.utils import get_country_code
from accounts.services import constants as service_constants
from accounts.services.exceptions import InvalidServiceException
from accounts.services.models import Services
from accounts.services.utils.activity_note import (
    add_activity_notes,
    get_activity_note,
)
from accounts.services.utils.service import sync_memcache
from accounts.services.utils.service_number import (
    add_new_service_number,
    free_old_service_numbers,
    get_old_service_number_details,
)
from accounts.services.utils.service_rental import is_pending_rental
from accounts.utils.api_services.myoperator import MyOperator
from accounts.utils.api_services.number_system import NumberSystem
from accounts.services.events import (
    ServiceActivationEvent,
    UpdatePermissionEvent,
)

logger = logging.getLogger(__name__)


def reactivate_account(billing_account_id):

    # check if billing account is active
    billing_account = BillingAccounts.objects.filter(
        id=billing_account_id
    ).first()
    if billing_account is None:
        raise InvalidBillingAccountException()

    service = Services.live.filter(
        billing_account_id=billing_account.id,
        status=service_constants.SERVICE_STATUS_INACTIVE,
    ).first()

    if not service:
        raise InvalidServiceException()

    # fraud account check
    if billing_account.is_fraud():
        raise FraudBillingAccountException()

    # get service number
    old_service_number_details = get_old_service_number_details(service.id)
    if not old_service_number_details:
        raise InvalidServiceException("old service number not found")
    service_number = old_service_number_details.service_number.replace(
        "_free", ""
    )

    # check if account is blocked or deactivated by internal user
    if not get_activity_note(
        service.id,
        settings.DEACTIVATE_NOTE,
        created_by=[settings.MR_CRON, settings.ST_CRON],
        activity_date=service.churn_date,
    ):
        raise AccountActivationFailedException("deactivated by internal user")

    # check if there is any pending rental
    if is_pending_rental(service.id):
        raise AccountActivationFailedException("Service has pending rental")

    # check if last month statement is missing
    if is_last_statement_missing(
        billing_account.id, service.activation_date, timezone.now()
    ):
        raise AccountActivationFailedException("Last statement missing")

    # get number details for service number
    number_details = NumberSystem().get_number_details(
        service.gsn, service_number
    )
    if "number_status" not in number_details:
        raise AccountActivationFailedException(
            f"number system error: {str(number_details)}"
        )

    if number_details["number_status"] != "service_deactive":
        raise AccountActivationFailedException(
            f"number status is {number_details['number_status']}"
        )

    with transaction.atomic():
        # update service status to active
        service.activate()

        add_activity_notes(
            billing_account.id,
            service.id,
            settings.REACTIVATE_NOTE,
            settings.AS_CRON,
        )

        # update status old service number
        free_old_service_numbers(service.id)

        add_new_service_number(
            service_id=service.id,
            service_number=service_number,
            number_type=number_details.get("number_type", 1),
            number_cost=number_details.get("number_cost", 0),
            is_live=1,
            swapped_date=timezone.now(),
        )

        # update number system
        did_numbersystem = NumberSystem().make_number_live(
            service_number,
            service.gsn,
            service_number_old=service_number,
            service_id=service.id,
            live_status=service.live_status,
            activation_date=service.activation_date.strftime(
                "%Y-%m-%d %H:%M:%S"
            ),
        )

    country_short_code = get_country_code(service.product_id)
    # update myoperator
    MyOperator().update_service_number(
        service.gsn,
        service_number,
        live_status=service.live_status,
        number_property=number_details["number_type"],
        expiry=service.expiry_date.strftime("%Y-%m-%d %H:%M:%S"),
        dnid1=did_numbersystem["dnid1"],
        dnid2=did_numbersystem["dnid2"],
        country_code=country_short_code,
    )

    sync_memcache(service.id)  # update memcache

    MyOperator().update_permissions({"gsn": service.gsn}, country_short_code)
    UpdatePermissionEvent().company_id(service.gsn).send()
    ServiceActivationEvent().company_id(service.gsn).send()
    logger.title("Account Reactivated!!").info(
        {"billing_account_id": billing_account.id}
    )
