from django.conf import settings

from accounts.utils.events.sns import SnsEvent
from accounts.billing_accounts.utils.billing_account import (
    get_product_short_code_from_ban,
)


class UpcomingServiceRenewalEvent(SnsEvent):
    EVENT_ACTION = "upcoming_service_renewal"
    REQUIRED_PARAMS = (
        "ban",
        "billing_account_id",
    )

    def __init__(self):
        self.arn = settings.ACCOUNTS_SNS_ARN
        super().__init__()

    def ban(self, ban):
        self.set_data("ban", ban)
        return self

    def billing_account_id(self, billing_account_id):
        self.service_type = get_product_short_code_from_ban(billing_account_id)
        self.set_data("billing_account_id", billing_account_id)
        return self


class MonthlyStatementGeneratedEvent(SnsEvent):
    EVENT_ACTION = "monthly_statement_generated"
    REQUIRED_PARAMS = (
        "ban",
        "billing_account_id",
    )

    def __init__(self):
        self.arn = settings.ACCOUNTS_SNS_ARN
        super().__init__()

    def ban(self, ban):
        self.set_data("ban", ban)
        return self

    def billing_account_id(self, billing_account_id):
        self.service_type = get_product_short_code_from_ban(billing_account_id)
        self.set_data("billing_account_id", billing_account_id)
        return self


class ServiceRentalPendingEvent(SnsEvent):
    EVENT_ACTION = "service_rental_pending"
    REQUIRED_PARAMS = (
        "ban",
        "billing_account_id",
    )

    def __init__(self):
        self.arn = settings.ACCOUNTS_SNS_ARN
        super().__init__()

    def ban(self, ban):
        self.set_data("ban", ban)
        return self

    def billing_account_id(self, billing_account_id):
        self.service_type = get_product_short_code_from_ban(billing_account_id)
        self.set_data("billing_account_id", billing_account_id)
        return self


class FlaggedFraudEvent(SnsEvent):
    EVENT_ACTION = "flagged_fraud"
    REQUIRED_PARAMS = (
        "ban",
        "billing_account_id",
    )

    def __init__(self):
        self.arn = settings.ACCOUNTS_SNS_ARN
        super().__init__()

    def ban(self, ban):
        self.set_data("ban", ban)
        return self

    def billing_account_id(self, billing_account_id):
        self.service_type = get_product_short_code_from_ban(billing_account_id)
        self.set_data("billing_account_id", billing_account_id)
        return self
