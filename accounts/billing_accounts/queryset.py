import typing as t
from decimal import Decimal
from django.db import models
from django.db.models import DecimalField, Sum
from django.db.models.functions import Coalesce

from accounts.billing_accounts.enums import (
    BillingStatusEnum,
    BillingAccountCreditStatusEnum,
    DiscountBucketStatusEnum,
)
from . import constants

if t.TYPE_CHECKING:
    from accounts.billing_accounts.models import BillingAccounts


class BillingAccountQuerySet(models.QuerySet):
    def ban(self, ac_number):
        return self.filter(ac_number=ac_number)

    def pilot_ban(self, parent_id):
        """
        return first ban as pilot account of a corporate account
        """
        return (
            self.filter(parent_id=parent_id)
            .filter(status=constants.BILLING_STATUS_ACTIVE)
            .order_by("created")
            .first()
        )

    def parent_ban(self, parent_id):
        return self.filter(parent_id=parent_id)


class BillingAccountCreditQuerySet(models.QuerySet):
    def billing_account(self, billing_account: "BillingAccounts"):
        return self.filter(billing_account=billing_account)

    def credit_amount(self) -> Decimal:
        qs = self.filter(status=BillingAccountCreditStatusEnum.ACTIVE.value)
        result = qs.annotate(
            amount=Coalesce("credit_amount", 0, output_field=DecimalField())
        ).first()
        return result.amount if result else Decimal(0)
