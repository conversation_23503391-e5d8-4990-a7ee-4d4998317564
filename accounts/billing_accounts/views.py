import logging
from rest_framework import status, generics, filters
from django_filters import rest_framework as django_filters
from rest_framework.response import Response
from rest_framework.serializers import ValidationError
from rest_framework.exceptions import NotFound
from accounts.exceptions import BaseException
from accounts.billing_accounts.exceptions import InvalidBillingAccountException
from accounts.services.exceptions import InvalidServiceException

from accounts import error_codes
from accounts.billing_accounts.tasks import (
    account_reactivation,
    update_ban_credit_limit,
)
from accounts.generics import SnsHandlerView
from accounts.billing_accounts.serializers import (
    MarkFraudSerializer,
    BillingSerializer,
    GstUpdateSerializer,
    SearchFilterSerializer,
    ContactSerializer,
)
from accounts.billing_accounts.utils.verification_state import mark_as_fraud
from accounts.billing_accounts.utils.billing_account import (
    log_edited_fields,
    fetch_billing_account_list,
)
from accounts.users.models import UserProfiles
from accounts.billing_accounts.models import BillingAccounts
from django.forms.models import model_to_dict
from accounts.billing_accounts.models import ServiceContacts
from accounts.billing_accounts.filters import ContactsFilter
from accounts.billing_accounts.utils.contact_notifier import ContactNotifier
from django.db import transaction

logger = logging.getLogger(__name__)


class BillingAccountListAPIView(generics.ListAPIView):
    serializer_class = BillingSerializer

    def get_queryset(self, sort, order, **kwargs):
        return fetch_billing_account_list(
            sort,
            order,
            ac_number=kwargs["ac_number"],
            business_name=kwargs["business_name"],
            status=kwargs["status"],
        )

    def get(self, request):
        serializer = SearchFilterSerializer(data=request.query_params)

        try:
            if serializer.is_valid(raise_exception=True):
                query_set = self.filter_queryset(
                    self.get_queryset(
                        serializer.data["sort"],
                        serializer.data["order"],
                        ac_number=serializer.data["ac_number"],
                        business_name=serializer.data["business_name"],
                        status=serializer.data["status"],
                    )
                )
                data = self.paginate_queryset(query_set)
                serializer = self.get_serializer(data, many=True)

                return self.get_paginated_response(serializer.data)

        except ValidationError as e:
            logger.title("Billing Account List Validation Error").error(
                e, exc_info=True
            )

            return Response(
                {
                    "errors": serializer.errors,
                    "code": error_codes.VALIDATION_ERROR,
                },
                status.HTTP_400_BAD_REQUEST,
            )

        except BaseException as e:
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class AccountReactivationProcessView(SnsHandlerView):
    def notification_handler(self, message):
        try:
            billing_account_id = message["billing_account_id"]

            task = (
                account_reactivation.apply_async(
                    kwargs={"billing_account_id": billing_account_id},
                ),
            )
            logger.title("Celery Task Scheduled").info(f"ID: {task[0]}")
            return Response(
                {"message": "Task Scheduled", "data": {"task_id": str(task[0])}}
            )
        except (TypeError, KeyError):
            raise ValidationError(detail="billing account id is missing.")
        except BaseException as e:
            logger.error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class MarkAccountFraudAPIView(generics.UpdateAPIView):
    def post(self, request, billing_account_id, *args, **kwargs):
        serializer = MarkFraudSerializer(data=request.data)
        try:
            if serializer.is_valid(raise_exception=True):
                email = serializer.data["user_email"]
                if email:
                    user_id = UserProfiles.objects.get(email=email).id
                else:
                    user_id = request.user.id

                mark_as_fraud(billing_account_id, user_id)
                return Response(
                    {
                        "message": "Account marked as fraud",
                    },
                    status.HTTP_200_OK,
                )
        except InvalidBillingAccountException:
            raise NotFound(detail="billing account not found", code=404)
        except InvalidServiceException:
            raise NotFound(detail="service not found", code=404)
        except ValidationError as e:
            return Response(
                {
                    "code": error_codes.VALIDATION_ERROR,
                    "errors": e.detail,
                },
                status.HTTP_400_BAD_REQUEST,
            )
        except BaseException as e:
            logger.title("Mark Fraud Error").error(e, exc_info=True)
            return Response(
                {
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class BillingAPIView(generics.RetrieveUpdateAPIView):
    serializer_class = BillingSerializer
    lookup_field = "id"
    lookup_url_kwarg = "id"

    def get_object(self):
        try:
            return BillingAccounts.objects.get(id=self.kwargs["id"])
        except BillingAccounts.DoesNotExist:
            raise NotFound(detail="Billing account not found")

    def get(self, request, *args, **kwargs):
        try:
            data = self.get_object()
            serializer = self.get_serializer(data)
            return Response(
                {"message": "Billing account details", "data": serializer.data}
            )
        except BaseException as e:
            logger.title("Billing account Detail Error").error(e, exc_info=True)
            return Response(
                {
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )

    def patch(self, request, id, *args, **kwargs):
        try:
            instance = self.get_object()
            original_data = model_to_dict(instance)
            serializer = self.get_serializer(
                instance, data=request.data, partial=True
            )
            if serializer.is_valid(raise_exception=True):
                serializer.save()
                log_edited_fields(
                    id,
                    original_data,
                    model_to_dict(serializer.instance),
                )

                return Response(
                    {
                        "status": "success",
                        "message": "Billing details updated successfully.",
                        "data": serializer.data,
                    }
                )
        except ValidationError as e:
            return Response(
                {
                    "code": error_codes.VALIDATION_ERROR,
                    "errors": e.detail,
                },
                status.HTTP_400_BAD_REQUEST,
            )
        except BaseException as e:
            return Response(
                {
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class GstUpdateAPIView(generics.UpdateAPIView):
    serializer_class = GstUpdateSerializer

    lookup_field = "id"
    lookup_url_kwarg = "id"

    def get_object(self):
        try:
            return BillingAccounts.objects.get(id=self.kwargs["id"])
        except BillingAccounts.DoesNotExist:
            raise NotFound(detail="Billing account not found")

    def put(self, request, id, *args, **kwargs):
        try:
            instance = self.get_object()
            original_data = model_to_dict(instance)
            serializer = self.get_serializer(instance, data=request.data)
            if serializer.is_valid(raise_exception=True):
                serializer.save()
                log_edited_fields(
                    id,
                    original_data,
                    model_to_dict(serializer.instance),
                )
                return Response(
                    {
                        "message": "GSTIN Updated Successfully.",
                        "data": serializer.data,
                    }
                )
        except ValidationError as e:
            return Response(
                {
                    "code": error_codes.VALIDATION_ERROR,
                    "errors": e.detail,
                },
                status.HTTP_400_BAD_REQUEST,
            )
        except BaseException as e:
            return Response(
                {
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class UpdateCreditLimitView(SnsHandlerView):
    def notification_handler(self, message):
        try:
            billing_account_id = message["billing_account_id"]
            task = (
                update_ban_credit_limit.apply_async(
                    kwargs={"billing_account_id": billing_account_id},
                ),
            )
            logger.title("Celery Task Scheduled").info(f"ID: {task[0]}")
            return Response(
                {"message": "Task Scheduled", "data": {"task_id": str(task[0])}}
            )
        except (TypeError, KeyError):
            raise ValidationError(detail="billing account id is missing.")
        except BaseException as e:
            logger.error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class ContactListCreateView(generics.ListCreateAPIView):
    serializer_class = ContactSerializer
    filter_backends = [
        filters.OrderingFilter,
        django_filters.DjangoFilterBackend,
    ]
    filterset_class = ContactsFilter

    ordering_fields = [
        "name",
        "created",
    ]
    ordering = ["-created"]  # Default ordering

    def get_queryset(self):
        billing_account_id = self.kwargs.get("billing_account_id", None)
        # Check if Billing Account exists
        if not BillingAccounts.objects.filter(id=billing_account_id).exists():
            raise NotFound(
                detail="Billing Account not found",
                code=status.HTTP_404_NOT_FOUND,
            )
        return ServiceContacts.objects.filter(
            billing_account_id=billing_account_id
        )

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["billing_account_id"] = self.kwargs[
            "billing_account_id"
        ]  # Pass billing_account_id from URL
        return context

    def get(self, request, *args, **kwargs):
        try:
            return super().get(request, *args, **kwargs)
        except BaseException as e:
            return Response(
                {
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )

    def post(self, request, *args, **kwargs):
        try:
            self.get_queryset()
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            with transaction.atomic():
                contact = serializer.save()
            ContactNotifier(contact).created()
            return Response(
                {
                    "status": "success",
                    "message": "Contact added successfully",
                },
                status.HTTP_201_CREATED,
            )
        except ValidationError as e:
            return Response(
                {
                    "code": error_codes.VALIDATION_ERROR,
                    "errors": e.detail,
                },
                status.HTTP_400_BAD_REQUEST,
            )
        except BaseException as e:
            return Response(
                {
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class ContactUpdateView(generics.RetrieveUpdateAPIView):
    serializer_class = ContactSerializer

    def get_queryset(self):
        billing_account_id = self.kwargs.get("billing_account_id", None)
        id = self.kwargs.get("pk", None)
        # Check if contact associated with billing account exists
        service_contact = ServiceContacts.objects.filter(
            id=id, billing_account_id=billing_account_id
        )

        if not service_contact.exists():
            raise NotFound(
                detail="Billing Account or Contact not found",
                code=status.HTTP_404_NOT_FOUND,
            )

        return service_contact

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["billing_account_id"] = self.kwargs["billing_account_id"]
        context["contact_id"] = self.kwargs["pk"]
        return context

    def put(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data)
            serializer.is_valid(raise_exception=True)
            with transaction.atomic():
                contact = serializer.save()
            ContactNotifier(contact).updated()
            return Response(
                {
                    "status": "success",
                    "message": "Contact updated successfully",
                }
            )
        except ValidationError as e:
            return Response(
                {
                    "code": error_codes.VALIDATION_ERROR,
                    "errors": e.detail,
                },
                status.HTTP_400_BAD_REQUEST,
            )
        except BaseException as e:
            return Response(
                {
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )
