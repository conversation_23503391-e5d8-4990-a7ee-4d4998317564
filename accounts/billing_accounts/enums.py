from accounts.enums import BaseEnum


class BillingStatusEnum(BaseEnum):
    INACTIVE = 0
    ACTIVE = 1


class BillingVerificationStateEnum(BaseEnum):
    UNVERIFIED = 1
    SEMI_VERIFIED = 2
    VERIFIED = 3
    SPAM = 4
    FRAUD = 5


class BillingAccountCreditStatusEnum(BaseEnum):
    INACTIVE = 0
    ACTIVE = 1


class BillingAccountCreditTransType(BaseEnum):
    CREDIT = "cr"
    DEBIT = "dr"


class DiscountBucketApplyOnEnum(BaseEnum):
    RENTAL = "R"
    USAGES = "U"
    ALL = "A"


class DiscountBucketStatusEnum(BaseEnum):
    INACTIVE = 0
    ACTIVE = 1


class ContactStatusEnums(BaseEnum):
    INACTIVE = 0
    ACTIVE = 1


class ContactTypeEnums(BaseEnum):
    BILLING = 1
    GENERAL = 2
    TECH = 4


class DocTypeEnums(BaseEnum):
    PAN_CARD = "pan_card"
    AADHAAR_CARD = "aadhaar_card"
    VOTER_ID_CARD = "voter_id_card"
    RATION_CARD = "rashan_card"
    DRIVING_LICENSE = "driving_licence"
    OTHER = "other"
    PARTNER_SHIP_DEED = "partner_ship_deed"
    CERTIFICATE_OF_INCORPORATION = "certificate_of_incorporation_(coi)"
    RBI_CERTIFICATE = "rbi_certificate_(mandatory)"
    SALE_TAX = "sale_tax"
    SERVICE_TAX_REGISTRATION = "service_tax_registration"
    SHOP_ESTABLISHMENT = "shop_eshtablishment"
    TRUST_DEED = "trust_deed"
    TELEPHONE_BILL = "telephone_bill"
    ELECTRICITY_BILL = "electricity_bill"
    MOBILE_BILL = "mobile_bill"
    COI_BY_FOREIGN_GOVT = "coi_by_foreign_govt"
    GOVT_ID = "govt_id"
    LOCAL_ADDRESS_PROOF = "local_address_proof"
    PASSPORT = "passport"
    PARTNERSHIP_DOCUMENTS = "partnership_documents"
    FORM_60_OF_HUF = "form_60_of_huf"
    GOVT_ID_CARD = "govt_id_card"
    ADDRESS_PROOF_GOVT_RECONIZED = "address_proof(govt_reconized)"
    GAS_CONNECTION = "gas_connection"
    REGISTRATION_PROOF = "registration_proof_(mandatory)"
    SIGNING_AUTHORITY_IDENTITY_PROOF = "signing_autority_identity_proof"
    CURRENT_PASSBOOK = "current_passbook"
    LEASE_AGREEMENT = "lease_agreement"
    IDENTITY_PROOF = "identity_proof"
    BANK_PASSBOOK = "bank_passbook"
    CREDIT_CARD_STATEMENT = "credit_card_statement"
    WATER_BILL = "water_bill"
    WORK_ORDER = "work_order"
    RENT_AGREEMENT_DULY_STAMPED_BY_COURT = (
        "rent_agreement_(duly_stamped_by_court)"
    )
    GENERAL_DOCUMENT = "general_document"
    ADDRESS_PROOF = "address_proof"
    PASSPORT_SAME_COUNTRY = "passport_(same_country)"
    PASSPORT_WORLDWIDE = "passport_(worldwide)"
    ID_PROOF_SAME_COUNTRY = "id_proof_(same_country)"
    ID_PROOF_WORLDWIDE = "id_proof_(worldwide)"
    ADDRESS_PROOF_SAME_COUNTRY = "address_proof_(same_country)"
    ADDRESS_PROOF_WORLDWIDE = "address_proof_(worldwide)"
    EKYC = "ekyc"
    UAN = "uan"
    GST = "gst"
