import logging
import typing as t

from django.db import models

from accounts.billing_accounts.enums import DocTypeEnums
from accounts.utils.common import uuid

logger = logging.getLogger(__name__)


class Countries(models.Model):
    id = models.AutoField(primary_key=True)
    country = models.CharField(max_length=200, null=True, unique=True)
    short_code = models.CharField(
        max_length=4,
        null=True,
        help_text="Short Code(for india-IN)",
        unique=True,
    )
    code3 = models.Char<PERSON>ield(max_length=4, null=True)
    c_code = models.CharField(
        max_length=20,
        null=True,
        help_text="Country Dialing  Code(+91 for india)",
    )
    default_time_zone = models.CharField(
        max_length=255, null=True, help_text="utc time zone"
    )
    timezone_offset = models.CharField(max_length=6, null=True)
    timezone_name = models.CharField(max_length=100, null=True)
    currency_default = models.CharField(max_length=50, null=True)
    currency_code = models.Char<PERSON><PERSON>(max_length=250, null=True)
    status = models.CharField(
        max_length=5,
        default="1",
        help_text="Country Dialing  Code(+91 for india)",
    )
    date = models.DateField(auto_now_add=True)

    objects = models.Manager()

    class Meta:
        db_table = "countries"
        indexes = [
            models.Index(fields=["timezone_offset"]),
            models.Index(fields=["status"]),
        ]


class StateCodes(models.Model):
    id = models.AutoField(primary_key=True)
    country = models.ForeignKey(
        Countries,
        on_delete=models.CASCADE,
    )
    name = models.CharField(max_length=200)
    type = models.IntegerField(help_text="1=> state, 2 => Union territories")
    code2 = models.CharField(max_length=2)
    code_for_gst = models.CharField(max_length=20, null=True)
    code_for_tin = models.IntegerField()
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "state_codes"
        indexes = [models.Index(fields=["country_id", "code_for_gst"])]
        ordering = ["created"]


class TaxVoicetrees(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    country_code = models.CharField(max_length=2)
    state_id = models.IntegerField(default=0)
    tax_name = models.CharField(max_length=200)
    parent = models.IntegerField()
    value = models.DecimalField(max_digits=10, decimal_places=2)
    effective_from = models.DateField()
    effective_till = models.DateField(null=True)

    objects = models.Manager()

    class Meta:
        db_table = "tax_voicetrees"
        indexes = [
            models.Index(
                fields=[
                    "country_code",
                    "state_id",
                    "effective_from",
                    "effective_till",
                ]
            )
        ]


class DocTypes(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=250)
    short_code = models.CharField(
        max_length=250,
        choices=DocTypeEnums.choices(),
        default=DocTypeEnums.OTHER.value,
    )
    status = models.IntegerField(default=0)
    created = models.DateField(auto_now_add=True)
    modified = models.DateField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "doc_types"
        indexes = [models.Index(fields=["status"])]

    @classmethod
    def get_doc_type_by_short_code(
        cls, doc_type_enum: DocTypeEnums
    ) -> t.Optional["DocTypes"]:
        try:
            return cls.objects.get(short_code=doc_type_enum.value)
        except cls.DoesNotExist:
            return None


class BankAccountDetails(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    account_number = models.CharField(max_length=250)
    bank = models.CharField(max_length=200)
    ifsc_code = models.CharField(max_length=200)
    branch = models.CharField(max_length=200)
    address = models.TextField()
    can_send = models.SmallIntegerField(default=1)
    status = models.SmallIntegerField(default=1)
    mode = models.CharField(max_length=3)

    objects = models.Manager()

    class Meta:
        db_table = "bank_account_details"
        indexes = [models.Index(fields=["account_number"])]


class BankAccountShares(models.Model):
    id = models.AutoField(primary_key=True)
    bank_account = models.ForeignKey(
        BankAccountDetails,
        db_column="bank_account_id",
        on_delete=models.CASCADE,
    )
    share_by = models.CharField(max_length=10)
    content_json = models.TextField()
    status = models.SmallIntegerField()
    created_by = models.IntegerField()
    created_by_dept = models.IntegerField()
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "bank_account_shares"


class BankSheetHistories(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    file_name = models.CharField(max_length=250)
    account_no = models.CharField(max_length=250)
    opening_bal = models.DecimalField(
        max_digits=10, decimal_places=3, default=0.000
    )
    closing_bal = models.DecimalField(
        max_digits=10, decimal_places=3, default=0.000
    )
    from_date = models.DateField()
    to_date = models.DateField()
    debit_count = models.IntegerField()
    credit_count = models.IntegerField()
    dr_amount = models.DecimalField(max_digits=10, decimal_places=3)
    cr_amount = models.DecimalField(max_digits=10, decimal_places=3)
    created_by = models.CharField(max_length=36)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "bank_sheet_histories"


class EmailQueues(models.Model):
    id = models.CharField(
        primary_key=True, max_length=50, default=uuid, editable=False
    )
    data = models.TextField()
    service_number = models.CharField(max_length=50, null=True)
    billing_account_id = models.CharField(max_length=50, null=True)
    status = models.SmallIntegerField(default=0)
    send_timestamp = models.BigIntegerField(null=True)
    unmature = models.SmallIntegerField(default=0)
    mature = models.SmallIntegerField(default=0)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "email_queues"
        indexes = [
            models.Index(fields=["billing_account_id", "modified"]),
            models.Index(fields=["status"]),
        ]


class SmsQueues(models.Model):
    id = models.CharField(
        primary_key=True, max_length=50, default=uuid, editable=False
    )
    data = models.TextField()
    service_number = models.CharField(max_length=50, null=True)
    billing_account_id = models.CharField(max_length=50, null=True)
    status = models.SmallIntegerField(default=0)
    send_timestamp = models.BigIntegerField(null=True)
    unmature = models.SmallIntegerField(default=0)
    mature = models.SmallIntegerField(default=0)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "sms_queues"
        indexes = [
            models.Index(fields=["billing_account_id", "modified"]),
            models.Index(fields=["status"]),
        ]


class CeleryScheduledTask(models.Model):
    id = models.BigAutoField(primary_key=True, editable=False)
    task_id = models.CharField(max_length=255, unique=True)
    task_name = models.CharField(max_length=255)
    args = models.TextField(blank=True, null=True)
    kwargs = models.TextField(blank=True, null=True)
    eta = models.DateTimeField(blank=True, null=True)
    created = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "django_celery_scheduled_tasks"

    def __str__(self):
        return f"{self.id} - {self.task_name}"
