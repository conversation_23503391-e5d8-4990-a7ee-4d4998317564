from celery.signals import before_task_publish
from django.utils.dateparse import parse_datetime
from accounts.core.models import CeleryScheduledTask
import logging

logger = logging.getLogger(__name__)


@before_task_publish.connect
def task_scheduled_handler(sender=None, headers=None, body=None, **kwargs):
    try:
        # Extract `task_id` safely from headers or body
        if headers:
            task_id = headers.get("id")
            eta = headers.get("eta")
        else:
            task_id = body[0] if isinstance(body, tuple) else body.get("id")
            eta = None  # ETA may not be in the body tuple

        task_name = sender

        # Parse ETA if available
        if eta:
            eta = parse_datetime(eta)

        # Extract args and kwargs safely
        if isinstance(body, tuple):
            args = str(body[1])  # body[1] contains args in tuple format
            kwargs = str(body[2])  # body[2] contains kwargs in tuple format
        else:
            args = str(body.get("args", []))
            kwargs = str(body.get("kwargs", {}))

        CeleryScheduledTask.objects.create(
            task_id=task_id,
            task_name=task_name,
            args=args,
            kwargs=kwargs,
            eta=eta,
        )
        logger.info(f"Task scheduled: {task_name} (ID: {task_id})")
    except Exception as e:
        logger.title("Error saving scheduled task").critical(e, exc_info=True)
