from freezegun import freeze_time
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

from accounts.core.tests.factories import CountryFactory, StateCodeFactory
from accounts.core.models import StateCodes


class TestPackageCategoriesView(APITestCase):
    def setUp(self) -> None:
        self.country = CountryFactory.create()
        return super().setUp()

    def test_list_all_states_by_country_id(self):
        StateCodeFactory.create_batch(size=5, country=self.country)
        url = reverse("core:country_states", kwargs={"id": self.country.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_json = response.json()
        self.assertEqual(response_json["pagination"]["count"], 5)
        self.assertEqual(response_json["code"], 200)
        state = (
            StateCodes.objects.filter(country=self.country)
            .order_by("name")
            .first()
        )
        self.assertEqual(
            response_json["data"][0],
            {
                "id": state.id,
                "name": state.name,
                "code_for_gst": state.code_for_gst,
                "code_for_tin": state.code_for_tin,
                "created": state.created.strftime("%Y-%m-%dT%H:%M:%SZ"),
                "modified": state.modified.strftime("%Y-%m-%dT%H:%M:%SZ"),
            },
        )

    def test_default_ordering_by_name_asc(self):
        state_1 = StateCodeFactory.create(
            country=self.country, name="Andhra Pradesh"
        )
        state_2 = StateCodeFactory.create(country=self.country, name="Punjab")

        url = reverse("core:country_states", kwargs={"id": self.country.id})
        response = self.client.get(url)
        response_json = response.json()

        self.assertEqual(response_json["data"][0]["id"], state_1.id)
        self.assertEqual(response_json["data"][1]["id"], state_2.id)

    def test_ordering_by_name_asc(self):
        state_1 = StateCodeFactory.create(
            country=self.country, name="Andhra Pradesh"
        )
        state_2 = StateCodeFactory.create(country=self.country, name="Punjab")

        url = reverse("core:country_states", kwargs={"id": self.country.id})
        response = self.client.get(url, data={"ordering": "name"})
        response_json = response.json()

        self.assertEqual(response_json["data"][0]["id"], state_1.id)
        self.assertEqual(response_json["data"][1]["id"], state_2.id)

    def test_ordering_by_name_desc(self):
        state_1 = StateCodeFactory.create(
            country=self.country, name="Andhra Pradesh"
        )
        state_2 = StateCodeFactory.create(country=self.country, name="Punjab")

        url = reverse("core:country_states", kwargs={"id": self.country.id})
        response = self.client.get(url, data={"ordering": "-name"})
        response_json = response.json()

        self.assertEqual(response_json["data"][0]["id"], state_2.id)
        self.assertEqual(response_json["data"][1]["id"], state_1.id)

    def test_with_fields(self):
        state = StateCodeFactory.create(country=self.country)
        url = reverse("core:country_states", kwargs={"id": self.country.id})
        response = self.client.get(url, data={"fields": "id,name,code_for_gst"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_json = response.json()
        self.assertEqual(response_json["code"], 200)
        self.assertEqual(
            response_json["data"][0],
            {
                "id": state.id,
                "name": state.name,
                "code_for_gst": state.code_for_gst,
            },
        )
