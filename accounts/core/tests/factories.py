import factory
from decimal import Decimal
from datetime import timedelta
from factory import Faker, SubFactory
from factory.django import DjangoModelFactory
from django.utils import timezone
from accounts.core.models import Countries, StateCodes, TaxVoicetrees


class CountryFactory(DjangoModelFactory):
    country = factory.Iterator(["India", "USA", "Canada"])
    short_code = factory.Iterator(["IN", "US", "CA"])
    code3 = factory.Iterator(["IND", "USA", "CAN"])
    c_code = factory.Iterator(["91", "1", "1"])
    default_time_zone = factory.Iterator(
        ["UTC+05:30", "UTC-08:00", "UTC-08:00"]
    )
    timezone_offset = factory.Iterator(["+05:30", "-08:00", "-08:00"])
    timezone_name = factory.Iterator(
        ["Asia/Kolkata", "Pacific/Pitcairn", "Pacific/Pitcairn"]
    )
    currency_default = None
    currency_code = None

    class Meta:
        model = Countries
        django_get_or_create = (
            "country",
            "short_code",
        )


class StateCodeFactory(DjangoModelFactory):
    country = SubFactory(CountryFactory)
    name = Faker("name")
    type = 1
    code2 = Faker("pystr", min_chars=2, max_chars=2)
    code_for_gst = Faker("pystr", min_chars=2, max_chars=2)
    code_for_tin = Faker("random_int", min=1, max=10)

    class Meta:
        model = StateCodes


class TaxVoicetreeFactory(factory.django.DjangoModelFactory):
    country_code = factory.Iterator(["IN", "US", "CA"])
    tax_name = Faker("word")
    parent = 0
    value = Faker("pydecimal", left_digits=2, right_digits=2, positive=True)
    effective_from = Faker("date_time", tzinfo=timezone.get_current_timezone())
    effective_till = Faker(
        "date_between",
        start_date=effective_from,
        tzinfo=timezone.get_current_timezone(),
    )

    @classmethod
    def create_gst_tax(
        cls, state_code: StateCodeFactory, value: Decimal = 18.0
    ):
        return cls.create(
            country_code=state_code.country.short_code,
            state_id=state_code.id,
            tax_name="GST",
            value=value,
            effective_from=timezone.now() - timedelta(days=10),
            effective_till=timezone.now() + timedelta(days=10),
        )

    class Meta:
        model = TaxVoicetrees
