# Generated by Django 3.2.18 on 2025-06-09 09:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_alter_statecodes_options'),
    ]

    operations = [
        migrations.AddField(
            model_name='doctypes',
            name='short_code',
            field=models.CharField(choices=[('pan_card', 'PAN_CARD'), ('aadhaar_card', 'AADHAAR_CARD'), ('voter_id_card', 'VOTER_ID_CARD'), ('rashan_card', 'RATION_CARD'), ('driving_licence', 'DRIVING_LICENSE'), ('other', 'OTHER'), ('partner_ship_deed', 'PARTNER_SHIP_DEED'), ('certificate_of_incorporation_(coi)', 'CERTIFICATE_OF_INCORPORATION'), ('rbi_certificate_(mandatory)', 'RBI_CERTIFICATE'), ('sale_tax', 'SALE_TAX'), ('service_tax_registration', 'SERVICE_TAX_REGISTRATION'), ('shop_eshtablishment', 'SHOP_ESTABLISHMENT'), ('trust_deed', 'TRUST_DEED'), ('telephone_bill', 'TELEPHONE_BILL'), ('electricity_bill', 'ELECTRICITY_BILL'), ('mobile_bill', 'MOBILE_BILL'), ('coi_by_foreign_govt', 'COI_BY_FOREIGN_GOVT'), ('govt_id', 'GOVT_ID'), ('local_address_proof', 'LOCAL_ADDRESS_PROOF'), ('passport', 'PASSPORT'), ('partnership_documents', 'PARTNERSHIP_DOCUMENTS'), ('form_60_of_huf', 'FORM_60_OF_HUF'), ('govt_id_card', 'GOVT_ID_CARD'), ('address_proof(govt_reconized)', 'ADDRESS_PROOF_GOVT_RECONIZED'), ('gas_connection', 'GAS_CONNECTION'), ('registration_proof_(mandatory)', 'REGISTRATION_PROOF'), ('signing_autority_identity_proof', 'SIGNING_AUTHORITY_IDENTITY_PROOF'), ('current_passbook', 'CURRENT_PASSBOOK'), ('lease_agreement', 'LEASE_AGREEMENT'), ('identity_proof', 'IDENTITY_PROOF'), ('bank_passbook', 'BANK_PASSBOOK'), ('credit_card_statement', 'CREDIT_CARD_STATEMENT'), ('water_bill', 'WATER_BILL'), ('work_order', 'WORK_ORDER'), ('rent_agreement_(duly_stamped_by_court)', 'RENT_AGREEMENT_DULY_STAMPED_BY_COURT'), ('general_document', 'GENERAL_DOCUMENT'), ('address_proof', 'ADDRESS_PROOF'), ('passport_(same_country)', 'PASSPORT_SAME_COUNTRY'), ('passport_(worldwide)', 'PASSPORT_WORLDWIDE'), ('id_proof_(same_country)', 'ID_PROOF_SAME_COUNTRY'), ('id_proof_(worldwide)', 'ID_PROOF_WORLDWIDE'), ('address_proof_(same_country)', 'ADDRESS_PROOF_SAME_COUNTRY'), ('address_proof_(worldwide)', 'ADDRESS_PROOF_WORLDWIDE'), ('ekyc', 'EKYC'), ('uan', 'UAN'), ('gst', 'GST')], default='other', max_length=250),
        ),
    ]
