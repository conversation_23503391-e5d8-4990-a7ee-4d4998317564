import logging
from django.db.models import Q
from django.utils import timezone
from accounts.core.models import SmsQueues
import time

logger = logging.getLogger(__name__)


def check_sms_send_today(ban_id):
    return SmsQueues.objects.filter(
        Q(billing_account_id=ban_id), Q(modified__date=timezone.now().date())
    ).exists()


def enqueue_sms(billing_account_id, service_number, sms_data):
    return SmsQueues.objects.create(
        data=sms_data,
        service_number=service_number,
        send_timestamp=int(time.time()),
        billing_account_id=billing_account_id,
    )
