from django.test import TestCase
from django.utils import timezone
from accounts.core.utils.tax_calculation import (
    calculate_tax,
    sum_taxes,
    calculate_parent_tax,
    calculate_child_tax,
)
from accounts.core.tests.factories import TaxVoicetreeFactory, StateCodeFactory
from accounts.core.utils.tax_calculation import calc_reverse_tax
from accounts.core.models import TaxVoicetrees


def test_with_country_id_99():
    # Test case for country_id = 99
    country_id = 99
    amount = 100.0
    expected_result = 84.*************
    assert calc_reverse_tax(country_id, amount) == expected_result


def test_with_country_id_not_99():
    # Test case for country_id != 99
    country_id = 10
    amount = 100.0
    assert calc_reverse_tax(country_id, amount) == amount


class TestTaxCalculationUtils(TestCase):
    def setUp(self):
        self.amount = 1000
        self.country_code = "IN"
        self.state_id = 0
        self.date = timezone.now()
        self.state = StateCodeFactory.create()
        self.parent_tax = TaxVoicetreeFactory.create(
            id=1,
            country_code="IN",
            state_id=self.state.id,
            tax_name="GST",
            parent=0,
            value=10,
            effective_from=self.date - timezone.timedelta(days=1),
            effective_till=self.date + timezone.timedelta(days=1),
        )
        self.child_tax = TaxVoicetreeFactory.create(
            id=2,
            country_code="IN",
            state_id=self.state.id,
            tax_name="Cess",
            parent=self.parent_tax.id,
            value=2,
            effective_from=self.date - timezone.timedelta(days=1),
            effective_till=self.date + timezone.timedelta(days=1),
        )
        self.tax_data = TaxVoicetrees.objects.filter(
            country_code=self.country_code,
            state_id=self.state.id,
            effective_from__lte=self.date,
            effective_till__gte=self.date,
        ).values()

    def test_calculate_tax_with_no_tax(self):
        result = calculate_tax(self.amount, self.country_code)
        self.assertTrue(result)
        self.assertEqual(result["tax_total"], 180)
        self.assertEqual(len(result["detail"]), 1)
        self.assertEqual(result["detail"]["0"]["name"], "GST")

    def test_calculate_tax_with_tax(self):
        result = calculate_tax(self.amount, self.country_code, self.state.id)
        self.assertTrue(result)
        self.assertEqual(result["tax_total"], 102)
        self.assertEqual(len(result["detail"]), 1)
        self.assertEqual(result["detail"]["1"]["name"], "Cess")

    def test_calculate_tax_country_other_than_IN(self):
        result = calculate_tax(self.amount, "US")
        self.assertTrue(result)
        self.assertEqual(result["tax_total"], 0)
        self.assertEqual(len(result["detail"]), 1)
        self.assertEqual(result["detail"]["0"]["name"], "No tax")

    def test_sum_taxes(self):
        main_tax = {
            "1": {
                "name": "GST",
                "tax_percent": 10,
                "amount": self.amount,
                "tax": 100,
            }
        }
        child_tax = {
            "2": {
                "parent": "1",
                "name": "Cess",
                "tax_percent": 2,
                "amount": 100,
                "tax": 2,
            }
        }
        result = sum_taxes(main_tax, child_tax)
        self.assertTrue(result)
        self.assertEqual(result["tax_total"], 102)
        self.assertEqual(len(result["detail"]), 1)

    def test_calculate_parent_tax(self):
        result = calculate_parent_tax(self.amount, self.tax_data)
        self.assertEqual(len(result), 1)
        self.assertEqual(result["1"]["name"], "GST")
        self.assertEqual(result["1"]["tax_percent"], 10)
        self.assertEqual(result["1"]["tax"], 100)

    def test_calculate_child_tax(self):
        parent_taxes = {
            "1": {
                "name": "GST",
                "tax_percent": 10,
                "amount": self.amount,
                "tax": 100,
            }
        }
        result = calculate_child_tax(self.tax_data, parent_taxes)
        self.assertEqual(len(result), 1)
        self.assertEqual(result["2"]["name"], "Cess")
        self.assertEqual(result["2"]["tax_percent"], 2)
        self.assertEqual(result["2"]["tax"], 2)
