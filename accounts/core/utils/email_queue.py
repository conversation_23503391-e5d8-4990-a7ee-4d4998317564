import logging
from django.db.models import Q
from django.utils import timezone
from accounts.core.models import EmailQueues
import time

logger = logging.getLogger(__name__)


def check_email_send_today(ban_id):
    return EmailQueues.objects.filter(
        Q(billing_account_id=ban_id), Q(modified__date=timezone.now().date())
    ).exists()


def enqueue_email(billing_account_id, service_number, email_data):
    return EmailQueues.objects.create(
        data=email_data,
        service_number=service_number,
        send_timestamp=int(time.time()),
        billing_account_id=billing_account_id,
    )
