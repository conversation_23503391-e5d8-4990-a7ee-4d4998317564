import logging
from django.utils import timezone
from accounts.core.models import TaxVoicetrees
from typing import Optional, Dict, List, Union

logger = logging.getLogger(__name__)


def calc_reverse_tax(country_id: int, amount: float) -> float:
    if country_id == 99:  # hardcoded value, need to work in future
        tax = 18
    else:
        return amount

    return (float(amount) * 100) / (100 + tax)


def calculate_tax(
    amount: float,
    country_code: str,
    state_id: int = 0,
    date: Optional[timezone.datetime] = None,
) -> Union[
    bool, Dict[str, Union[List[Dict[str, Union[str, float, int]]], float]]
]:
    if not country_code:
        return False

    if not date:
        date = timezone.now()

    state_id = 0 if not state_id else state_id

    taxes = TaxVoicetrees.objects.filter(
        country_code=country_code,
        state_id=state_id,
        effective_from__lte=date,
        effective_till__gte=date,
    ).values()

    if len(taxes) <= 0:
        if country_code == "IN":
            tax_percent = 18
            no_tax_found_array: Dict[
                str, Union[List[Dict[str, Union[str, float, int]]], float]
            ] = {
                "detail": {
                    "0": {
                        "name": "GST",
                        "tax_percent": tax_percent,
                        "amount": amount,
                        "tax": (amount * tax_percent) / 100,
                    }
                },
                "tax_total": (amount * tax_percent) / 100,
            }
            return no_tax_found_array
        else:
            no_tax_found_array: Dict[
                str, Union[List[Dict[str, Union[str, float, int]]], float]
            ] = {
                "detail": {
                    "0": {
                        "name": "No tax",
                        "tax_percent": 0,
                        "amount": amount,
                        "tax": 0,
                    }
                },
                "tax_total": 0,
            }
            return no_tax_found_array

    parent_taxes: Dict[
        int, Dict[str, Union[str, float, int]]
    ] = calculate_parent_tax(amount, taxes)
    child_taxes: Dict[
        int, Dict[str, Union[str, float, int]]
    ] = calculate_child_tax(taxes, parent_taxes)
    return sum_taxes(parent_taxes, child_taxes)


def sum_taxes(
    main_tax: Dict[int, Dict[str, Union[str, float, int]]],
    child_tax: Dict[int, Dict[str, Union[str, float, int]]],
) -> Dict[str, Union[Dict[str, Dict[str, Union[str, float, int]]], float]]:
    amount = 0
    ttx: Dict[
        str, Union[Dict[str, Dict[str, Union[str, float, int]]], float]
    ] = {
        "detail": {},
    }

    if main_tax:
        for key, tax in main_tax.items():
            amount += tax["tax"]
            ttx["detail"][key] = tax
    if child_tax:
        for key, tax in child_tax.items():
            amount += tax["tax"]
            ttx["detail"][tax["parent"]] = tax
    ttx["tax_total"] = amount
    return ttx


def calculate_parent_tax(
    amount: float, taxes
) -> Dict[int, Dict[str, Union[str, float, int]]]:
    output: Dict[int, Dict[str, Union[str, float, int]]] = {}

    for tax in taxes:
        if tax["parent"] == 0:
            tax_amount = (amount * tax["value"]) / 100
            output[tax["id"]] = {
                "name": tax["tax_name"],
                "tax_percent": tax["value"],
                "amount": amount,
                "tax": tax_amount,
            }
    return output


def calculate_child_tax(
    taxes, parent_taxes
) -> Dict[int, Dict[str, Union[str, float, int]]]:
    output: Dict[int, Dict[str, Union[str, float, int]]] = {}

    for tax in taxes:
        if tax["parent"] != 0:
            tax_amount = (
                parent_taxes[str(tax["parent"])]["tax"] * tax["value"]
            ) / 100
            output[tax["id"]] = {
                "parent": str(tax["parent"]),
                "name": tax["tax_name"],
                "tax_percent": tax["value"],
                "amount": parent_taxes[str(tax["parent"])]["tax"],
                "tax": tax_amount,
            }
    return output
