from core.enums import PresignedUrlModule
from core.validators import PresignedUrlInputValidator
from rest_flex_fields import FlexFieldsModelSerializer
from rest_framework import serializers, status

from accounts.core.models import StateCodes


class CountryStateSerializer(FlexFieldsModelSerializer):
    class Meta:
        model = StateCodes
        fields = (
            "id",
            "name",
            "code_for_gst",
            "code_for_tin",
            "created",
            "modified",
        )


class PresignedUrlRequestSerializer(serializers.Serializer):
    module = serializers.ChoiceField(choices=PresignedUrlModule.values())
    content_type = serializers.CharField()
    file_name = serializers.CharField(required=False, allow_blank=True)

    def validate(self, attrs):
        validator = PresignedUrlInputValidator(
            module_string=attrs["module"],
            content_type=attrs["content_type"],
            file_name=attrs.get("file_name"),
        )

        try:
            validator.validate()
        except ValueError as e:
            raise serializers.ValidationError(
                detail=str(e), code=status.HTTP_400_BAD_REQUEST
            )

        attrs["content_type"] = validator.content_type
        attrs["s3_prefix"] = validator.get_s3_prefix()
        attrs["s3_tmp_prefix"] = validator.get_s3_tmp_prefix()
        attrs["file_name"] = validator.get_final_file_name()
        attrs["file_size"] = validator.file_size

        return attrs
