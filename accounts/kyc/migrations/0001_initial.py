# Generated by Django 3.2.18 on 2025-06-02 06:34

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('billing_accounts', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='KYC',
            fields=[
                ('id', models.CharField(default=uuid.uuid4, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('current_state', models.CharField(choices=[('verification', 'VERIFICATION'), ('gst_info', 'GST_INFO'), ('upload_pan', 'UPLOAD_PAN'), ('digilocker_pan', 'DIGILOCKER_PAN'), ('video_kyc', 'VIDEO_KYC'), ('e_sign', 'E_SIGN'), ('kyc_done', 'KYC_DONE')], default='verification', max_length=30)),
                ('mode', models.Char<PERSON>ield(choices=[('gst', 'GST'), ('aadhaar', 'AADHAAR')], max_length=20)),
                ('source', models.CharField(choices=[('myoperator', 'MYOPERATOR'), ('heyo', 'HEYO'), ('accounts', 'ACCOUNTS')], default='myoperator', max_length=20)),
                ('status', models.SmallIntegerField(choices=[(0, 'PENDING'), (1, 'COMPLETED'), (2, 'FAILED'), (3, 'EXPIRED')], default=0)),
                ('expiry', models.DateTimeField(default=None, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('billing_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.billingaccounts')),
            ],
            options={
                'db_table': 'kycs',
            },
        ),
        migrations.CreateModel(
            name='KYCState',
            fields=[
                ('id', models.CharField(default=uuid.uuid4, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('state', models.CharField(choices=[('verification', 'VERIFICATION'), ('gst_info', 'GST_INFO'), ('upload_pan', 'UPLOAD_PAN'), ('digilocker_pan', 'DIGILOCKER_PAN'), ('video_kyc', 'VIDEO_KYC'), ('e_sign', 'E_SIGN'), ('kyc_done', 'KYC_DONE')], max_length=30)),
                ('status', models.SmallIntegerField(choices=[(0, 'PENDING'), (1, 'COMPLETED'), (2, 'FAILED')], default=0)),
                ('data', models.JSONField(default=dict)),
                ('task_id', models.CharField(default=None, max_length=36, null=True)),
                ('failure_reason', models.CharField(default=None, max_length=255, null=True)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(default=None, null=True)),
                ('kyc', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='states', to='kyc.kyc')),
            ],
            options={
                'db_table': 'kyc_states',
            },
        ),
    ]
