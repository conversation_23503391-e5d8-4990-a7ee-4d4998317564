from typing import Dict
from accounts.kyc.exceptions import (
    AadhaarParserException,
    KycFileDownloadParserException,
    PanOCRParserException,
)

from accounts.kyc.schemas import <PERSON><PERSON><PERSON><PERSON><PERSON>dd<PERSON>, AadhaarDetail, PanDetail


class AadhaarParser:
    def parse(self, data: dict) -> AadhaarDetail:
        if not data:
            raise AadhaarParserException(
                "[AadhaarParser] No or invalid data field provided."
            )

        address = data.get("address", {})

        return AadhaarDetail(
            client_id=data.get("client_id", ""),
            full_name=data.get("full_name", ""),
            aadhaar_number=data.get("aadhaar_number", ""),
            dob=data.get("dob", ""),
            gender=data.get("gender", ""),
            address=AadhaarAddress(
                country=address.get("country", ""),
                dist=address.get("dist", ""),
                state=address.get("state", ""),
                po=address.get("po", ""),
                loc=address.get("loc", ""),
                vtc=address.get("vtc", ""),
                subdist=address.get("subdist", ""),
                street=address.get("street", ""),
                house=address.get("house", ""),
                landmark=address.get("landmark", ""),
            ),
            face_status=data.get("face_status", False),
            face_score=data.get("face_score", -1),
            zip=data.get("zip", ""),
            profile_image=data.get("profile_image", ""),
            has_image=data.get("has_image", False),
            email_hash=data.get("email_hash", ""),
            mobile_hash=data.get("mobile_hash", ""),
            raw_xml=data.get("raw_xml", ""),
            zip_data=data.get("zip_data", ""),
            care_of=data.get("care_of", ""),
            share_code=data.get("share_code", ""),
            mobile_verified=data.get("mobile_verified", False),
            reference_id=data.get("reference_id", ""),
            aadhaar_pdf=data.get("aadhaar_pdf", None),
            status=data.get("status", ""),
            uniqueness_id=data.get("uniqueness_id", ""),
        )


class AadhaarFileDownloadParser:
    def __init__(self, data: dict):
        self.data = data
        self.pdf_url = ""
        self.client_id = ""

    def get_pdf_url(self, data: Dict) -> str:
        url = data.get("aadhaar_pdf", "")
        if not url:
            raise KycFileDownloadParserException(
                "No PDF URL found in the Aadhaar PDF download API response"
            )
        return url

    def get_client_id(self, data: Dict) -> str:
        client_id = data.get("client_id", "")
        if not client_id:
            raise KycFileDownloadParserException(
                "No client id found in the Aadhaar PDF download API response"
            )
        return client_id

    def parse(self):
        if not self.data.get("data"):
            raise KycFileDownloadParserException(
                "No data found in the Aadhaar PDF download API response"
            )

        data = self.data.get("data", {})
        self.pdf_url = self.get_pdf_url(data)
        self.client_id = self.get_client_id(data)

        return self


class GstFileDownloadParser:
    def __init__(self, data: dict):
        self.data = data
        self.pdf_url = ""
        self.client_id = ""
        self.gstin = ""

    def get_pdf_url(self, data: Dict) -> str:
        url = data.get("pdf_report", "")
        if not url:
            raise KycFileDownloadParserException(
                "No PDF URL found in the response"
            )
        return url

    def get_client_id(self, data: Dict) -> str:
        client_id = data.get("client_id", "")
        if not client_id:
            raise KycFileDownloadParserException(
                "No client ID found in the response"
            )
        return client_id

    def get_gstin(self, data: Dict) -> str:
        gstin = data.get("gstin", "")
        if not gstin:
            raise KycFileDownloadParserException(
                "No GSTIN found in the response"
            )
        return gstin

    def parse(self):
        if not self.data.get("data"):
            raise KycFileDownloadParserException(
                "No data found in the response"
            )

        data = self.data.get("data", {})
        self.pdf_url = self.get_pdf_url(data)
        self.client_id = self.get_client_id(data)
        self.gstin = self.get_gstin(data)
        return self


class PanOCRParser:
    def __init__(self, raw_data: dict):
        self.raw_data = raw_data

    def parse(self) -> PanDetail:
        if not self.raw_data.get("data"):
            raise PanOCRParserException(
                "[PanOCRParser] No or invalid data field provided."
            )

        client_id = self.raw_data["data"].get("client_id", "")
        ocr_fields = self.raw_data["data"].get("ocr_fields", [])
        pan_info = ocr_fields[0] if ocr_fields else {}

        return PanDetail(
            client_id=client_id,
            pan_number=pan_info.get("pan_number", {}).get("value", ""),
            full_name=pan_info.get("full_name", {}).get("value", ""),
            father_name=pan_info.get("father_name", {}).get("value", ""),
            dob=pan_info.get("dob", {}).get("value", ""),
        )
