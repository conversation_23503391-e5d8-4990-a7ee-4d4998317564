from accounts.billing_accounts.models import BillingAccounts
from accounts.kyc.enums import KYCStatusEnum
from accounts.kyc.exceptions import ExistingKycException
from accounts.kyc.models import KYC


def validate_existing_kyc(billing_account: BillingAccounts):
    """Validates if there is an existing KYC for the billing account.

    Args:
        billing_account (BillingAccounts): The billing account.

    Raises:
        ExistingKycException: If there is an existing KYC for the billing account.
    """

    kyc = (
        KYC.objects.filter(billing_account=billing_account)
        .exclude(
            status__in=[
                KYCStatusEnum.EXPIRED.value,
                KYCStatusEnum.FAILED.value,
            ]
        )
        .first()
    )
    if kyc:
        raise ExistingKycException()
