from django.urls import path

from accounts.kyc.views.aadhaar import AadhaarSendOtpView, AadhaarVerifyOtpView
from accounts.kyc.views.gst import (
    GstNonOtpView,
    GstSendOtpView,
    GstVerifyOtpView,
)
from accounts.kyc.views.pan import PanUploadView

app_name = "kyc"

urlpatterns = [
    path(
        "<str:kyc_id>/gst/non-otp",
        view=GstNonOtpView.as_view(),
        name="gst_non_otp",
    ),
    path("gst/send-otp", view=GstSendOtpView.as_view(), name="gst_send_otp"),
    path(
        "gst/verify-otp", view=GstVerifyOtpView.as_view(), name="gst_verify_otp"
    ),
    path(
        "aadhaar/send-otp",
        view=AadhaarSendOtpView.as_view(),
        name="aadhaar-send-otp",
    ),
    path(
        "aadhaar/verify-otp",
        view=AadhaarVerifyOtpView.as_view(),
        name="aadhaar-verify-otp",
    ),
    path(
        "<str:kyc_id>/pan/upload",
        view=PanUploadView.as_view(),
        name="pan-upload",
    ),
]
