from accounts.kyc.exceptions import KycNotFoundException
from accounts.kyc.models import KYC
import logging

logger = logging.getLogger(__name__)


class KycObjMixin:
    lookup_url_kwarg = "kyc_id"

    def get_object(self, request) -> KYC:
        """Fetches the KYC object from the database.

        Args:
            request (Request): The request object.

        Raises:
            KycNotFoundException: If the KYC object does not exist.

        Returns:
            KYC: The KYC object.
        """

        kyc_id = self.kwargs[self.lookup_url_kwarg]  # type: ignore

        try:
            return KYC.objects.get(
                id=kyc_id,
                billing_account=request.billing_account,
            )
        except KYC.DoesNotExist:
            logger.error(
                f"[KycObjMixin] KYC not found for kyc_id: {kyc_id}, billing_account: {request.billing_account_id}"
            )
            raise KycNotFoundException()
