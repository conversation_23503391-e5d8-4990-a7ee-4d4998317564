from accounts.enums import BaseEnum


class KYCModeEnum(BaseEnum):
    GST = "gst"
    AADHAAR = "aadhaar"


class KYCSourceEnum(BaseEnum):
    MYOPERATOR = "myoperator"
    HEYO = "heyo"
    ACCOUNTS = "accounts"


class KYCStatusEnum(BaseEnum):
    PENDING = 0
    COMPLETED = 1
    FAILED = 2
    EXPIRED = 3


class KYCStateEnum(BaseEnum):
    VERIFICATION = "verification"
    GST_INFO = "gst_info"
    UPLOAD_PAN = "upload_pan"
    DIGILOCKER_PAN = "digilocker_pan"
    VIDEO_KYC = "video_kyc"
    E_SIGN = "e_sign"
    KYC_DONE = "kyc_done"


class KYCStateStatusEnum(BaseEnum):
    PENDING = 0
    COMPLETED = 1
    FAILED = 2
