{"data": {"client_id": "aadhaar_v2_MyTbbbjfikBvczitbacn", "full_name": "<PERSON>", "aadhaar_number": "XXXXXXXX3987", "dob": "1953-02-25", "gender": "M", "address": {"country": "India", "dist": "dist_india", "state": "state_india", "po": "po_india", "loc": "loc_india", "vtc": "vtc_india", "subdist": "subdist_india", "street": "street_india", "house": "house_india", "landmark": "landmark_india"}, "face_status": false, "face_score": -1, "zip": "110001", "profile_image": "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", "has_image": true, "email_hash": "", "mobile_hash": "ce4fece6e427314c0eb38bdf8cca7c5f57523d7171163f20aafc009938e73f0c", "raw_xml": "https://test.com/test.test/aadhaar_xml/398720250609161233632/398720250609161233632-2025-06-09-104234624644.xml?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAY5K3QRM5FYWPQJOP%2F20250609%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Date=20250609T104235Z&X-Amz-Expires=432000&X-Amz-SignedHeaders=host&X-Amz-Signature=c9ba736d3a4f5e35a4e6a72b18604cab8d798580d8f985ac05277a72c736540c", "zip_data": "https://test.com/test.test/aadhaar_xml/398720250609161233632/398720250609161233632-2025-06-09-104234463981.zip?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAY5K3QRM5FYWPQJOP%2F20250609%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Date=20250609T104235Z&X-Amz-Expires=432000&X-Amz-SignedHeaders=host&X-Amz-Signature=a6063f18ee50ef3dfe726de3e7af03027769f65fe9d338ff11dca0d8b190af49", "care_of": "S/O care_of", "share_code": "1253", "mobile_verified": false, "reference_id": "398720250609161233642", "aadhaar_pdf": null, "status": "success_a<PERSON><PERSON>ar", "uniqueness_id": "5d3607da34e52bfc6122da3743287449f2fb8963bf44bdf53646de8e9096ca2c"}, "status_code": 200, "success": true, "message": null, "message_code": "success"}