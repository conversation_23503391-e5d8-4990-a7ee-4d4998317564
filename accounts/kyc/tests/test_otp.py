import pytest

from accounts.kyc.otp import <PERSON>stOtpHand<PERSON>
from accounts.exceptions import OTPException, OTPMaxAttemptException
from accounts.kyc.cache_handler import GstOtpCacheHandler
from accounts.utils.api_services.otp import OTPApiService
from accounts.kyc.exceptions import (
    InvalidOtpException,
    OtpDataNotFoundException,
)


@pytest.mark.unittest
def test_gst_otp_handler_setup_send_otp():
    gst_number = "27AAPFU0939F1ZV"
    handler = GstOtpHandler()
    handler.setup_send_otp(gst_number)
    assert handler.otp_cache_handler is not None
    assert handler.api_service is not None
    assert isinstance(handler.otp_cache_handler, GstOtpCacheHandler)
    assert isinstance(handler.api_service, OTPApiService)


@pytest.mark.unittest
def test_gst_otp_handler_setup_verify_otp():
    """Test setup for verifying <PERSON>TP."""
    gst_number = "27AAPFU0939F1ZV"
    handler = GstOtpHandler()
    handler.setup_verify_otp(gst_number)
    assert handler.otp_cache_handler is not None
    assert isinstance(handler.otp_cache_handler, GstOtpCacheHandler)
    assert (
        handler._api_service is None
    )  # API service should not be initialized for verify


@pytest.mark.unittest
def test_gst_otp_handler_send_otp_success(mock_smsg_api):
    """Test successful OTP sending."""
    gst_number = "27AAPFU0939F1ZV"
    phone_number = "+919876543210"
    otp = "1234"

    handler = GstOtpHandler()
    handler.setup_send_otp(gst_number)

    mock_smsg_api()

    result = handler.send_otp(phone_number, otp)
    assert result == otp

    # Verify cache operations
    data = handler.otp_cache_handler.get_otp_data()
    assert data["phone_number"] == phone_number
    assert data["otp"] == otp
    assert data["verification_attempts"] == "0"


@pytest.mark.unittest
def test_gst_otp_handler_send_otp_without_custom_otp(mock_smsg_api):
    """Test OTP generation when not provided."""
    gst_number = "27AAPFU0939F1ZV"
    phone_number = "+919876543210"

    handler = GstOtpHandler()
    handler.setup_send_otp(gst_number)

    mock_smsg_api()

    generated_otp = handler.send_otp(phone_number)
    assert generated_otp is not None
    assert isinstance(generated_otp, str)
    assert len(generated_otp) == 4
    assert generated_otp.isdigit()
    data = handler.otp_cache_handler.get_otp_data()
    assert data["phone_number"] == phone_number
    assert data["otp"] == generated_otp
    assert data["verification_attempts"] == "0"


@pytest.mark.unittest
def test_gst_otp_handler_send_otp_max_attempts_exceeded():
    """Test OTP sending when max attempts exceeded."""
    gst_number = "27AAPFU0939F1ZV"
    phone_number = "+919876543210"

    handler = GstOtpHandler()
    handler.setup_send_otp(gst_number)

    # Increment attempts to max
    for _ in range(5):
        handler.otp_cache_handler.incr_send_attempt()

    with pytest.raises(OTPException, match="Failed to send OTP"):
        handler.send_otp(phone_number)


@pytest.mark.unittest
def test_gst_otp_handler_verify_otp_success():
    """Test successful OTP verification."""
    gst_number = "27AAPFU0939F1ZV"
    otp = "1234"
    phone_number = "+919876543210"

    handler = GstOtpHandler()
    handler.setup_verify_otp(gst_number)

    # Set up OTP data
    handler.otp_cache_handler.set_otp_data(phone_number, otp, "0")

    result = handler.verify_otp(otp)
    assert result == otp

    # Verify cache operations
    data = handler.otp_cache_handler.get_otp_data()
    assert data["verification_attempts"] == "1"
    assert data["verified"] == "true"


@pytest.mark.unittest
def test_gst_otp_handler_verify_otp_invalid():
    """Test OTP verification with invalid OTP."""
    gst_number = "27AAPFU0939F1ZV"
    otp = "1234"
    phone_number = "+919876543210"

    handler = GstOtpHandler()
    handler.setup_verify_otp(gst_number)

    # Set up OTP data with different OTP
    handler.otp_cache_handler.set_otp_data(phone_number, "5678", "0")

    with pytest.raises(
        InvalidOtpException, match="Incorrect OTP. Please try again."
    ):
        handler.verify_otp(otp)

    data = handler.otp_cache_handler.get_otp_data()
    assert data["verification_attempts"] == "1"
    assert data.get("verified") is None


@pytest.mark.unittest
def test_gst_otp_handler_verify_otp_max_attempts():
    """Test OTP verification when max attempts exceeded."""
    gst_number = "27AAPFU0939F1ZV"
    otp = "1234"
    phone_number = "+919876543210"

    handler = GstOtpHandler()
    handler.setup_verify_otp(gst_number)

    # Set up OTP data
    handler.otp_cache_handler.set_otp_data(phone_number, otp, "0")

    # Increment verification attempts to max
    for _ in range(handler.otp_cache_handler.MAX_VERIFY_ATTEMPTS + 1):
        handler.otp_cache_handler.incr_verification_attempt()

    with pytest.raises(
        OTPMaxAttemptException,
        match=f"OTP verification attempts exceeded. Maximum allowed: {handler.otp_cache_handler.MAX_VERIFY_ATTEMPTS}",
    ):
        handler.validate_max_verification_attempts()


@pytest.mark.unittest
def test_gst_otp_handler_verify_otp_not_found():
    """Test OTP verification when OTP data not found."""
    gst_number = "27AAPFU0939F1ZV"
    otp = "1234"

    handler = GstOtpHandler()
    handler.setup_verify_otp(gst_number)

    with pytest.raises(
        OtpDataNotFoundException, match="OTP expired. Please request a new OTP."
    ):
        handler.verify_otp(otp)
