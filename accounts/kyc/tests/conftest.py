from typing import Any, Dict, Optional

import pytest
from django.conf import settings
from rest_framework import status

from accounts.billing_accounts.enums import DocTypeEnums
from accounts.billing_accounts.tests.factories import BillingAccountFactory
from accounts.tests.factories.billing_accounts import DocTypeFactory


@pytest.fixture
def billing_account() -> Any:
    """Create a billing account fixture."""
    return BillingAccountFactory()


@pytest.fixture
def gst_doc_type() -> Any:
    """Create GST document type fixture."""
    return DocTypeFactory(
        short_code=DocTypeEnums.GST.value,
        name=DocTypeEnums.GST.name,
    )


@pytest.fixture
def aadhaar_doc_type() -> Any:
    """Create Aadhaar document type fixture."""
    return DocTypeFactory(
        short_code=DocTypeEnums.AADHAAR_CARD.value,
        name=DocTypeEnums.AADHAAR_CARD.name,
    )


@pytest.fixture
def mock_gst_detail_api_with_custom_data(load_json, mock_gst_detail_api):
    def wrap(
        mobile_number: Optional[str] = None,
        gst_number: Optional[str] = None,
        gst_status: Optional[str] = None,
        expected_res: Optional[Dict[str, Any]] = None,
        status_code: int = status.HTTP_200_OK,
    ):
        if not expected_res:
            expected_res = load_json(
                "accounts/kyc/tests/fixtures/gst_details.json"
            )

        if mobile_number and expected_res:
            expected_res["data"]["contact_details"]["principal"][
                "mobile"
            ] = mobile_number
        if gst_number and expected_res:
            expected_res["data"]["gstin"] = gst_number
        if gst_status and expected_res:
            expected_res["data"]["gstin_status"] = gst_status

        res = mock_gst_detail_api(expected_res, status_code)
        return res

    return wrap


@pytest.fixture
def api_headers(billing_account):
    return {
        f"HTTP_{settings.BILLING_ACCOUNT_HEADER_NAME}": billing_account.id,
    }


@pytest.fixture
def valid_gst_number(load_json):
    return load_json("accounts/kyc/tests/fixtures/gst_details.json")["data"][
        "gstin"
    ]


@pytest.fixture
def valid_aadhaar_number():
    return "************"


@pytest.fixture
def valid_aadhaar_client_id():
    return "aadhaar_v2_MyTbbbjfikBvczitbacn"


@pytest.fixture
def valid_aadhaar_otp():
    return "123456"
