import io
import pytest
from moto import mock_aws
from django.conf import settings
from accounts.billing_accounts.enums import DocTypeEnums
from accounts.kyc.handlers import KycHand<PERSON>
from accounts.kyc.enums import KYCModeEnum, KYCStatusEnum
from accounts.kyc.exceptions import KycFailedException
from tests.factories.billing_accounts import DocTypeFactory


@pytest.mark.parametrize(
    "init_kyc,kyc_mode",
    [
        (KycHandler.init_gst_kyc, KYCModeEnum.GST),
        (KycHandler.init_aadhaar_kyc, KYCModeEnum.AADHAAR),
    ],
)
@pytest.mark.django_db
@pytest.mark.unittest
def test_init_kyc(billing_account, init_kyc, kyc_mode):
    """Test initializing KYC for both GST and Aadhaar modes."""

    kyc = init_kyc(billing_account, {})
    assert kyc.mode == kyc_mode.value
    assert kyc.billing_account == billing_account
    assert kyc.status == KYCStatusEnum.PENDING.value


@pytest.mark.parametrize(
    "request_data",
    [
        {
            "init_kyc": KycHandler.init_gst_kyc,
            "url": "https://example.com/path/to/file.pdf",
            "file_name": "file.pdf",
        },
        {
            "init_kyc": KycHandler.init_gst_kyc,
            "url": "https://example.com/path/to/file.jpg",
            "file_name": "file.jpg",
        },
        {
            "init_kyc": KycHandler.init_gst_kyc,
            "url": "https://example.com/path/to/file.png",
            "file_name": "file.png",
        },
        {
            "init_kyc": KycHandler.init_aadhaar_kyc,
            "url": "https://example.com/path/to/file.pdf",
            "file_name": "file.pdf",
        },
        {
            "init_kyc": KycHandler.init_aadhaar_kyc,
            "url": "https://example.com/path/to/file.jpg",
            "file_name": "file.jpg",
        },
        {
            "init_kyc": KycHandler.init_aadhaar_kyc,
            "url": "https://example.com/path/to/file.png",
            "file_name": "file.png",
        },
    ],
)
@pytest.mark.django_db
@pytest.mark.unittest
def test_get_file_name(billing_account, request_data):
    """Test getting file name from URL."""
    kyc = request_data["init_kyc"](billing_account, {})
    handler = KycHandler(kyc)
    file_name = handler.get_file_name(request_data["url"])
    assert file_name == request_data["file_name"]


@pytest.mark.parametrize(
    "init_kyc",
    [KycHandler.init_gst_kyc, KycHandler.init_aadhaar_kyc],
)
@pytest.mark.django_db
@pytest.mark.unittest
def test_upload_file_to_s3(billing_account, mock_s3_bucket, init_kyc):
    """Test uploading file to S3."""
    file_data = io.BytesIO(b"test content")
    extension = "pdf"
    kyc = init_kyc(billing_account, {})
    handler = KycHandler(kyc)
    s3_path = handler.upload_file_to_s3(file_data, extension)

    # Verify file was uploaded
    response = mock_s3_bucket.get_object(
        Bucket=settings.AWS_STORAGE_BUCKET_NAME, Key=s3_path
    )
    assert response["Body"].read() == b"test content"


@pytest.mark.parametrize(
    "init_kyc",
    [KycHandler.init_gst_kyc, KycHandler.init_aadhaar_kyc],
)
@pytest.mark.unittest
@pytest.mark.django_db
@mock_aws
def test_upload_file_to_s3_failure(billing_account, init_kyc):
    """Test S3 upload failure."""
    file_data = io.BytesIO(b"test content")
    extension = "pdf"
    kyc = init_kyc(billing_account, {})
    handler = KycHandler(kyc)
    # Delete bucket to simulate failure
    with pytest.raises(KycFailedException) as exc_info:
        handler.upload_file_to_s3(file_data, extension)
    assert "S3 File Upload Failed" in str(exc_info.value)


@pytest.mark.parametrize(
    "request_data",
    [
        {
            "init_kyc": KycHandler.init_gst_kyc,
            "url": "https://example.com/path/to/file.pdf",
        },
        {
            "init_kyc": KycHandler.init_gst_kyc,
            "url": "https://example.com/path/to/file.jpg",
        },
        {
            "init_kyc": KycHandler.init_gst_kyc,
            "url": "https://example.com/path/to/file.png",
        },
        {
            "init_kyc": KycHandler.init_aadhaar_kyc,
            "url": "https://example.com/path/to/file.pdf",
        },
        {
            "init_kyc": KycHandler.init_aadhaar_kyc,
            "url": "https://example.com/path/to/file.jpg",
        },
        {
            "init_kyc": KycHandler.init_aadhaar_kyc,
            "url": "https://example.com/path/to/file.png",
        },
    ],
)
@pytest.mark.unittest
@pytest.mark.django_db
def test_download_file_from_url(
    load_json, billing_account, mock_s3_bucket_download, request_data
):
    """Test downloading file from URL."""

    s3_download_api_res = mock_s3_bucket_download(url=request_data["url"])
    kyc = request_data["init_kyc"](billing_account, {})
    handler = KycHandler(kyc)

    file_data = handler.download_file_from_url(request_data["url"])
    assert isinstance(file_data, io.BytesIO)
    assert file_data.read() == b"test content"
    assert s3_download_api_res.call_count == 1


@pytest.mark.parametrize(
    "request_data",
    [
        {
            "init_kyc": KycHandler.init_gst_kyc,
            "doc_type": DocTypeEnums.GST,
            "doc_number": "27AAPFU0939F1ZV",
        },
        {
            "init_kyc": KycHandler.init_aadhaar_kyc,
            "doc_type": DocTypeEnums.AADHAAR_CARD,
            "doc_number": "************",
        },
    ],
)
@pytest.mark.unittest
@pytest.mark.django_db
def test_create_doc(billing_account, mock_s3_bucket, request_data):
    """Test creating billing account doc."""
    doc_type = DocTypeFactory(
        short_code=request_data["doc_type"].value,
        name=request_data["doc_type"].name,
    )
    kyc = request_data["init_kyc"](billing_account, {})
    handler = KycHandler(kyc)
    file_data = io.BytesIO(b"test content")
    extension = "pdf"
    s3_path = handler.upload_file_to_s3(file_data, extension)

    # Create doc
    doc = handler.create_doc(
        doc_name="test_doc",
        doc_ext=extension,
        s3_path=s3_path,
        doc_type=request_data["doc_type"],
        doc_number=request_data["doc_number"],
    )

    assert doc.kyc == kyc
    assert doc.doc_type == doc_type
    assert doc.doc_number == request_data["doc_number"]
    assert doc.doc_path == s3_path
    assert doc.doc_name == "test_doc"
    assert doc.doc_ext == extension
    assert doc.billing_account == kyc.billing_account
    assert doc.kyc == kyc
