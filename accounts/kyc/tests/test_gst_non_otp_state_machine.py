import pytest
from accounts.billing_accounts.models import BillingAccountDocs
from accounts.kyc.cache_handler import GstCacheHandler
from accounts.kyc.enums import (
    KYCModeEnum,
    KYCStateEnum,
    KYCStatusEnum,
    KYCStateStatusEnum,
)
from accounts.kyc.models import KYC
from accounts.kyc.sm.statemachines import PersistedKYCStateMachine
from statemachine.exceptions import TransitionNotAllowed

from accounts.kyc.tests.factories import KYCFactory


@pytest.mark.django_db
@pytest.mark.unittest
def test_verification_to_gst_info_transition(
    billing_account,
    valid_gst_number,
    mock_gst_detail_api_with_custom_data,
    mock_surepass_api_gst_pdf,
    mock_s3_bucket_download,
    mock_s3_bucket,
    load_json,
    gst_doc_type,
    settings,
):
    """Test the state machine transition from verification to gst_info."""
    # Create initial KYC
    kyc = KYCFactory(
        billing_account=billing_account,
        status=KYCStatusEnum.PENDING.value,
        mode=KYCModeEnum.AADHAAR.value,
        current_state=KYCStateEnum.VERIFICATION.value,
    )
    kyc.create_kyc_state(data={"gst_number": valid_gst_number}, task_id="123")
    kyc.mark_latest_state_as_completed()

    gst_data = load_json("accounts/kyc/tests/fixtures/gst_details.json")
    # Setup API response
    gst_detail_api_res = mock_gst_detail_api_with_custom_data(
        mobile_number="**********",
        gst_number=valid_gst_number,
        expected_res=gst_data,
    )

    gst_pdf_api_res = mock_surepass_api_gst_pdf()
    s3_download_api_res = mock_s3_bucket_download(
        url=load_json("accounts/kyc/tests/fixtures/gst_pdf_success.json")[
            "data"
        ]["pdf_report"]
    )

    # Create state machine
    state_machine = PersistedKYCStateMachine(kyc)

    # Test transition
    task_id = state_machine.verification_to_gst_info(valid_gst_number)

    assert gst_detail_api_res.call_count == 1
    assert gst_pdf_api_res.call_count == 1
    assert s3_download_api_res.call_count == 1

    assert GstCacheHandler(valid_gst_number).get() == gst_data["data"]
    # Verify state changes
    kyc.refresh_from_db()
    assert kyc.current_state == KYCStateEnum.GST_INFO.value
    assert kyc.status == KYCStatusEnum.PENDING.value

    # Verify new state creation
    state = kyc.get_latest_state()
    assert state is not None
    assert state.state == KYCStateEnum.GST_INFO.value
    assert state.status == KYCStateStatusEnum.COMPLETED.value
    assert state.task_id == task_id
    assert state.failure_reason is None
    # Verify document creation
    doc_qs = BillingAccountDocs.objects.filter(
        kyc=kyc,
        doc_type=gst_doc_type,
        doc_number=valid_gst_number,
        doc_name="pdf_report_gstin_1749201959545362",
        doc_ext="pdf",
        status=True,
    )
    assert doc_qs.count() == 1
    doc: BillingAccountDocs = doc_qs.first()  # type: ignore

    s3_obj_response = mock_s3_bucket.get_object(
        Bucket=settings.AWS_STORAGE_BUCKET_NAME,
        Key=doc.doc_path,
    )
    assert s3_obj_response["Body"].read() == b"test content"


@pytest.mark.django_db
@pytest.mark.unittest
def test_verification_to_gst_info_transition_conditions(
    billing_account,
    valid_gst_number,
):
    """Test the conditions for verification_to_gst_info transition."""
    # Create initial KYC
    kyc = KYCFactory(
        billing_account=billing_account,
        status=KYCStatusEnum.PENDING.value,
        mode=KYCModeEnum.AADHAAR.value,
        current_state=KYCStateEnum.VERIFICATION.value,
    )
    kyc.create_kyc_state(data={"gst_number": valid_gst_number}, task_id="123")

    # Create state machine
    state_machine = PersistedKYCStateMachine(kyc)

    # Test conditions
    assert state_machine.is_kyc_mode_aadhaar() is True
    assert (
        state_machine.is_verification_completed() is False
    )  # Initial state is not completed

    # Complete verification state
    kyc.mark_latest_state_as_completed(state=KYCStateEnum.VERIFICATION)
    assert state_machine.is_verification_completed() is True
    assert state_machine.is_kyc_mode_aadhaar() is True


@pytest.mark.django_db
@pytest.mark.unittest
def test_verification_to_gst_info_transition_invalid_mode(
    billing_account, valid_gst_number
):
    """Test transition with invalid KYC mode."""
    # Create initial KYC with GST mode
    kyc = KYCFactory(
        billing_account=billing_account,
        status=KYCStatusEnum.PENDING.value,
        mode=KYCModeEnum.GST.value,  # GST mode instead of AADHAAR
        current_state=KYCStateEnum.VERIFICATION.value,
    )
    kyc.create_kyc_state(data={})

    # Create state machine
    state_machine = PersistedKYCStateMachine(kyc)

    # Test transition should fail
    with pytest.raises(TransitionNotAllowed):
        state_machine.verification_to_gst_info(valid_gst_number)
