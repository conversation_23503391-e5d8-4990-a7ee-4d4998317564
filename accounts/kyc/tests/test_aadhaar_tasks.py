from django.conf import settings

import pytest
from rest_framework import status

from accounts.kyc.enums import (
    KYCModeEnum,
    KYCStateEnum,
    KYCStateStatusEnum,
    KYCStatusEnum,
)
from accounts.kyc.exceptions import (
    KycFailedException,
    KycFileDownloadException,
    KycNotFoundException,
)
from accounts.kyc.models import KYC, KYCState
from accounts.kyc.tasks import download_aadhaar_pdf_task
from accounts.utils.common import get_trace_id
from accounts.exceptions import ExternalAPIException


@pytest.mark.django_db
@pytest.mark.unittest
def test_download_aadhaar_pdf_task(
    load_json,
    mock_aadhaar_download_pdf_api,
    mock_s3_bucket_download,
    mock_s3_bucket,
    aadhaar_doc_type,
    billing_account,
    valid_aadhaar_number,
    valid_aadhaar_client_id,
):
    """Test downloading Aadhaar PDF and verifying document creation.

    This test verifies that:
    1. Aadhaar PDF is downloaded successfully
    2. KYC verification state status is updated to completed
    3. Document is created with correct details
    4. File is uploaded to S3
    """

    aadhaar_download_pdf_api_200_success_response = load_json(
        "accounts/kyc/tests/fixtures/aadhaar_download_pdf_api_200_success_response.json"
    )
    # mock aadhaar download pdf api
    mocked_aadhaar_download_pdf_api = mock_aadhaar_download_pdf_api()
    # mock s3 download api
    mocked_s3_bucket_download = mock_s3_bucket_download(
        url=aadhaar_download_pdf_api_200_success_response["data"]["aadhaar_pdf"]
    )
    # Setup test data
    aadhaar_data = load_json(
        "accounts/kyc/tests/fixtures/aadhaar_verify_otp_api_200_success_response.json"
    )["data"]
    kyc = KYC.initialize_kyc(
        mode=KYCModeEnum.AADHAAR,
        billing_account=billing_account,
        data=aadhaar_data,
    )
    # Execute task
    task = download_aadhaar_pdf_task.apply_async(args=[valid_aadhaar_number, valid_aadhaar_client_id, kyc.id])  # type: ignore
    assert task.successful()
    # Verify results
    kyc.refresh_from_db()
    assert kyc.status == KYCStatusEnum.PENDING.value
    # Verify state changes
    latest_state = kyc.get_latest_state()
    assert latest_state is not None
    assert latest_state.state == KYCStateEnum.VERIFICATION.value
    assert latest_state.status == KYCStateStatusEnum.COMPLETED.value
    assert latest_state.data == aadhaar_data
    assert not latest_state.failure_reason
    assert latest_state.started_at
    assert latest_state.completed_at
    # Verify document creation
    doc = kyc.billing_account.get_docs().first()
    assert doc is not None
    assert doc.doc_type == aadhaar_doc_type
    assert doc.doc_number == valid_aadhaar_number
    assert doc.kyc == kyc
    assert doc.doc_name == "398720250611143358901-2025-06-11-************"
    assert doc.doc_ext == "pdf"
    assert doc.doc_path.startswith("companydoc/")
    # Verify S3 upload
    response = mock_s3_bucket.get_object(
        Bucket=settings.AWS_STORAGE_BUCKET_NAME, Key=doc.doc_path
    )
    assert response["Body"].read() == b"test content"
    assert mocked_s3_bucket_download.call_count == 1
    assert mocked_aadhaar_download_pdf_api.call_count == 1

    # assert API calls
    assert mocked_aadhaar_download_pdf_api.call_count == 1
    assert mocked_s3_bucket_download.call_count == 1


@pytest.mark.django_db
@pytest.mark.unittest
def test_download_aadhaar_pdf_task_api_failure(
    load_json,
    mock_aadhaar_download_pdf_api,
    billing_account,
    valid_aadhaar_number,
    valid_aadhaar_client_id,
):
    """Test downloading Aadhaar PDF when API call fails.

    This test verifies that:
    1. Task handles API failure gracefully
    2. KYC and KYC state status is updated to failed
    3. Failure reason is logged and stored in Database
    4. No Document is created
    """

    # mock aadhaar download pdf api
    mocked_aadhaar_download_pdf_api = mock_aadhaar_download_pdf_api(
        expected_response={}, status_code=status.HTTP_503_SERVICE_UNAVAILABLE
    )
    # Setup test data
    aadhaar_data = load_json(
        "accounts/kyc/tests/fixtures/aadhaar_verify_otp_api_200_success_response.json"
    )["data"]
    kyc = KYC.initialize_kyc(
        mode=KYCModeEnum.AADHAAR,
        billing_account=billing_account,
        data=aadhaar_data,
    )
    # Execute task and expect exception
    with pytest.raises(KycFileDownloadException):
        task = download_aadhaar_pdf_task.apply_async(args=[valid_aadhaar_number, valid_aadhaar_client_id, kyc.id])  # type: ignore
        assert not task.successful()
    # Verify results
    kyc.refresh_from_db()
    assert kyc.status == KYCStatusEnum.FAILED.value
    # Verify state changes
    latest_state = kyc.get_latest_state()
    assert latest_state is not None
    assert latest_state.state == KYCStateEnum.VERIFICATION.value
    assert latest_state.status == KYCStateStatusEnum.FAILED.value
    assert latest_state.data == aadhaar_data
    assert (
        latest_state.failure_reason
        == f"KycFileDownloadException: Failed to download Aadhaar PDF., trace_id: {get_trace_id()}"
    )
    assert latest_state.started_at
    assert latest_state.completed_at is None
    # Verify document creation
    doc = kyc.billing_account.get_docs().first()
    assert doc is None
    # assert API calls
    assert mocked_aadhaar_download_pdf_api.call_count == 1


@pytest.mark.django_db
@pytest.mark.unittest
def test_download_aadhaar_pdf_task_download_failure(
    load_json,
    mock_aadhaar_download_pdf_api,
    mock_s3_bucket_download,
    billing_account,
    valid_aadhaar_number,
    valid_aadhaar_client_id,
):
    """Test downloading Aadhaar PDF when file download from URL fails.

    This test verifies that:
    1. Task handles file download failure gracefully
    2. KYC and KYC state status is updated to failed
    3. Failure reason is logged and stored in Database
    4. No Document is created
    """

    aadhaar_download_pdf_api_200_success_response = load_json(
        "accounts/kyc/tests/fixtures/aadhaar_download_pdf_api_200_success_response.json"
    )
    # mock aadhaar download pdf api
    mocked_aadhaar_download_pdf_api = mock_aadhaar_download_pdf_api()
    # mock s3 download api
    mocked_s3_bucket_download = mock_s3_bucket_download(
        url=aadhaar_download_pdf_api_200_success_response["data"][
            "aadhaar_pdf"
        ],
        status_code=status.HTTP_404_NOT_FOUND,
    )
    # Setup test data
    aadhaar_data = load_json(
        "accounts/kyc/tests/fixtures/aadhaar_verify_otp_api_200_success_response.json"
    )["data"]
    kyc = KYC.initialize_kyc(
        mode=KYCModeEnum.AADHAAR,
        billing_account=billing_account,
        data=aadhaar_data,
    )
    # Execute task and expect exception
    with pytest.raises(ExternalAPIException):
        task = download_aadhaar_pdf_task.apply_async(args=[valid_aadhaar_number, valid_aadhaar_client_id, kyc.id])  # type: ignore
        assert not task.successful()
    # Verify results
    kyc.refresh_from_db()
    assert kyc.status == KYCStatusEnum.FAILED.value
    # Verify state changes
    latest_state = kyc.get_latest_state()
    assert latest_state is not None
    assert latest_state.state == KYCStateEnum.VERIFICATION.value
    assert latest_state.status == KYCStateStatusEnum.FAILED.value
    assert latest_state.data == aadhaar_data
    assert (
        latest_state.failure_reason
        == f"ExternalAPIException: {status.HTTP_404_NOT_FOUND} Client Error: Not Found for url: {aadhaar_download_pdf_api_200_success_response['data']['aadhaar_pdf']}, trace_id: {get_trace_id()}"
    )
    assert latest_state.started_at
    assert latest_state.completed_at is None
    # Verify document creation
    doc = kyc.billing_account.get_docs().first()
    assert doc is None
    # assert API calls
    assert mocked_aadhaar_download_pdf_api.call_count == 1
    assert mocked_s3_bucket_download.call_count == 1


@pytest.mark.django_db
@pytest.mark.unittest
def test_download_aadhaar_pdf_task_s3_upload_failure(
    load_json,
    mock_aadhaar_download_pdf_api,
    mock_s3_bucket_download,
    billing_account,
    valid_aadhaar_number,
    valid_aadhaar_client_id,
):
    """Test downloading Aadhaar PDF when S3 upload fails.

    This test verifies that:
    1. Task handles S3 upload failure gracefully
    2. KYC and KYC state status is updated to failed
    3. Failure reason is logged and stored in Database
    """

    aadhaar_download_pdf_api_200_success_response = load_json(
        "accounts/kyc/tests/fixtures/aadhaar_download_pdf_api_200_success_response.json"
    )
    # mock aadhaar download pdf api
    mocked_aadhaar_download_pdf_api = mock_aadhaar_download_pdf_api()
    # mock s3 download api
    mocked_s3_bucket_download = mock_s3_bucket_download(
        url=aadhaar_download_pdf_api_200_success_response["data"][
            "aadhaar_pdf"
        ],
    )
    # Setup test data
    aadhaar_data = load_json(
        "accounts/kyc/tests/fixtures/aadhaar_verify_otp_api_200_success_response.json"
    )["data"]
    kyc = KYC.initialize_kyc(
        mode=KYCModeEnum.AADHAAR,
        billing_account=billing_account,
        data=aadhaar_data,
    )
    # Execute task and expect exception
    with pytest.raises(KycFailedException):
        task = download_aadhaar_pdf_task.apply_async(args=[valid_aadhaar_number, valid_aadhaar_client_id, kyc.id])  # type: ignore
        assert not task.successful()
    # Verify results
    kyc.refresh_from_db()
    assert kyc.status == KYCStatusEnum.FAILED.value
    # Verify state changes
    latest_state = kyc.get_latest_state()
    assert latest_state is not None
    assert latest_state.state == KYCStateEnum.VERIFICATION.value
    assert latest_state.status == KYCStateStatusEnum.FAILED.value
    assert latest_state.data == aadhaar_data
    assert (
        latest_state.failure_reason
        == f"KycFailedException: S3 File Upload Failed., trace_id: {get_trace_id()}"
    )
    assert latest_state.started_at
    assert latest_state.completed_at is None
    # Verify document creation
    doc = kyc.billing_account.get_docs().first()
    assert doc is None
    # assert API calls
    assert mocked_aadhaar_download_pdf_api.call_count == 1
    assert mocked_s3_bucket_download.call_count == 1


@pytest.mark.django_db
@pytest.mark.unittest
def test_download_aadhaar_pdf_task_kyc_not_found(
    valid_aadhaar_number,
    valid_aadhaar_client_id,
):
    """Test downloading Aadhaar PDF when KYC object is not found.

    This test verifies that:
    1. Task handles non-existent KYC gracefully
    2. Appropriate exception is raised
    """

    # assert no KYC exist
    assert KYC.objects.count() == 0
    assert KYCState.objects.count() == 0
    # Execute task with non-existing KYC
    with pytest.raises(KycNotFoundException):
        task = download_aadhaar_pdf_task.apply_async(args=[valid_aadhaar_number, valid_aadhaar_client_id, "non-existing-kyc-id"])  # type: ignore
        assert not task.successful()
    # assert no KYC exist
    assert KYC.objects.count() == 0
    assert KYCState.objects.count() == 0
