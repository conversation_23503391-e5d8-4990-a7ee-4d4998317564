import pytest
from accounts.kyc.parsers import (
    AadhaarFileDownloadParser,
    GstFileDownloadParser,
)
from accounts.kyc.exceptions import KycFileDownloadParserException


@pytest.mark.unittest
def test_kyc_file_download_parser_success(load_json):
    """Test successful parsing of KYC file download response."""
    data = load_json("accounts/kyc/tests/fixtures/gst_pdf_success.json")

    parser = GstFileDownloadParser(data)
    result = parser.parse()

    assert result.pdf_url == data["data"]["pdf_report"]
    assert result.client_id == data["data"]["client_id"]
    assert result.gstin == data["data"]["gstin"]


@pytest.mark.unittest
def test_kyc_file_download_parser_missing_data():
    """Test parser with missing data field."""
    data = {}

    parser = GstFileDownloadParser(data)
    with pytest.raises(KycFileDownloadParserException) as exc_info:
        parser.parse()

    assert str(exc_info.value) == "No data found in the response"


@pytest.mark.unittest
def test_kyc_file_download_parser_missing_pdf_url(load_json):
    """Test parser with missing PDF URL."""
    data = load_json("accounts/kyc/tests/fixtures/gst_pdf_success.json")
    del data["data"]["pdf_report"]

    parser = GstFileDownloadParser(data)
    with pytest.raises(KycFileDownloadParserException) as exc_info:
        parser.parse()

    assert str(exc_info.value) == "No PDF URL found in the response"


@pytest.mark.unittest
def test_kyc_file_download_parser_missing_client_id(load_json):
    """Test parser with missing client ID."""
    data = load_json("accounts/kyc/tests/fixtures/gst_pdf_success.json")
    del data["data"]["client_id"]

    parser = GstFileDownloadParser(data)
    with pytest.raises(KycFileDownloadParserException) as exc_info:
        parser.parse()

    assert str(exc_info.value) == "No client ID found in the response"


@pytest.mark.unittest
def test_kyc_file_download_parser_missing_gstin(load_json):
    """Test parser with missing GSTIN."""
    data = load_json("accounts/kyc/tests/fixtures/gst_pdf_success.json")
    del data["data"]["gstin"]

    parser = GstFileDownloadParser(data)
    with pytest.raises(KycFileDownloadParserException) as exc_info:
        parser.parse()

    assert str(exc_info.value) == "No GSTIN found in the response"


@pytest.mark.unittest
def test_aadhaar_file_download_parser_success(load_json):
    """Test successful parsing of Aadhaar file download response."""

    data = load_json(
        "accounts/kyc/tests/fixtures/aadhaar_download_pdf_api_200_success_response.json"
    )

    parser = AadhaarFileDownloadParser(data)
    result = parser.parse()

    assert result.pdf_url == data["data"]["aadhaar_pdf"]
    assert result.client_id == data["data"]["client_id"]


@pytest.mark.unittest
def test_aadhaar_file_download_parser_missing_data():
    """Test parser with missing data field."""

    data = {}

    parser = AadhaarFileDownloadParser(data)
    with pytest.raises(KycFileDownloadParserException) as exc_info:
        parser.parse()

    assert (
        str(exc_info.value)
        == "No data found in the Aadhaar PDF download API response"
    )


@pytest.mark.unittest
def test_aadhaar_file_download_parser_missing_pdf_url(load_json):
    """Test parser with missing PDF URL."""

    data = load_json(
        "accounts/kyc/tests/fixtures/aadhaar_download_pdf_api_200_success_response.json"
    )
    del data["data"]["aadhaar_pdf"]

    parser = AadhaarFileDownloadParser(data)
    with pytest.raises(KycFileDownloadParserException) as exc_info:
        parser.parse()

    assert (
        str(exc_info.value)
        == "No PDF URL found in the Aadhaar PDF download API response"
    )


@pytest.mark.unittest
def test_aadhaar_file_download_parser_missing_client_id(load_json):
    """Test parser with missing client ID."""

    data = load_json(
        "accounts/kyc/tests/fixtures/aadhaar_download_pdf_api_200_success_response.json"
    )
    del data["data"]["client_id"]

    parser = AadhaarFileDownloadParser(data)
    with pytest.raises(KycFileDownloadParserException) as exc_info:
        parser.parse()

    assert (
        str(exc_info.value)
        == "No client id found in the Aadhaar PDF download API response"
    )
