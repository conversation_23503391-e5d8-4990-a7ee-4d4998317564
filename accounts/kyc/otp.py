import logging
from typing import Optional
from accounts.kyc.cache_handler import (
    GstOtpCacheHandler,
)

from accounts.kyc.exceptions import (
    InvalidOtpException,
)
from accounts.utils.api_services.otp import OTPApiService
from accounts.utils.otp import BaseOtpHandler

logger = logging.getLogger(__name__)


class GstOtpHandler(BaseOtpHandler):
    def _setup_otp_cache_handler(self, gst_number: str):
        self.otp_cache_handler: GstOtpCacheHandler = GstOtpCacheHandler(
            gst_number
        )

    def setup_send_otp(self, gst_number: str):
        self._setup_otp_cache_handler(gst_number)
        self.api_service: OTPApiService = OTPApiService()

    def setup_verify_otp(self, gst_number: str):
        self._setup_otp_cache_handler(gst_number)

    def _send_otp(self, number: str, otp: str):
        country_code, phone_number = self.get_country_code_and_phone_number(
            number
        )
        self.send(str(country_code), str(phone_number), otp)

    def _verify_otp(self, otp_data: dict, otp: str):
        if otp_data.get("otp") != otp:
            logger.error(f"[GstOtpHandler] Invalid OTP: {otp} for {otp_data}")
            raise InvalidOtpException()

    def send_otp(self, number: str, otp: Optional[str] = None) -> str:
        if not otp:
            otp = self.generate_otp()
        self._send_otp(number, otp)
        self.otp_cache_handler.incr_send_attempt()
        self.otp_cache_handler.set_otp_data(number, otp)
        return otp

    def verify_otp(self, otp: str):
        otp_data = self.otp_cache_handler.get_otp_data()
        self.otp_cache_handler.incr_verification_attempt()
        self._verify_otp(otp_data, otp)
        self.otp_cache_handler.mark_as_verified(otp_data)
        return otp
