import os

from django.conf import settings
from rest_framework import authentication, exceptions
from accounts.users.models import ApiKeys


class SnsAuthentication(authentication.BasicAuthentication):
    def authenticate(self, request):
        """
        Returns a `User` if a correct username and password have been supplied
        using HTTP Basic authentication.  Otherwise returns `None`.
        """
        auth = authentication.get_authorization_header(request).split()

        if not auth or auth[0].lower() != b"basic":
            raise exceptions.AuthenticationFailed(
                "No authentication details found."
            )
        super().authenticate(request)

    def authenticate_credentials(self, user_id, password, request=None):
        if f"{user_id}:{password}" != settings.SNS_EVENT_AUTH_USER_PWD:
            raise exceptions.AuthenticationFailed("Invalid username/password.")


class UserAuthorization(authentication.BasicAuthentication):
    """
    Custom user authentication class.
    """

    def authenticate(self, request):
        if "Authorization" not in request.headers:
            raise exceptions.AuthenticationFailed(
                {"message": "Invalid token"},
            )

        api_key = request.headers["Authorization"]
        try:
            token = ApiKeys.objects.get(api_key=api_key, status=1)
        except ApiKeys.DoesNotExist:
            raise exceptions.AuthenticationFailed(
                {"message": "Invalid token"},
            )
        return (token.user_profile, None)

    def authenticate_header(self, request):
        return "Token"
