from django.urls import path

from accounts.offers.views import (
    OfferListCreateAPIView,
    OfferPaymentListCreateAPIView,
    OfferPaymentRetrieveAPIView,
    OfferRetrieveUpdateAPIView,
    OfferEventProcessView,
)

app_name = "offers"

urlpatterns = [
    path(
        "",
        view=OfferListCreateAPIView.as_view(),
        name="offer_create_list",
    ),
    path(
        "<str:offer_id>",
        view=OfferRetrieveUpdateAPIView.as_view(),
        name="retrieve_update",
    ),
    path(
        "payment/<str:payment_id>",
        view=OfferPaymentRetrieveAPIView.as_view(),
        name="offer_payment_detail",
    ),
    path(
        "<str:offer_id>/payments",
        view=OfferPaymentListCreateAPIView.as_view(),
        name="list_create_offer_payments",
    ),
    path(
        "event/process",
        view=OfferEventProcessView.as_view(),
        name="process",
    ),
]
