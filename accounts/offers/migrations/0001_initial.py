# Generated by Django 3.2.18 on 2025-03-19 11:54

import accounts.utils.common
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Offers',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, max_length=36, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=50)),
                ('code', models.CharField(editable=False, max_length=10, unique=True)),
                ('value', models.DecimalField(decimal_places=3, max_digits=10)),
                ('term', models.IntegerField(choices=[(1, 'flat'), (2, 'percent')], help_text='1:flat; 2:percent;')),
                ('type', models.IntegerField(choices=[(1, 'credit'), (2, 'package')], help_text='1:credit; 2:package;')),
                ('min_amount', models.DecimalField(decimal_places=3, max_digits=10)),
                ('max_amount', models.DecimalField(decimal_places=3, max_digits=10)),
                ('expiry', models.DateTimeField()),
                ('status', models.SmallIntegerField(choices=[(0, 'inactive'), (1, 'active')], default=1, help_text='0:inactive; 1:active')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'offers',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='PaymentOffers',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, max_length=36, primary_key=True, serialize=False)),
                ('offer_amount', models.DecimalField(decimal_places=3, max_digits=10, null=True)),
                ('status', models.SmallIntegerField(choices=[(0, 'pending'), (1, 'applied'), (2, 'rejected')], default=0)),
                ('comment', models.CharField(max_length=100)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('offer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='offers.offers')),
            ],
            options={
                'db_table': 'payment_offers',
                'managed': True,
            },
        ),
    ]
