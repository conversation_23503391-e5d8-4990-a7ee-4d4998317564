# Generated by Django 3.2.18 on 2025-03-19 11:54

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('packages', '0001_initial'),
        ('products', '0001_initial'),
        ('offers', '0001_initial'),
        ('payments', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='paymentoffers',
            name='payment',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='payments.recharges', to_field='payment_id'),
        ),
        migrations.AddField(
            model_name='offers',
            name='custom_package',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='packages.packagecustoms'),
        ),
        migrations.AddField(
            model_name='offers',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.products'),
        ),
        migrations.AddIndex(
            model_name='paymentoffers',
            index=models.Index(fields=['payment_id'], name='payment_off_payment_f98b52_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentoffers',
            index=models.Index(fields=['offer_id'], name='payment_off_offer_i_e5ef71_idx'),
        ),
        migrations.AddIndex(
            model_name='offers',
            index=models.Index(fields=['code'], name='offers_code_b4bd9c_idx'),
        ),
    ]
