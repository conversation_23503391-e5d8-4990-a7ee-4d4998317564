from accounts.offers.models import Offers
from django_filters import rest_framework as django_filters
from django_filters import rest_framework as filters
from django.utils import timezone


class OfferFilter(django_filters.FilterSet):
    status = filters.CharFilter(method="filter_status")
    is_expired = filters.BooleanFilter(method="filter_is_expired")
    package_rent = filters.NumberFilter(
        field_name="custom_package__package_rent"
    )
    payment_cycle = filters.NumberFilter(
        field_name="custom_package__payment_cycle"
    )
    global_package_id = filters.CharFilter(
        field_name="custom_package__parent_package__id"
    )

    def filter_status(self, queryset, name, value):
        if value.lower() == "active":
            return queryset.filter(status=1)
        elif value.lower() == "inactive":
            return queryset.filter(status=0)
        return queryset

    def filter_is_expired(self, queryset, name, value):
        if value == 0:
            now = timezone.now()
            return queryset.filter(expiry__gte=now)
        elif value == 1:
            now = timezone.now()
            return queryset.filter(expiry__lte=now)
        return queryset

    class Meta:
        model = Offers
        fields = {
            "code": ["exact"],
            "name": ["icontains"],
            "custom_package__payment_cycle": ["exact"],
        }
