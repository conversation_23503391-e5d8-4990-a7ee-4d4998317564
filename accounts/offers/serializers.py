from decimal import Decimal
from django.utils import timezone
from rest_framework import serializers
from accounts.core.utils.tax_calculation import calc_reverse_tax
from accounts.offers.constants import (
    OFFER_STATUS_ACTIVE,
    OFFER_STATUS_INACTIVE,
    OFFER_PAYMENT_STATUS_PENDING,
    OFFER_PAYMENT_STATUS_APPLIED,
    OFFER_TERM_FLAT,
    OFFER_TERM_PERCENT,
    OFFER_TYPE_CREDIT_ADDITION,
    OFFER_TYPE_PACKAGE,
)
from accounts.packages.constants import PACKAGE_STATUS_ACTIVE
from accounts.packages.models import PackageCustoms
from accounts.packages.serializers import (
    CustomPackageSerializer,
    CustomPackageDiscountSerializer,
)
from accounts.offers.models import Offers
from accounts.packages.models import Packages


class OfferCustomPackageSerializer(CustomPackageSerializer):
    discount = serializers.SerializerMethodField()
    package_name = serializers.SerializerMethodField()

    class Meta(CustomPackageSerializer.Meta):
        fields = (
            "id",
            "parent_package_id",
            "package_name",
            "custom_code",
            "package_rent",
            "payment_cycle",
        )

    def get_discount(self, obj):
        if obj.discount:
            # Customize the discount serialization if needed
            return CustomPackageDiscountSerializer(obj.discount).data
        return None

    def get_package_name(self, obj):
        if obj.parent_package:
            return obj.parent_package.name
        return None


class OfferCreateSerializer(serializers.Serializer):
    custom_package_id = serializers.CharField(max_length=100, default=None)
    product_id = serializers.CharField(max_length=100, default=None)
    name = serializers.CharField(max_length=255)
    value = serializers.DecimalField(max_digits=8, decimal_places=3, default=0)
    term = serializers.ChoiceField(
        choices=[OFFER_TERM_PERCENT, OFFER_TERM_FLAT], default=OFFER_TERM_FLAT
    )
    type = serializers.ChoiceField(
        choices=[OFFER_TYPE_CREDIT_ADDITION, OFFER_TYPE_PACKAGE],
        default=OFFER_TYPE_CREDIT_ADDITION,
    )
    min_amount = serializers.DecimalField(
        max_digits=8, decimal_places=3, default=0.00
    )
    max_amount = serializers.DecimalField(
        max_digits=8, decimal_places=3, default=1.00
    )
    expiry = serializers.DateTimeField()
    status = serializers.ChoiceField(
        required=False, default="active", choices=["active", "inactive"]
    )

    def validate(self, data):
        type = data["type"]
        if type == OFFER_TYPE_CREDIT_ADDITION:
            min_amount = data["min_amount"]
            max_amount = data["max_amount"]
            value = data["value"]

            if min_amount >= max_amount:
                raise serializers.ValidationError(
                    "Minimum amount should be less than maximum amount"
                )

            if data["term"] == "percent" and value > 99:
                raise serializers.ValidationError(
                    "Percent value should not be greater than 99"
                )
        elif type == OFFER_TYPE_PACKAGE:
            if data["custom_package_id"] is None:
                raise serializers.ValidationError(
                    "custom_package_id is required"
                )

            if not PackageCustoms.objects.filter(
                id=data["custom_package_id"], accept_status=1
            ).exists():
                raise serializers.ValidationError("Invalid custom_package_id")
            data["value"] = 0

        expiry = data["expiry"]
        if expiry <= timezone.now():
            raise serializers.ValidationError(
                "Expiry date must be in the future."
            )
        return data

    def validate_status(self, value):
        # manipulate value for status field as 0=> Inactive,1=>active to insert in DB
        if value.lower() == "active":
            return 1
        else:
            return 0

    def to_internal_value(self, data):
        # manipulate value for term field as 1=> flat,2=>percent to insert in DB
        if "term" in data:
            if data["term"].lower() == "percent":
                data["term"] = OFFER_TERM_PERCENT
            else:
                data["term"] = OFFER_TERM_FLAT
        if "type" in data:
            if data["type"].lower() == "credit":
                data["type"] = OFFER_TYPE_CREDIT_ADDITION
            else:
                data["type"] = OFFER_TYPE_PACKAGE
        return super().to_internal_value(data)


class OfferStatusSerializer(serializers.Serializer):
    status = serializers.ChoiceField(choices=["active", "inactive"])

    def validate_status(self, value):
        # manipulate value for term field as 0=> Inactive,1=>active to insert in DB
        if value.lower() == "active":
            return OFFER_STATUS_ACTIVE
        else:
            return OFFER_STATUS_INACTIVE


class PaymentOfferCreateSerializer(serializers.Serializer):
    payment_id = serializers.CharField()


class OfferPaymentListSerializer(serializers.Serializer):
    sort = serializers.ChoiceField(
        required=False, choices=["created"], default="created"
    )
    order = serializers.ChoiceField(
        required=False, choices=["asc", "desc"], default="desc"
    )


class OfferSerializer(serializers.Serializer):
    id = serializers.CharField()
    product_id = serializers.CharField()
    custom_package_id = serializers.CharField(max_length=100)
    name = serializers.CharField()
    code = serializers.CharField()
    value = serializers.DecimalField(max_digits=8, decimal_places=3)
    term = serializers.SerializerMethodField()
    type = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    min_amount = serializers.DecimalField(max_digits=8, decimal_places=3)
    max_amount = serializers.DecimalField(max_digits=8, decimal_places=3)
    custom_package = OfferCustomPackageSerializer()
    expiry = serializers.DateTimeField()
    created = serializers.DateTimeField()

    def get_status(self, obj):
        if obj.status == OFFER_STATUS_ACTIVE:
            return "active"
        return "inactive"

    def get_term(self, obj):
        if obj.term == 1:
            return "flat"
        return "percent"

    def get_type(self, obj):
        if obj.type == 1:
            return "credit"
        return "package"

    # def get_custom_package(self, obj):
    #     fields_param = self.context["request"].query_params.get("fields")
    #     if fields_param and "package" in fields_param and obj.custom_package:
    #         if "discount" not in fields_param:
    #             obj.custom_package.discount_data = {}
    #         return OfferCustomPackageSerializer(obj.custom_package).data
    #     return {}


class PaymentOfferSerializer(serializers.Serializer):
    id = serializers.CharField()
    offer_id = serializers.SerializerMethodField()
    applicable_amount = serializers.SerializerMethodField()
    offer_amount = serializers.DecimalField(max_digits=8, decimal_places=3)
    status = serializers.SerializerMethodField()
    comment = serializers.CharField()
    created = serializers.DateTimeField()

    def get_status(self, obj):
        if obj.status == OFFER_PAYMENT_STATUS_PENDING:
            return "pending"
        elif obj.status == OFFER_PAYMENT_STATUS_APPLIED:
            return "applied"
        else:
            return "rejected"

    def get_applicable_amount(self, obj):
        amount_without_tax = calc_reverse_tax(
            obj.payment.country_id, obj.payment.amount
        )
        # round off to 3 decimal places
        return "{:.3f}".format(
            Decimal(obj.offer.calc_offer_amount(amount_without_tax))
        )

    def get_offer_id(self, obj):
        return obj.offer_id
