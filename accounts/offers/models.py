from datetime import datetime, timezone

from django.db import models

from accounts.offers import constants
from accounts.payments.models import Recharges
from accounts.products.models import Products
from accounts.utils.common import uuid
from accounts.packages.models import PackageCustoms


class Offers(models.Model):
    id = models.CharField(max_length=36, primary_key=True, default=uuid)
    custom_package = models.ForeignKey(
        PackageCustoms, on_delete=models.CASCADE, null=True
    )
    name = models.CharField(max_length=50)
    code = models.CharField(max_length=10, unique=True, editable=False)
    value = models.DecimalField(max_digits=10, decimal_places=3)
    product = models.ForeignKey(
        Products,
        on_delete=models.CASCADE,
    )
    term = models.IntegerField(
        choices=[
            (constants.OFFER_TERM_FLAT, "flat"),
            (constants.OFFER_TERM_PERCENT, "percent"),
        ],
        help_text="1:flat; 2:percent;",
    )
    type = models.IntegerField(
        choices=[
            (constants.OFFER_TYPE_CREDIT_ADDITION, "credit"),
            (constants.OFFER_TYPE_PACKAGE, "package"),
        ],
        help_text="1:credit; 2:package;",
    )
    min_amount = models.DecimalField(max_digits=10, decimal_places=3)
    max_amount = models.DecimalField(max_digits=10, decimal_places=3)
    expiry = models.DateTimeField()
    status = models.SmallIntegerField(
        default=constants.OFFER_STATUS_ACTIVE,
        choices=[
            (constants.OFFER_STATUS_INACTIVE, "inactive"),
            (constants.OFFER_STATUS_ACTIVE, "active"),
        ],
        help_text="0:inactive; 1:active",
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    # calculate offer amount
    def calc_offer_amount(self, payment_amount):
        if self.term == constants.OFFER_TERM_PERCENT:
            amount = (payment_amount / 100) * (self.value)
            return float(amount)
        elif self.term == constants.OFFER_TERM_FLAT:
            return float(self.value)
        raise RuntimeError("Invalid offer value")

    # is offer expired
    def is_offer_expired(self):
        return self.expiry < datetime.now(timezone.utc)

    #  check payment amount range falls in min and max range
    def is_amount_outside_range(self, payment_amount):
        if payment_amount < self.min_amount or payment_amount > self.max_amount:
            return True
        return False

    class Meta:
        db_table = "offers"
        managed = True
        indexes = [
            models.Index(fields=["code"]),
        ]


class PaymentOffers(models.Model):
    id = models.CharField(max_length=36, primary_key=True, default=uuid)
    offer = models.ForeignKey(
        Offers,
        on_delete=models.CASCADE,
    )
    payment = models.OneToOneField(
        Recharges,
        to_field="payment_id",
        on_delete=models.CASCADE,
        unique=True,
    )
    offer_amount = models.DecimalField(
        max_digits=10, decimal_places=3, null=True
    )
    status = models.SmallIntegerField(
        default=constants.OFFER_PAYMENT_STATUS_PENDING,
        choices=[
            (constants.OFFER_PAYMENT_STATUS_PENDING, "pending"),
            (constants.OFFER_PAYMENT_STATUS_APPLIED, "applied"),
            (constants.OFFER_PAYMENT_STATUS_REJECTED, "rejected"),
        ],
    )
    comment = models.CharField(max_length=100)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "payment_offers"
        managed = True
        indexes = [
            models.Index(fields=["payment_id"]),
            models.Index(fields=["offer_id"]),
        ]
