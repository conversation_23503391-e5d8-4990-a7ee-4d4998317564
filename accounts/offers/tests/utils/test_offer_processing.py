import json
from unittest import mock

from django.conf import settings
from django.test import TestCase
from django.utils import timezone

import pytest
import responses

from accounts.billing_accounts.tests.factories import BillingAccountFactory
from accounts.services.tests.factories import (
    ServiceFactory,
)
from accounts.packages.tests.factories import (
    PackageFactory,
    PackageCustomFactory,
    PackageFeatureFactory,
    PackageFeatureRatesFactory,
)
from accounts.global_packages.tests.factories import (
    GlobalPackageFactory,
    GlobalPackageFeatureFactory,
    GlobalPackageFeatureRatesFactory,
)
from accounts.core.tests.factories import CountryFactory
from accounts.exceptions import ExternalAPIException
from accounts.offers.exceptions import (
    InvalidOfferPaymentException,
    OfferProcessingFailedException,
    CreditadditionFailedException,
)
from accounts.packages.exceptions import (
    CustomPackageException,
    PackageException,
    PackageChangeException,
)
from accounts.services.exceptions import InvalidServiceException
from accounts.offers.constants import (
    OFFER_PAYMENT_STATUS_REJECTED,
    OFFER_PAYMENT_STATUS_APPLIED,
    OFFER_STATUS_INACTIVE,
    OFFER_STATUS_ACTIVE,
    OFFER_TYPE_PACKAGE,
)
from accounts.offers.tests.factories import OfferFactory, PaymentOffersFactory
from accounts.offers.utils.offer_processing import process_pending_offer_payment
from accounts.payments.tests.factories import (
    PaymentTracksFactory,
    RechargesFactory,
    TrackingSettlementHistoriesFactory,
)
from accounts.products.tests.factories import OrgTypesFactory, ProductFactory
from accounts.offers.models import PaymentOffers
from accounts.discounts.tests.factories import DiscountFactory
from django.forms.models import model_to_dict


class TestOfferProcessing(TestCase):
    def setUp(self):
        self.country = CountryFactory.create()

        self.product = ProductFactory.create(
            country=self.country,
        )
        self.billing_account = BillingAccountFactory.create(
            discount=None,
        )
        self.offer = OfferFactory.create(
            product=self.product,
            min_amount=300,
            max_amount=500,
        )

        self.recharge = RechargesFactory.create(
            billing_account=self.billing_account,
            payment_id="abc_123_333",
            amount=400,
            country=self.country,
            status="Y",
        )

        self.recharge_two = RechargesFactory.create(
            billing_account=self.billing_account,
            payment_id="abc_12345",
            amount=400,
            country=self.country,
            status="Y",
        )

        self.payment_track = PaymentTracksFactory.create(
            payment_id=self.recharge.payment_id, amount=self.recharge.amount
        )

        TrackingSettlementHistoriesFactory.create(
            billing_account=self.recharge.billing_account,
            setl_key=self.payment_track.txn_setl_key,
            payment=self.payment_track,
            amount=self.payment_track.amount,
        )

        self.global_package = GlobalPackageFactory.create()

        self.global_package_feature = GlobalPackageFeatureFactory.create(
            package=self.global_package
        )
        GlobalPackageFeatureRatesFactory.create(
            package_feature=self.global_package_feature
        )
        self.discount = DiscountFactory.create()

        discount_dict = model_to_dict(self.discount)
        discount_dict["valid_till"] = discount_dict["valid_till"].strftime(
            "%Y-%m-%d %H:%M:%S"
        )
        discount_str = json.dumps(discount_dict)

        self.package_custom = PackageCustomFactory.create(
            parent_package=self.global_package,
            features=f'{{"{self.global_package_feature.product_feature_id}":"{self.global_package_feature.product_feature_id}"}}',
            free=f'{{"{self.global_package_feature.product_feature_id}":"0"}}',
            property_id="{}",
            discount_data=discount_str,
        )

        self.package = PackageFactory.create(
            product=self.global_package.product,
            name=self.global_package.name,
            package_type=self.global_package.package_type,
            rent_per_month=float(self.package_custom.package_rent)
            + float(self.package_custom.additional_rent),
            renew_cycle=self.package_custom.payment_cycle,
            package_custom=self.package_custom,
            is_public=0,
            ocs_flag=0,
            package_for=self.global_package.package_for,
            package_category=self.global_package.package_category,
            discount=self.discount,
            description=None,
        )

        self.package_feature = PackageFeatureFactory.create(
            package=self.package
        )

        self.package_feature_rate = PackageFeatureRatesFactory.create(
            package_feature=self.package_feature
        )

        self.offer_two = OfferFactory.create(
            product=self.product,
            min_amount=300,
            max_amount=500,
            type=OFFER_TYPE_PACKAGE,
            custom_package=self.package_custom,
        )

        PackageFactory.create(
            package_custom=self.offer_two.custom_package,
            code=self.offer_two.custom_package.custom_code,
        )

    def test_with_invalid_payment_id(self):
        # test with invalid payment_id (payment id not found in db)
        payment_id = "abc123"
        with pytest.raises(InvalidOfferPaymentException):
            process_pending_offer_payment(payment_id)

    def test_with_offer_with_payment_not_settled_or_inactive(self):
        # test with offer inactive i.e. offer status = 0
        offer = OfferFactory.create(
            value="200.000",
            product=self.product,
            min_amount="300.000",
            max_amount="500.000",
        )
        PaymentOffersFactory.create(
            offer=offer,
            payment=self.recharge_two,
        )
        with pytest.raises(OfferProcessingFailedException):
            process_pending_offer_payment(self.recharge_two.payment_id)

    def test_with_inactive_offer(self):
        # test with offer inactive i.e. offer status = 0
        offer = OfferFactory.create(
            value="200.000",
            product=self.product,
            min_amount="300.000",
            max_amount="500.000",
            expiry=timezone.now() - timezone.timedelta(days=10),
            status=OFFER_STATUS_INACTIVE,
        )
        PaymentOffersFactory.create(
            offer=offer,
            payment=self.recharge,
        )
        with pytest.raises(OfferProcessingFailedException):
            process_pending_offer_payment(self.recharge.payment_id)

    @responses.activate
    def test_offer_amount_within_range(self):
        # test with valid payment_id and payment amount within min and max range
        PaymentOffersFactory.create(
            offer=self.offer,
            payment=self.recharge,
        )
        expected_dict = json.dumps(
            {
                "status": "success",
                "message": "credit added successfully",
                "code": 200,
            }
        )
        responses.add(
            responses.POST,
            settings.ACCOUNT_API_V1_HOST + "discount/credit_add",
            body=expected_dict,
            status=200,
        )
        result = process_pending_offer_payment(self.recharge.payment_id)

        offer_payment = PaymentOffers.objects.get(
            payment_id=self.recharge.payment_id
        )
        assert offer_payment.offer_amount == self.offer.value
        assert offer_payment.status == OFFER_PAYMENT_STATUS_APPLIED
        assert result is None

    def test_offer_amount_out_of_range(self):
        # test with payment amount out of min and max range and term as flat
        recharge = RechargesFactory.create(
            billing_account=self.billing_account,
            payment_id="123456",
            amount=200,
            country=self.country,
        )

        payment_track = PaymentTracksFactory.create(
            payment_id=recharge.payment_id, amount=recharge.amount
        )
        TrackingSettlementHistoriesFactory.create(
            billing_account=recharge.billing_account,
            setl_key=payment_track.txn_setl_key,
            payment=payment_track,
            amount=200,
        )
        PaymentOffersFactory.create(
            offer=self.offer,
            payment=recharge,
        )
        with pytest.raises(OfferProcessingFailedException):
            process_pending_offer_payment(recharge.payment_id)

    @responses.activate
    def test_with_credit_api_failure(self):
        # test with credit_api_failure
        expected_dict = json.dumps(
            {
                "status": "error",
                "message": "invalid token",
                "code": 400,
            }
        )
        responses.add(
            responses.POST,
            settings.ACCOUNT_API_V1_HOST + "discount/credit_add",
            body=expected_dict,
            status=400,
        )
        PaymentOffersFactory.create(
            offer=self.offer,
            payment=self.recharge,
        )
        with pytest.raises(CreditadditionFailedException):
            process_pending_offer_payment(self.recharge.payment_id)

    def test_with_invalid_term(self):
        # test with term is set to 4 (i.e. other then flat, percent and package)
        offer = OfferFactory.create(
            value="200.000",
            product=self.product,
            term=4,
            min_amount="300.000",
            max_amount="500.000",
        )
        self.payment_offer = PaymentOffersFactory.create(
            offer=offer,
            payment=self.recharge,
        )
        with pytest.raises(RuntimeError):
            process_pending_offer_payment(self.payment_offer.payment_id)

    def test_with_expired_offer(self):
        offer = OfferFactory.create(
            value="200.000",
            product=self.product,
            min_amount="300.000",
            max_amount="500.000",
            expiry=timezone.now() - timezone.timedelta(days=10),
            status=OFFER_STATUS_ACTIVE,
        )
        PaymentOffersFactory.create(
            offer=offer,
            payment=self.recharge,
        )
        with pytest.raises(OfferProcessingFailedException):
            process_pending_offer_payment(payment_id=self.recharge.payment_id)

    @responses.activate
    def test_with_term_percent(self):
        offer = OfferFactory.create(
            product=self.product,
            min_amount="300.000",
            max_amount="500.000",
            term=2,
            value=20,  # 20% of the payment amount
        )
        recharge = RechargesFactory.create(
            billing_account=self.billing_account,
            payment_id="123456",
            amount=400,
            country=self.country,
        )
        payment_track = PaymentTracksFactory.create(
            payment_id=recharge.payment_id, amount=recharge.amount
        )
        TrackingSettlementHistoriesFactory.create(
            billing_account=recharge.billing_account,
            setl_key=payment_track.txn_setl_key,
            payment=payment_track,
            amount=recharge.amount,
        )
        PaymentOffersFactory.create(
            offer=offer,
            payment=recharge,
        )

        expected_dict = json.dumps(
            {
                "status": "success",
                "message": "credit added successfully",
                "code": 200,
            }
        )
        responses.add(
            responses.POST,
            settings.ACCOUNT_API_V1_HOST + "discount/credit_add",
            body=expected_dict,
            status=200,
        )

        result = process_pending_offer_payment(recharge.payment_id)

        offer_payment = PaymentOffers.objects.get(
            payment_id=recharge.payment_id
        )
        assert offer_payment.offer_amount == 80  # 20 percent of 400
        assert offer_payment.status == OFFER_PAYMENT_STATUS_APPLIED
        assert result is None

    def test_offer_package_type_service_not_found(self):
        offer = OfferFactory.create(
            product=self.product,
            min_amount=300,
            max_amount=500,
            type=OFFER_TYPE_PACKAGE,
        )
        PaymentOffersFactory.create(
            offer=offer,
            payment=self.recharge,
        )
        with pytest.raises(InvalidServiceException):
            process_pending_offer_payment(self.recharge.payment_id)

    def test_offer_custom_package_not_found(self):
        ServiceFactory.create(
            billing_account=self.billing_account,
            status=1,
            live_status=1,
        )
        offer = OfferFactory.create(
            product=self.product,
            min_amount=300,
            max_amount=500,
            type=OFFER_TYPE_PACKAGE,
            custom_package=None,
        )
        PaymentOffersFactory.create(
            offer=offer,
            payment=self.recharge,
        )
        with pytest.raises(CustomPackageException):
            process_pending_offer_payment(self.recharge.payment_id)

    def test_offer_package_not_found(self):
        ServiceFactory.create(
            billing_account=self.billing_account,
            status=1,
            live_status=1,
        )
        offer = OfferFactory.create(
            product=self.product,
            min_amount=300,
            max_amount=500,
            type=OFFER_TYPE_PACKAGE,
        )
        PaymentOffersFactory.create(
            offer=offer,
            payment=self.recharge,
        )
        with pytest.raises(PackageException):
            process_pending_offer_payment(self.recharge.payment_id)

    @responses.activate
    def test_offer_package_with_change_package_pending_amount_api_failure(self):
        expected_dict = json.dumps(
            {
                "status": "error",
                "message": "invalid token",
                "code": 400,
            }
        )
        responses.add(
            responses.POST,
            settings.ACCOUNT_API_V1_HOST
            + "external/change_package_pending_amount",
            body=expected_dict,
            status=400,
        )
        ServiceFactory.create(
            billing_account=self.billing_account,
            status=1,
            live_status=1,
        )
        offer = OfferFactory.create(
            product=self.product,
            min_amount=300,
            max_amount=500,
            type=OFFER_TYPE_PACKAGE,
        )
        PaymentOffersFactory.create(
            offer=offer,
            payment=self.recharge,
        )
        PackageFactory.create(
            package_custom=offer.custom_package,
            code=offer.custom_package.custom_code,
        )
        with pytest.raises(PackageChangeException):
            process_pending_offer_payment(self.recharge.payment_id)

    @responses.activate
    def test_offer_package_with_pending_amount_failure(self):
        expected_dict = json.dumps(
            {
                "status": "success",
                "message": "credit added successfully",
                "code": 200,
                "data": {"amount_without_tax": "100"},
            }
        )
        responses.add(
            responses.POST,
            settings.ACCOUNT_API_V1_HOST
            + "external/change_package_pending_amount",
            body=expected_dict,
            status=200,
        )
        ServiceFactory.create(
            billing_account=self.billing_account,
            status=1,
            live_status=1,
        )
        offer = OfferFactory.create(
            product=self.product,
            min_amount=300,
            max_amount=500,
            type=OFFER_TYPE_PACKAGE,
        )
        PaymentOffersFactory.create(
            offer=offer,
            payment=self.recharge,
        )
        PackageFactory.create(
            package_custom=offer.custom_package,
            code=offer.custom_package.custom_code,
        )
        with pytest.raises(OfferProcessingFailedException):
            process_pending_offer_payment(self.recharge.payment_id)

    @responses.activate
    @mock.patch("accounts.discounts.models.Discounts.objects.create")
    @mock.patch("accounts.packages.models.Packages.objects.create")
    @mock.patch("accounts.packages.models.PackageFeatures.objects.create")
    @mock.patch("accounts.packages.models.PackageFeatureRates.objects.create")
    def test_offer_package_with_create_package_from_custom_package_success(
        self,
        mock_create_package_feature_rate,
        mock_create_package_feature,
        mock_create_package,
        mock_create_discount,
    ):
        expected_dict = json.dumps(
            {
                "status": "success",
                "message": "credit added successfully",
                "code": 200,
                "data": {"amount_without_tax": "0"},
            }
        )
        responses.add(
            responses.POST,
            settings.ACCOUNT_API_V1_HOST
            + "external/change_package_pending_amount",
            body=expected_dict,
            status=200,
        )
        ServiceFactory.create(
            billing_account=self.billing_account,
            status=1,
            live_status=1,
        )
        PaymentOffersFactory.create(
            offer=self.offer_two,
            payment=self.recharge,
        )
        mock_create_discount.return_value = self.discount
        mock_create_package.return_value = self.package
        mock_create_package_feature.return_value = self.package_feature
        mock_create_package_feature_rate.return_value = (
            self.package_feature_rate
        )

        expected_dict = json.dumps(
            {
                "status": "success",
                "message": "package upgrade successfully",
                "code": "200",
            }
        )
        responses.add(
            responses.POST,
            settings.ACCOUNT_API_V1_HOST + "external/change_package",
            body=expected_dict,
            status=200,
        )

        result = process_pending_offer_payment(self.recharge.payment_id)

        offer_payment = PaymentOffers.objects.get(
            payment_id=self.recharge.payment_id
        )
        assert offer_payment.offer_amount == self.discount.value
        assert offer_payment.status == OFFER_PAYMENT_STATUS_APPLIED
        assert result is None

    @responses.activate
    @mock.patch("accounts.discounts.models.Discounts.objects.create")
    @mock.patch("accounts.packages.models.Packages.objects.create")
    @mock.patch("accounts.packages.models.PackageFeatures.objects.create")
    @mock.patch("accounts.packages.models.PackageFeatureRates.objects.create")
    def test_offer_package_with_create_package_from_custom_package_change_package_api_failure(
        self,
        mock_create_package_feature_rate,
        mock_create_package_feature,
        mock_create_package,
        mock_create_discount,
    ):
        expected_dict = json.dumps(
            {
                "status": "success",
                "message": "credit added successfully",
                "code": 200,
                "data": {"amount_without_tax": "0"},
            }
        )
        responses.add(
            responses.POST,
            settings.ACCOUNT_API_V1_HOST
            + "external/change_package_pending_amount",
            body=expected_dict,
            status=200,
        )
        ServiceFactory.create(
            billing_account=self.billing_account,
            status=1,
            live_status=1,
        )
        PaymentOffersFactory.create(
            offer=self.offer_two,
            payment=self.recharge,
        )
        mock_create_discount.return_value = self.discount
        mock_create_package.return_value = self.package
        mock_create_package_feature.return_value = self.package_feature
        mock_create_package_feature_rate.return_value = (
            self.package_feature_rate
        )

        expected_dict = json.dumps(
            {
                "status": "error",
                "message": "Invalid token",
                "code": "400",
            }
        )
        responses.add(
            responses.POST,
            settings.ACCOUNT_API_V1_HOST + "external/change_package",
            body=expected_dict,
            status=400,
        )

        with pytest.raises(PackageChangeException):
            process_pending_offer_payment(self.recharge.payment_id)

    def test_with_invalid_type(self):
        offer = OfferFactory.create(
            value="200.000",
            product=self.product,
            type=4,
            min_amount="300.000",
            max_amount="500.000",
        )
        self.payment_offer = PaymentOffersFactory.create(
            offer=offer,
            payment=self.recharge,
        )
        with pytest.raises(ValueError):
            process_pending_offer_payment(self.payment_offer.payment_id)
