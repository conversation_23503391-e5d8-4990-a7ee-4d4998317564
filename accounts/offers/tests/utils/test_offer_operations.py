from unittest import mock

from django.test import TestCase
from django.utils import timezone

import pytest

from accounts.billing_accounts.tests.factories import BillingAccountFactory
from accounts.core.tests.factories import CountryFactory
from accounts.offers.exceptions import OfferCodeGenerationError
from accounts.offers import constants
from accounts.offers.models import Offers, PaymentOffers
from accounts.offers.serializers import PaymentOfferSerializer
from accounts.offers.utils.offer_operations import (
    check_code_exists,
    create_offer,
    edit_offer,
    enable_disable_offer,
    fetch_offer,
    fetch_payment_offer,
    fetch_payment_offer_list,
    generate_offer_code,
    get_pending_offers,
    link_payment_offer,
    update_payment_offer_status,
)
from accounts.offers.exceptions import (
    InvalidOfferException,
    OfferAlreadyAppliedException,
    OfferExpiredException,
)
from accounts.payments.exceptions import InvalidPaymentException
from accounts.offers.tests.factories import OfferFactory, PaymentOffersFactory
from accounts.payments.tests.factories import RechargesFactory
from accounts.products.tests.factories import OrgTypesFactory, ProductFactory
from accounts.packages.tests.factories import PackageCustomFactory


class TestOfferOperations(TestCase):
    def setUp(self):
        self.country = CountryFactory.create()
        self.product = ProductFactory.create(
            country=self.country,
        )
        self.billing_account = BillingAccountFactory.create(
            discount=None,
        )
        custom_package = PackageCustomFactory.create()
        self.offer_data = {
            "name": "Test Offer",
            "value": 150.00,
            "term": 1,
            "type": 1,
            "custom_package_id": custom_package.id,
            "min_amount": 200.000,
            "max_amount": 400.000,
            "expiry": timezone.now() + timezone.timedelta(days=365),
            "status": 1,
            "product_id": self.product.id,
        }
        self.recharge = RechargesFactory.create(
            billing_account=self.billing_account,
            country=self.country,
            status="0",
        )

    def test_create_offer_missing_data(self):
        data = {
            "product_id": 1,
        }
        with pytest.raises(KeyError):
            create_offer(data)

    def test_create_offer_success(self):
        result = create_offer(self.offer_data)
        assert result.name == self.offer_data["name"]
        assert result.value == self.offer_data["value"]
        assert result.term == self.offer_data["term"]
        assert result.min_amount == self.offer_data["min_amount"]
        assert result.max_amount == self.offer_data["max_amount"]
        assert result.expiry == self.offer_data["expiry"]
        assert result.status == self.offer_data["status"]
        assert result.product_id == self.offer_data["product_id"]

    @mock.patch("accounts.offers.utils.offer_operations.check_code_exists")
    def test_code_generation_create_offer_failure(self, mock_code):
        mock_code.return_value = True
        with pytest.raises(OfferCodeGenerationError):
            create_offer(self.offer_data)

    @mock.patch("accounts.offers.utils.offer_operations.check_code_exists")
    def test_code_generation_success(self, mock_code):
        mock_code.return_value = False
        result = generate_offer_code()
        assert result is not None

    @mock.patch("accounts.offers.utils.offer_operations.check_code_exists")
    def test_code_generation_failure(self, mock_code):
        mock_code.return_value = True
        with pytest.raises(OfferCodeGenerationError):
            generate_offer_code()

    def test_code_exist_success(self):
        offer = create_offer(self.offer_data)
        code = offer.code
        result = check_code_exists(code)
        assert result is True

    def test_code_exist_failure(self):
        result = check_code_exists("123456")
        assert result is not True

    def test_fetch_offer_success(self):
        offer = create_offer(self.offer_data)
        id = offer.id
        result = fetch_offer(id)
        assert result.name == self.offer_data["name"]
        assert result.value == self.offer_data["value"]
        assert result.term == self.offer_data["term"]
        assert result.min_amount == self.offer_data["min_amount"]
        assert result.max_amount == self.offer_data["max_amount"]
        assert result.expiry == self.offer_data["expiry"]
        assert result.status == self.offer_data["status"]
        assert result.product_id == self.offer_data["product_id"]

    def test_fetch_offer_failure(self):
        id = "12345"
        result = fetch_offer(id)
        assert result is None

    def test_edit_offer_success(self):
        offer = create_offer(self.offer_data)
        id = offer.id
        data = fetch_offer(id)
        data.name = "test offer edit"
        custom_package = PackageCustomFactory.create()
        edit_offer(
            id,
            {
                "name": "test offer edit",
                "product_id": data.product_id,
                "value": data.value,
                "term": data.term,
                "status": 0,
                "min_amount": data.min_amount,
                "max_amount": data.max_amount,
                "expiry": data.expiry,
                "custom_package_id": custom_package.id,
            },
        )
        result = fetch_offer(id)
        assert result.name == "test offer edit"
        assert result.status == 0

    def test_edit_offer_failure(self):
        with pytest.raises(Offers.DoesNotExist):
            edit_offer("12345", {})

    def test_enable_disable_offer_success(self):
        offer = create_offer(self.offer_data)
        id = offer.id
        result = enable_disable_offer(id, status=0)
        assert result is True

    def test_enable_disable_offer_failure(self):
        with pytest.raises(Offers.DoesNotExist):
            enable_disable_offer("12345", status=0)

    def test_link_payment_offer_success(self):
        offer = create_offer(self.offer_data)
        id = offer.id
        result = vars(link_payment_offer(id, self.recharge.payment_id))
        assert result["offer_id"] == offer.id
        assert result["payment_id"] == self.recharge.payment_id

    def test_link_payment_invalid_offer_exception(self):
        offer = OfferFactory.create(status=0)
        recharge = RechargesFactory(status="N")
        with pytest.raises(InvalidOfferException):
            link_payment_offer(offer.id, recharge.payment_id)

        with pytest.raises(InvalidOfferException):
            link_payment_offer("dummyOfferId", recharge.payment_id)

    def test_link_payment_invalid_payment_exception(self):
        offer = OfferFactory.create()
        with pytest.raises(InvalidPaymentException):
            link_payment_offer(offer.id, "dummyPaymentId")

        # raises InvalidPaymentException if payment is already completed
        recharge = RechargesFactory(status="Y")
        with pytest.raises(InvalidPaymentException):
            link_payment_offer(offer.id, recharge.payment_id)

    def test_link_payment_already_applied_exception(self):
        recharge = RechargesFactory(payment_id="abc_123", status="N")
        PaymentOffersFactory.create(payment=recharge, status=1)

        new_offer = OfferFactory.create()
        with pytest.raises(OfferAlreadyAppliedException):
            link_payment_offer(new_offer.id, "abc_123")

    def test_link_payment_offer_exipred_exception(self):
        offer = OfferFactory.create(
            expiry=timezone.now() - timezone.timedelta(10)
        )
        with pytest.raises(OfferExpiredException):
            link_payment_offer(offer.id, self.recharge.payment_id)

    def test_link_payment_offer_override_old_offer_success(self):
        recharge = RechargesFactory(status="N")
        PaymentOffersFactory.create(payment_id=recharge.payment_id)

        new_offer = OfferFactory.create()
        obj = link_payment_offer(new_offer.id, recharge.payment_id)
        assert obj.offer_id == new_offer.id

    def test_fetch_payment_offer_success(self):
        offer = OfferFactory.create()
        recharge = RechargesFactory(status="0")
        link_payment_offer(offer.id, recharge.payment_id)

        result = fetch_payment_offer(recharge.payment_id)
        assert result.offer.id == offer.id
        assert result.payment_id == recharge.payment_id

    def test_fetch_payment_offer_failure(self):
        payment_id = "12345"
        result = fetch_payment_offer(payment_id)
        assert result is None

    def test_fetch_payment_offer_list_success(self):
        offer = create_offer(self.offer_data)
        link_payment_offer(offer.id, self.recharge.payment_id)
        recharge2 = RechargesFactory.create(
            billing_account=self.billing_account,
            country=self.country,
            status="0",
        )
        link_payment_offer(offer.id, recharge2.payment_id)

        offers = [
            PaymentOfferSerializer(
                fetch_payment_offer(recharge2.payment_id)
            ).data,
            PaymentOfferSerializer(
                fetch_payment_offer(self.recharge.payment_id)
            ).data,
        ]
        serializer = PaymentOfferSerializer(
            fetch_payment_offer_list(offer.id, "created", "desc"), many=True
        )
        result = serializer.data
        self.assertListEqual(result, offers)

    def test_fetch_payment_offer_list_failure(self):
        serializer = PaymentOfferSerializer(
            fetch_payment_offer_list("12345", "created", "desc"), many=True
        )
        result = serializer.data
        assert len(result) == 0

    def test_update_offer_payment_status_success(self):
        offer = create_offer(self.offer_data)
        id = offer.id
        link_payment_offer(id, self.recharge.payment_id)
        payment_offer_data = fetch_payment_offer(self.recharge.payment_id)
        result = update_payment_offer_status(
            payment_offer_data.id,
            status=constants.OFFER_PAYMENT_STATUS_APPLIED,
            offer_amount=150,
            comment="",
        )
        assert result is True

    def test_update_offer_payment_status_failure(self):
        with pytest.raises(PaymentOffers.DoesNotExist):
            update_payment_offer_status(
                "124567", status=constants.OFFER_PAYMENT_STATUS_APPLIED
            )

    def test_get_pending_offers_success(self):
        offer = create_offer(self.offer_data)
        id = offer.id
        link_payment_offer(id, self.recharge.payment_id)
        result = get_pending_offers(self.recharge.payment_id)
        assert result["offer_id"] == id

    def test_get_pending_offers_failure(self):
        offer = create_offer(self.offer_data)
        id = offer.id
        link_payment_offer(id, self.recharge.payment_id)
        payment_offer_data = fetch_payment_offer(self.recharge.payment_id)
        update_payment_offer_status(
            payment_offer_data.id,
            status=constants.OFFER_PAYMENT_STATUS_APPLIED,
            offer_amount=150,
            comment="",
        )
        result = get_pending_offers(self.recharge.payment_id)
        assert result is None
