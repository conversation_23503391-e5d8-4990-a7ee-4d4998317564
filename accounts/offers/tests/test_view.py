from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase
from accounts.billing_accounts.tests.factories import BillingAccountFactory
from accounts.core.tests.factories import CountryFactory
from accounts.offers.tests.factories import (
    OfferFactory,
    PaymentOffersFactory,
    get_random_future_date,
)
from django.utils import timezone

from accounts.products.tests.factories import OrgTypesFactory, ProductFactory
from accounts.payments.tests.factories import RechargesFactory
from accounts.offers.models import Offers
from accounts.packages.tests.factories import PackageCustomFactory


class TestOfferRetrieveUpdateAPIView(APITestCase):
    def setUp(self):
        self.country = CountryFactory.create()

        self.product = ProductFactory.create(
            country=self.country,
        )
        self.offer = OfferFactory.create(
            product_id=self.product.id,
            value="200.000",
            min_amount="300.000",
            max_amount="500.000",
        )

    def test_retrieve_offer_success(self):
        url = reverse(
            "offers:retrieve_update", kwargs={"offer_id": self.offer.id}
        )
        response = self.client.get(
            url,
            {},
            format="json",
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"

        assert response.json()["data"]["code"] == self.offer.code
        assert response.json()["data"]["name"] == self.offer.name
        assert response.json()["data"]["value"] == self.offer.value
        assert response.json()["data"]["min_amount"] == self.offer.min_amount
        assert response.json()["data"]["max_amount"] == self.offer.max_amount

    def test_retrieve_offer_404(self):
        url = reverse("offers:retrieve_update", kwargs={"offer_id": "abc123"})
        response = self.client.get(
            url,
            {},
            format="json",
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.json()["status"] == "error"

    def test_edit_offer_success(self):
        url = reverse(
            "offers:retrieve_update", kwargs={"offer_id": self.offer.id}
        )
        name = "pay 200 get 350"
        response = self.client.put(
            url,
            {
                "name": name,
                "value": "10.56",
                "product_id": self.product.id,
                "term": "flat",
                "min_amount": "400.70",
                "max_amount": "500.85",
                "expiry": get_random_future_date(30),
                "status": "active",
            },
            format="json",
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"
        assert response.json()["data"]["code"] == self.offer.code
        assert response.json()["data"]["name"] == name

    def test_edit_offer_validation_failure(self):
        url = reverse(
            "offers:retrieve_update", kwargs={"offer_id": self.offer.id}
        )
        response = self.client.put(
            url,
            {},
            format="json",
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["status"] == "error"

    def test_edit_offer_404(self):
        url = reverse("offers:retrieve_update", kwargs={"offer_id": "abc123"})
        response = self.client.put(
            url,
            {
                "name": "name",
                "value": "10.56",
                "product_id": self.product.id,
                "term": "flat",
                "min_amount": "400.70",
                "max_amount": "500.85",
                "expiry": get_random_future_date(30),
                "status": "active",
            },
            format="json",
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.json()["status"] == "error"

    def test_enable_disable_offer_success(self):
        url = reverse(
            "offers:retrieve_update", kwargs={"offer_id": self.offer.id}
        )
        response = self.client.patch(
            url,
            {
                "status": "inactive",
            },
            format="json",
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"
        offer = Offers.objects.get(id=self.offer.id)
        assert offer.status == 0

    def test_enable_disable_offer_validation_failure(self):
        url = reverse(
            "offers:retrieve_update", kwargs={"offer_id": self.offer.id}
        )
        response = self.client.patch(
            url,
            {},
            format="json",
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["status"] == "error"

    def test_enable_disable_offer_404(self):
        url = reverse("offers:retrieve_update", kwargs={"offer_id": "abc123"})
        response = self.client.patch(
            url,
            {"status": "active"},
            format="json",
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.json()["status"] == "error"


class TestOfferListCreateAPIView(APITestCase):
    def setUp(self):
        self.country = CountryFactory.create()

        self.product = ProductFactory.create(
            country=self.country,
        )
        custom_package = PackageCustomFactory.create()
        self.offer = OfferFactory.create(
            product_id=self.product.id,
            value="200.000",
            min_amount="300.000",
            max_amount="500.000",
            custom_package=custom_package,
        )

    def test_create_offer_success(self):
        url = reverse("offers:offer_create_list")
        response = self.client.post(
            url,
            {
                "name": "pay 200 get 350",
                "product_id": self.product.id,
                "value": "10.50",
                "term": "percent",
                "min_amount": "400.75",
                "max_amount": "5000.080",
                "expiry": get_random_future_date(30),
            },
            format="json",
        )
        assert response.status_code == status.HTTP_201_CREATED
        assert response.json()["status"] == "success"
        assert response.json()["data"]["name"] == "pay 200 get 350"

    def test_create_offer_failure(self):
        url = reverse("offers:offer_create_list")
        response = self.client.post(
            url,
            {},
            format="json",
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["status"] == "error"

    def test_create_offer_package_code_required(self):
        offer_data = {
            "name": "Test Offer",
            "value": 150.00,
            "term": "1",
            "type": "2",
            "custom_package_id": None,
            "min_amount": 200.000,
            "max_amount": 400.000,
            "expiry": timezone.now() + timezone.timedelta(days=365),
            "status": 1,
            "product_id": self.product.id,
        }
        url = reverse("offers:offer_create_list")
        response = self.client.post(
            url,
            offer_data,
            format="json",
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["status"] == "error"

    def test_list_offer_success_with_filter_status(self):
        # test with status active
        url = reverse("offers:offer_create_list")
        response = self.client.get(
            url,
            {"status": "active"},
            format="json",
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"
        assert response.json()["pagination"] == {
            "count": 1,
            "per_page": 10,
            "total_pages": 1,
            "current": 1,
        }

        assert response.json()["data"][0]["code"] == self.offer.code
        assert response.json()["data"][0]["name"] == self.offer.name
        assert response.json()["data"][0]["value"] == self.offer.value
        assert response.json()["data"][0]["min_amount"] == self.offer.min_amount
        assert response.json()["data"][0]["max_amount"] == self.offer.max_amount
        assert response.json()["data"][0]["status"] == "active"
        assert (
            response.json()["data"][0]["custom_package"]["id"]
            == self.offer.custom_package.id
        )

        # test with status active
        offer = OfferFactory.create(
            product_id=self.product.id,
            value="200.000",
            min_amount="300.000",
            max_amount="500.000",
            status=0,
        )
        response = self.client.get(
            url,
            {"status": "inactive"},
            format="json",
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"
        assert response.json()["data"][0]["code"] == offer.code
        assert response.json()["data"][0]["status"] == "inactive"

    def test_list_offer_success_with_fields_package(self):
        # test without filters
        url = reverse("offers:offer_create_list")
        response = self.client.get(
            url,
            {"fields": "package"},
            format="json",
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"
        assert response.json()["pagination"] == {
            "count": 1,
            "per_page": 10,
            "total_pages": 1,
            "current": 1,
        }
        assert response.json()["data"][0]["code"] == self.offer.code
        assert response.json()["data"][0]["name"] == self.offer.name
        assert response.json()["data"][0]["value"] == self.offer.value
        assert response.json()["data"][0]["min_amount"] == self.offer.min_amount
        assert response.json()["data"][0]["max_amount"] == self.offer.max_amount
        assert (
            response.json()["data"][0]["custom_package"]["id"]
            == self.offer.custom_package.id
        )
        assert (
            response.json()["data"][0]["custom_package"]["parent_package_id"]
            == self.offer.custom_package.parent_package_id
        )
        assert (
            response.json()["data"][0]["custom_package"]["custom_code"]
            == self.offer.custom_package.custom_code
        )

        # test with filters
        response = self.client.get(
            url,
            {"code": "ABC123"},
            format="json",
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"
        assert response.json()["pagination"] == {
            "count": 0,
            "per_page": 10,
            "total_pages": 1,
            "current": 1,
        }

        assert response.json()["data"] == []


class TestOfferPaymentListCreateAPIView(APITestCase):
    def setUp(self):

        self.country = CountryFactory.create()

        self.product = ProductFactory.create(
            country=self.country,
        )
        self.billing_account = BillingAccountFactory.create(discount=None)

        self.offer = OfferFactory.create(
            product_id=self.product.id,
            value="200.000",
            min_amount="300.000",
            max_amount="500.000",
        )

        self.recharge = RechargesFactory.create(
            billing_account=self.billing_account,
            country=self.country,
            status="0",
        )

        self.payment_offer = PaymentOffersFactory.create(
            offer=self.offer,
            payment=self.recharge,
        )

    def test_payment_offer_list_success(self):
        # test with data
        url = reverse(
            "offers:list_create_offer_payments",
            kwargs={"offer_id": self.offer.id},
        )
        response = self.client.get(
            url,
            {},
            format="json",
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"
        assert response.json()["pagination"] == {
            "count": 1,
            "per_page": 10,
            "total_pages": 1,
            "current": 1,
        }
        assert response.json()["data"][0]["offer_id"] == self.offer.id

        # test with no data for offer id
        url = reverse(
            "offers:list_create_offer_payments",
            kwargs={"offer_id": "abc123"},
        )
        response = self.client.get(
            url,
            {},
            format="json",
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"
        assert response.json()["pagination"] == {
            "count": 0,
            "per_page": 10,
            "total_pages": 1,
            "current": 1,
        }
        assert response.json()["data"] == []

    def test_payment_offer_list_failure(self):
        # test with data
        url = reverse(
            "offers:list_create_offer_payments",
            kwargs={"offer_id": self.offer.id},
        )
        response = self.client.get(
            url,
            {"sort": "test_sort"},
            format="json",
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["status"] == "error"

    def test_link_payment_offer_validation_failure(self):
        url = reverse(
            "offers:list_create_offer_payments",
            kwargs={"offer_id": self.offer.id},
        )
        response = self.client.post(
            url,
            {},
            format="json",
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["status"] == "error"

    def test_link_payment_offer_failure(self):
        url = reverse(
            "offers:list_create_offer_payments",
            kwargs={"offer_id": self.offer.id},
        )
        response = self.client.post(
            url,
            {"payment_id": "11111"},
            format="json",
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["status"] == "error"

    def test_link_payment_offer_success(self):
        url = reverse(
            "offers:list_create_offer_payments",
            kwargs={"offer_id": self.offer.id},
        )
        recharge = RechargesFactory.create(
            billing_account_id=self.billing_account.id,
            country_id=self.country.id,
            status="0",
        )
        response = self.client.post(
            url,
            {"payment_id": recharge.payment_id},
            format="json",
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"


class TestOfferPaymentRetrieveAPIView(APITestCase):
    def setUp(self):

        self.country = CountryFactory.create()

        self.product = ProductFactory.create(
            country=self.country,
        )
        self.billing_account = BillingAccountFactory.create(discount=None)

        self.recharge = RechargesFactory.create(
            billing_account=self.billing_account,
            country=self.country,
            status="0",
        )

        self.offer = OfferFactory.create(
            product=self.product,
        )
        self.payment_offer = PaymentOffersFactory.create(
            offer=self.offer,
            payment=self.recharge,
        )

    def test_retrieve_payment_offer_success(self):
        url = reverse(
            "offers:offer_payment_detail",
            kwargs={"payment_id": self.recharge.payment_id},
        )
        response = self.client.get(
            url,
            {},
            format="json",
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"
        assert response.json()["data"]["offer_id"] == self.offer.id
        assert response.json()["data"]["status"] == "pending"

    def test_retrieve_payment_offer_404(self):
        url = reverse(
            "offers:offer_payment_detail",
            kwargs={"payment_id": "1234"},
        )
        response = self.client.get(
            url,
            {},
            format="json",
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.json()["status"] == "error"
