import random
import string
from django.utils import timezone

from factory import Faker, SubFactory, Sequence
from factory.django import DjangoModelFactory

from accounts.offers.models import Offers, PaymentOffers
from accounts.payments.tests.factories import RechargesFactory
from accounts.products.tests.factories import ProductFactory
from accounts.packages.tests.factories import PackageCustomFactory
from accounts.offers import constants


def get_random_future_date(days_range=30):
    today = timezone.now()
    days_to_add = random.randint(1, days_range)
    future_date = today + timezone.timedelta(days=days_to_add)
    return future_date


class OfferFactory(DjangoModelFactory):
    name = Faker("word")
    code = code = Sequence(
        lambda n: "".join(
            random.choices(string.ascii_uppercase + string.digits, k=10)
        ).upper()
    )
    product = SubFactory(ProductFactory)
    value = Faker("pydecimal", left_digits=5, right_digits=3, positive=True)
    term = constants.OFFER_TERM_FLAT
    type = constants.OFFER_TYPE_CREDIT_ADDITION
    min_amount = Faker(
        "pydecimal", left_digits=5, right_digits=3, positive=True
    )
    max_amount = Faker(
        "pydecimal", left_digits=5, right_digits=3, positive=True
    )
    expiry = get_random_future_date()
    status = constants.OFFER_STATUS_ACTIVE
    custom_package = SubFactory(PackageCustomFactory)

    class Meta:
        model = Offers


class PaymentOffersFactory(DjangoModelFactory):
    offer = SubFactory(OfferFactory)
    payment = SubFactory(RechargesFactory)
    offer_amount = None
    comment = Faker("text", max_nb_chars=100)
    status = constants.OFFER_PAYMENT_STATUS_PENDING

    class Meta:
        model = PaymentOffers
