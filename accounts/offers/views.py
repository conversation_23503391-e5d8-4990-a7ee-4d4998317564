import logging

from rest_framework import generics, status
from rest_framework.exceptions import NotFound
from rest_framework.response import Response
from rest_framework.serializers import ValidationError
from rest_framework import filters
from django_filters import rest_framework as django_filters
from accounts import error_codes
from accounts.exceptions import BaseException
from accounts.offers.exceptions import InvalidOfferException
from accounts.payments.exceptions import InvalidPaymentException
from accounts.generics import SnsHandlerView
from accounts.offers.models import Offers
from accounts.offers.tasks import process_offer_payment
from accounts.offers.utils.offer_operations import (
    create_offer,
    edit_offer,
    enable_disable_offer,
    fetch_offer,
    fetch_payment_offer,
    fetch_payment_offer_list,
    link_payment_offer,
)
from accounts.offers.filters import OfferFilter
from .serializers import (
    OfferCreateSerializer,
    OfferPaymentListSerializer,
    OfferSerializer,
    OfferStatusSerializer,
    PaymentOfferCreateSerializer,
    PaymentOfferSerializer,
)
from django.db.models import F

logger = logging.getLogger(__name__)


class OfferRetrieveUpdateAPIView(generics.RetrieveUpdateAPIView):
    serializer_class = OfferSerializer

    def get_object(self):
        return fetch_offer(self.kwargs["offer_id"])

    def perform_update(self, serializer):
        return edit_offer(self.kwargs["offer_id"], serializer.data)

    def get(self, request, *args, **kwargs):
        try:
            data = self.get_object()
            if not data:
                raise NotFound(detail="Offer not found", code=404)

            serializer = self.get_serializer(data)

            return Response(
                {"status": "success", "message": "", "data": serializer.data}
            )
        except BaseException as e:
            logger.title("Offer Detail Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )

    def put(self, request, *args, **kwargs):
        serializer = OfferCreateSerializer(data=request.data)
        try:
            if serializer.is_valid(raise_exception=True):
                self.perform_update(serializer)
                serializer = self.get_serializer(self.get_object())
                return Response(
                    {
                        "status": "success",
                        "message": "Offer Updated.",
                        "data": serializer.data,
                    }
                )
        except Offers.DoesNotExist:
            raise NotFound(detail="Offer not found", code=404)
        except ValidationError as e:
            logger.title("Offer Updation Validation Error").error(
                e, exc_info=True
            )

            return Response(
                {
                    "errors": serializer.errors,
                    "code": error_codes.VALIDATION_ERROR,
                },
                status.HTTP_400_BAD_REQUEST,
            )

        except BaseException as e:
            logger.title("Offer Updation Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )

    def patch(self, request, *args, **kwargs):
        serializer = OfferStatusSerializer(data=request.data)
        try:
            if serializer.is_valid(raise_exception=True):
                enable_disable_offer(
                    kwargs["offer_id"], serializer.data["status"]
                )
                serializer = self.get_serializer(self.get_object())
                return Response(
                    {
                        "status": "success",
                        "message": "",
                        "data": serializer.data,
                    }
                )
        except Offers.DoesNotExist:
            raise NotFound(detail="Offer not found", code=404)
        except ValidationError as e:
            logger.title("Offer Enable/disable Validation Error").error(
                e, exc_info=True
            )

            return Response(
                {
                    "errors": serializer.errors,
                    "code": error_codes.VALIDATION_ERROR,
                },
                status.HTTP_400_BAD_REQUEST,
            )

        except BaseException as e:
            logger.title("Offer Enable/disable Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class OfferListCreateAPIView(generics.ListCreateAPIView):
    serializer_class = OfferSerializer
    queryset = Offers.objects.all().annotate(
        payment_cycle=F("custom_package__payment_cycle"),
        package_rent=F("custom_package__package_rent"),
    )
    filter_backends = [
        filters.OrderingFilter,
        django_filters.DjangoFilterBackend,
    ]
    filterset_class = OfferFilter
    ordering_fields = [
        "name",
        "value",
        "created",
        "payment_cycle",
        "package_rent",
    ]

    def perform_create(self, serializer):
        return create_offer(serializer.data)

    def get_object(self, id):
        return fetch_offer(id)

    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset.order_by("-created")

    def get(self, request, *args, **kwargs):
        try:
            return super().get(request, *args, **kwargs)
        except BaseException as e:
            logger.title("Offer List Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )

    def post(self, request, *args, **kwargs):
        serializer = OfferCreateSerializer(data=request.data)
        try:
            if serializer.is_valid(raise_exception=True):
                offer = self.perform_create(serializer)

                data = self.get_object(offer.id)
                serializer = self.get_serializer(data)
                return Response(
                    {
                        "status": "success",
                        "message": "Offer created.",
                        "data": serializer.data,
                    },
                    status.HTTP_201_CREATED,
                )
        except ValidationError as e:
            logger.title("Offer Creation Validation Error").error(
                e, exc_info=True
            )
            return Response(
                {
                    "errors": serializer.errors,
                    "code": error_codes.VALIDATION_ERROR,
                },
                status.HTTP_400_BAD_REQUEST,
            )

        except BaseException as e:
            logger.title("Offer Creation Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class OfferPaymentListCreateAPIView(generics.ListCreateAPIView):

    serializer_class = PaymentOfferSerializer
    page_size = 10

    def get_queryset(self, sort, order):
        return fetch_payment_offer_list(self.kwargs["offer_id"], sort, order)

    def get(self, request, *args, **kwargs):

        serializer = OfferPaymentListSerializer(data=request.query_params)
        try:
            if serializer.is_valid(raise_exception=True):

                queryset = self.filter_queryset(
                    self.get_queryset(
                        serializer.data["sort"],
                        serializer.data["order"],
                    )
                )
                data = self.paginate_queryset(queryset)
                serializer = self.get_serializer(data, many=True)
                return self.get_paginated_response(serializer.data)

        except ValidationError as e:
            logger.title("Offer Payment List Validation Error").error(
                e, exc_info=True
            )

            return Response(
                {
                    "errors": serializer.errors,
                    "code": error_codes.VALIDATION_ERROR,
                },
                status.HTTP_400_BAD_REQUEST,
            )

        except BaseException as e:
            logger.title("Offer Payment List Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )

    def post(self, request, *args, **kwargs):
        serializer = PaymentOfferCreateSerializer(data=request.data)
        try:

            if serializer.is_valid(raise_exception=True):
                try:
                    link_payment_offer(
                        kwargs["offer_id"], serializer.data["payment_id"]
                    )
                    return Response(
                        {
                            "status": "success",
                            "message": "Offer linked with payment successfuly",
                        }
                    )
                except InvalidOfferException:
                    raise NotFound(detail="Offer not found", code=404)
                except InvalidPaymentException:
                    raise ValidationError("Invalid Payment ID")
        except ValidationError as e:
            logger.title("Payment Offer Creation Validation Error").error(
                e, exc_info=True
            )
            return Response(
                {
                    "errors": e.detail,
                    "code": error_codes.VALIDATION_ERROR,
                },
                status.HTTP_400_BAD_REQUEST,
            )

        except BaseException as e:
            logger.title("Payment Offer Creation Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class OfferPaymentRetrieveAPIView(generics.RetrieveAPIView):
    serializer_class = PaymentOfferSerializer

    def get_object(self):
        return fetch_payment_offer(self.kwargs["payment_id"])

    def get(self, request, *args, **kwargs):
        try:
            data = self.get_object()
            if not data:
                raise NotFound(detail="Offer payment not found", code=404)

            serializer = self.get_serializer(data)
            return Response(
                {
                    "status": "success",
                    "message": "offer payment info",
                    "data": serializer.data,
                }
            )
        except BaseException as e:
            logger.title("Offer Payment Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class OfferEventProcessView(SnsHandlerView):
    def notification_handler(self, message):
        try:
            payment_id = message["payment_id"]
            task = (
                process_offer_payment.apply_async(
                    kwargs={"payment_id": payment_id},
                ),
            )
            logger.title("Celery Task Scheduled").info(f"ID: {task[0]}")
            return Response(
                {"message": "Task Scheduled", "data": {"task_id": str(task[0])}}
            )
        except (TypeError, KeyError):
            raise ValidationError(detail="payment_id is missing")
        except BaseException as e:
            logger.error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )
