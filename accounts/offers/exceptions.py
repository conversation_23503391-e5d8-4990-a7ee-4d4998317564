from accounts.exceptions import BaseException
from accounts.error_codes import OFFER_ALREADY_APPLIED, OFFER_EXPIRED
from rest_framework import status


class OfferCodeGenerationError(BaseException):
    message = "Unable to generate offer code, max retry limit reached"


class OfferProcessingFailedException(BaseException):
    message = "Offer payment processing error"


class OfferAlreadyAppliedException(BaseException):
    http_status_code = status.HTTP_409_CONFLICT
    error_code = OFFER_ALREADY_APPLIED
    message = "Offer already applied"


class OfferExpiredException(BaseException):
    http_status_code = status.HTTP_410_GONE
    error_code = OFFER_EXPIRED
    message = "Offer expired"


class InvalidOfferException(BaseException):
    message = "Invalid Offer"


class InvalidOfferPaymentException(BaseException):
    message = "Invalid Payment"


class CreditadditionFailedException(BaseException):
    message = "Credit addition failed"
