import logging

from django.core.management.base import BaseCommand, CommandError
from accounts.offers.utils.offer_processing import process_pending_offer_payment

logger = logging.getLogger("offer_process")


class Command(BaseCommand):

    help = "Command to process pending offer payment"

    def add_arguments(self, parser):
        parser.add_argument("payment_id", type=str)

    def handle(self, *args, **options):
        payment_id = options["payment_id"]
        if not payment_id:
            raise CommandError("payment_id can't be none")

        return self.handle_process(payment_id)

    def handle_process(self, payment_id):
        process_pending_offer_payment(payment_id)
        self.stdout.write("Offer payment processed !!")
