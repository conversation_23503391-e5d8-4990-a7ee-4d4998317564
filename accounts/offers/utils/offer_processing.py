import logging

from django.conf import settings

from accounts.core.utils.tax_calculation import calc_reverse_tax
from accounts.exceptions import ExternalAPIException
from accounts.offers.exceptions import (
    InvalidOfferPaymentException,
    OfferProcessingFailedException,
    CreditadditionFailedException,
)
from accounts.packages.exceptions import (
    PackageException,
    CustomPackageException,
    PackageChangeException,
)
from accounts.services.constants import SERVICE_STATUS_INACTIVE
from accounts.services.exceptions import InvalidServiceException
from accounts.offers.constants import (
    OFFER_STATUS_ACTIVE,
    OFFER_PAYMENT_STATUS_APPLIED,
    OFFER_PAYMENT_STATUS_REJECTED,
    OFFER_PAYMENT_STATUS_PENDING,
    OFFER_TYPE_PACKAGE,
    OFFER_TYPE_CREDIT_ADDITION,
)
from accounts.offers.models import PaymentOffers
from accounts.offers.utils.offer_operations import (
    update_payment_offer_status,
)
from accounts.payments.models import Recharges, TrackingSettlementHistories
from accounts.utils.api_services.account_v1 import AccountApiV1
from accounts.packages.models import PackageCustoms, Packages
from accounts.packages import constants as package_constants
from django.forms.models import model_to_dict
from accounts.packages.utils.create_package import (
    create_package_from_custom_package,
)
from accounts.services.utils.service import get_service_details_f_ban

logger = logging.getLogger(__name__)


def process_pending_offer_payment(payment_id: str) -> None:
    try:
        # check if there are any pending offers,if yes then process further, if no then return
        offer_payment = (
            PaymentOffers.objects.select_related("offer")
            .select_related("payment")
            .filter(payment_id=payment_id, status=OFFER_PAYMENT_STATUS_PENDING)
            .first()
        )
        if offer_payment is None:
            raise InvalidOfferPaymentException()

        # get the payment amount and billing_account_id
        payment_settlement_history = TrackingSettlementHistories.objects.filter(
            payment_id=payment_id
        ).first()
        if not payment_settlement_history:
            raise OfferProcessingFailedException(
                "Invalid Payment or not settled"
            )
        billing_account_id = (
            payment_settlement_history.billing_account.parent_id
            or payment_settlement_history.billing_account_id
        )

        # fetch offer details from offer_payment
        offer = offer_payment.offer

        if offer.status != OFFER_STATUS_ACTIVE or offer.is_offer_expired():
            raise OfferProcessingFailedException(
                "Offer is deactivated or expired"
            )

        if offer.type == OFFER_TYPE_PACKAGE:
            service = get_service_details_f_ban(billing_account_id)
            if not service:
                raise InvalidServiceException("Service not found")
            if service.is_demo():
                raise InvalidServiceException("Demo Service")
            if service.status == SERVICE_STATUS_INACTIVE:
                raise InvalidServiceException("Service is Inactive")
            offer_amount = process_package_change(
                offer.custom_package_id, service.gsn
            )
        elif offer.type == OFFER_TYPE_CREDIT_ADDITION:
            offer_amount = add_credit(offer_payment.id, billing_account_id)
        else:
            raise ValueError("Invalid offer type")

        # if payment amount is within range then mark status as 1 (APPLIED)
        update_payment_offer_status(
            offer_payment.id,
            status=OFFER_PAYMENT_STATUS_APPLIED,
            offer_amount=offer_amount,
        )
    except OfferProcessingFailedException as e:
        # mark status as rejected in payment_offers table
        update_payment_offer_status(
            offer_payment.id,
            status=OFFER_PAYMENT_STATUS_REJECTED,
            comment=str(e),
        )
        raise


def process_package_change(custom_package_id, gsn):
    try:
        custom_package = PackageCustoms.objects.get(id=custom_package_id)
    except PackageCustoms.DoesNotExist:
        raise CustomPackageException("custom package not found")

    logger.title("process_package_change").info(
        "custom package %s", model_to_dict(custom_package)
    )

    active_package = Packages.objects.filter(
        code=custom_package.custom_code,
        status=package_constants.PACKAGE_STATUS_ACTIVE,
    ).first()

    if not active_package:
        raise PackageException("Invalid package")

    logger.title("process_package_change").info(
        "current active package %s", model_to_dict(active_package)
    )

    # change package from current billing cycle
    mode = 2

    change_package_pending_amount = (
        AccountApiV1().change_package_pending_amount(
            active_package.id, gsn, mode
        )
    )

    if not change_package_pending_amount:
        raise PackageChangeException("Change package pending amount api failed")

    if (
        float(
            change_package_pending_amount["data"]["amount_without_tax"].replace(
                ",", ""
            )
        )
        > 0
    ):
        raise OfferProcessingFailedException(
            "Package cannnot be changed due to pending amount"
        )

    package = create_package_from_custom_package(custom_package_id)

    change_package = AccountApiV1().change_package(package.id, gsn, mode)

    if not change_package:
        raise PackageChangeException("Change package api failed")

    offer_amount = 0
    if package.discount:
        offer_amount = package.discount.value

    return offer_amount


def add_credit(offer_payment_id, billing_account_id):

    offer_payment = (
        PaymentOffers.objects.select_related("offer")
        .select_related("payment")
        .filter(id=offer_payment_id, status=OFFER_PAYMENT_STATUS_PENDING)
        .first()
    )
    if offer_payment is None:
        raise InvalidOfferPaymentException()

    offer = offer_payment.offer

    # fetch recharge details from offer_payment
    recharge = offer_payment.payment
    # calculate reverse tax amount
    amount = calc_reverse_tax(recharge.country_id, recharge.amount)
    # calculate offer amount
    offer_amount = offer.calc_offer_amount(amount)
    # Check if offer is active and offer is not expired

    if offer.is_amount_outside_range(amount):
        # Check if payment amount is within range
        raise OfferProcessingFailedException(
            f"Payment amount doesn't fall within the valid range: {offer.min_amount} - {offer.max_amount}"
        )

    description = f"received {offer_amount} for offer code {offer.code}"
    result = AccountApiV1().add_credit(
        billing_account_id,
        offer_amount,
        description,
        settings.INTERNAL_API_USER_ID,
    )
    if not result:
        raise CreditadditionFailedException("Credit addition api failed")

    return offer_amount
