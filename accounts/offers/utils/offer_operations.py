import logging
from typing import Optional

from django.db.models import Q
from django.utils.crypto import get_random_string

from accounts.exceptions import DatabaseInsertionException, DataNotFound

from accounts.offers.exceptions import (
    OfferCodeGenerationError,
    OfferAlreadyAppliedException,
    OfferExpiredException,
    InvalidOfferException,
)
from accounts.payments.exceptions import InvalidPaymentException

from accounts.offers import constants
from accounts.offers.models import Offers, PaymentOffers, Products, Recharges

logger = logging.getLogger(__name__)


def create_offer(data: dict) -> Offers:
    """
    Creates an offer based on the provided data.

    Parameters:
        data (dict): A dictionary containing the data for creating an offer. It should include the following keys:
            - product_id (str): The ID of the product.
            - package_id (str): The ID of the package.
            - name (str): The name of the offer.
            - value (float): The value of the offer.
            - term (str): The term of the offer.
            - type (str): The type of the offer.
            - min_amount (float): The minimum amount for the offer.
            - max_amount (float): The maximum amount for the offer.
            - expiry (datetime): The expiry date of the offer.
            - status (str): The status of the offer.

    Returns:
        Offers: The created offer object.
    """
    offer_code = generate_offer_code()
    offer = Offers.objects.create(
        product_id=data["product_id"],
        custom_package_id=data["custom_package_id"],
        name=data["name"],
        value=data["value"],
        code=offer_code,
        term=data["term"],
        type=data["type"],
        min_amount=data["min_amount"],
        max_amount=data["max_amount"],
        expiry=data["expiry"],
        status=data["status"],
    )
    return offer


def generate_offer_code() -> str:
    """
    Generates a unique offer code using a specified set of allowed characters.

    Returns:
        str: The generated code.

    Raises:
        OfferCodeGenerationError: If a unique code cannot be generated after the maximum number of attempts.
    """
    allowed_chars = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789"

    max_attempts = 5

    for _ in range(max_attempts):
        code = get_random_string(length=6, allowed_chars=allowed_chars)
        if not check_code_exists(code):
            return code

    raise OfferCodeGenerationError()


def check_code_exists(code: str) -> bool:
    """
    Check if a code exists in the Offers table.

    Args:
        code (str): The code to check.

    Returns:
        bool: True if the code exists, False otherwise.
    """
    return Offers.objects.filter(code=code).exists()


def fetch_offer(id: str) -> dict:
    return Offers.objects.filter(id=id).first()


def edit_offer(id, data):
    obj = Offers.objects.get(id=id)
    obj.name = data["name"]
    obj.product_id = Products(data["product_id"])
    obj.custom_package_id = data["custom_package_id"]
    obj.value = data["value"]
    obj.term = data["term"]
    obj.status = data["status"]
    obj.min_amount = data["min_amount"]
    obj.max_amount = data["max_amount"]
    obj.expiry = data["expiry"]
    obj.save()
    return True


def enable_disable_offer(id, status):
    obj = Offers.objects.get(id=id)
    obj.status = status
    obj.save()
    return True


def fetch_payment_offer(payment_id: str) -> PaymentOffers:
    return (
        PaymentOffers.objects.select_related("payment")
        .filter(payment_id=payment_id)
        .first()
    )


def link_payment_offer(offer_id: str, payment_id: str) -> PaymentOffers:
    """
    Link a pending payment to an active offer.

    Args:
        offer_id (str): The ID of the offer to be linked.
        payment_id (str): The ID of the payment to be linked.

    Returns:
        PaymentOffers: The linked offer payment.

    Raises:
        InvalidOfferException: If the offer with the provided ID does not exist.
        OfferExpiredException: If the offer has expired.
        InvalidPaymentException: If the payment with the provided ID does not exist or is already completed.
        OfferAlreadyAppliedException: If an offer is already applied to the payment ID.

    Notes:
        - If a pending state already exists for the provided payment ID, offer will be overridden.
    """
    offer = Offers.objects.filter(
        id=offer_id, status=constants.OFFER_STATUS_ACTIVE
    ).first()
    if not offer:
        raise InvalidOfferException()

    if offer.is_offer_expired():
        raise OfferExpiredException()

    # check if payment exists and its not completed (Y=completed)
    recharge = Recharges.objects.filter(payment_id=payment_id).first()
    if not recharge:
        raise InvalidPaymentException()

    offer_payment = PaymentOffers.objects.filter(payment_id=payment_id).first()
    if (
        offer_payment
        and offer_payment.status == constants.OFFER_PAYMENT_STATUS_APPLIED
    ):
        raise OfferAlreadyAppliedException()

    if recharge.is_completed():
        raise InvalidPaymentException("Can't link offer to a completed payment")

    if offer_payment:
        logger.title("Overriding existing offer payment").info(
            {
                "offer_id": offer_payment.offer_id,
                "status": offer_payment.status,
                "comment": offer_payment.comment,
            }
        )

    linked_offer_payment, _ = PaymentOffers.objects.update_or_create(
        payment_id=recharge.payment_id,
        defaults={
            "offer_id": offer.id,
            "comment": "",
            "status": constants.OFFER_PAYMENT_STATUS_PENDING,
        },
    )
    return linked_offer_payment


def fetch_payment_offer_list(offer_id, sort, order):
    queryset = PaymentOffers.objects.select_related("payment").filter(
        offer_id=offer_id
    )
    if order == "asc":
        queryset = queryset.order_by(sort)
    else:
        queryset = queryset.order_by("-" + sort)
    return queryset.all()


def get_pending_offers(payment_id: str) -> Optional[dict]:
    """
    Get the pending offers for a given payment ID.

    Args:
        payment_id (str): The ID of the payment.

    Returns:
        Optional[dict]: A dictionary containing the ID and offer ID of the first pending offer,
        or None if no pending offers are found.
    """
    return (
        PaymentOffers.objects.filter(
            payment_id=payment_id,
            status=constants.OFFER_PAYMENT_STATUS_PENDING,
        )
        .values("id", "offer_id")
        .first()
    )


def update_payment_offer_status(id, status, **kwargs):
    obj = PaymentOffers.objects.get(id=id)
    obj.status = status

    if "comment" in kwargs:
        obj.comment = kwargs["comment"]

    if "offer_amount" in kwargs:
        obj.offer_amount = kwargs["offer_amount"]

    obj.save()
    return True
