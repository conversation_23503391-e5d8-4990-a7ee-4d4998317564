import logging
import celery
import celery.states

from django.conf import settings
from accounts.offers.exceptions import (
    InvalidOfferPaymentException,
    OfferProcessingFailedException,
)
from accounts.services.exceptions import InvalidServiceException
from accounts.offers.utils.offer_processing import process_pending_offer_payment

logger = logging.getLogger(__name__)


@celery.shared_task(
    bind=True,
    max_retries=settings.CELERY_TASK_MAX_RETRIES,
    queue=settings.CELERY_TASK_DEFAULT_QUEUE,
)
def process_offer_payment(task: celery.Task, payment_id: str) -> None:
    """
    Process the offer payment task asynchronously.

    Args:
        task (celery.Task): The celery task object.
        payment_id (str): The ID of the payment to process.

    Returns:
        None

    Raises:
        OfferProcessingFailedException: If the offer payment processing fails.
        InvalidOfferPaymentException: If payment is invalid.
        Exception: If any other exception occurs during processing.

    Description:
        This function is a Celery task that processes the offer payment asynchronously.
        It updates the state of the task to "PROGRESS" at the beginning.
        It then calls the `process_pending_offer_payment` function to process the payment.
        If the payment processing is successful, it logs the success message and updates the state to "SUCCESS".
        If the payment processing fails due to `OfferProcessingFailedException` or `InvalidOfferPaymentException`,
        it logs the error message and updates the state to "FAILED".
        For any other exceptions, it logs the critical error message and updates the state to "FAILED".
    """
    task.update_state(state=celery.states.STARTED)
    try:
        try:
            process_pending_offer_payment(payment_id)
        except InvalidServiceException as e:
            logger.title("Offer payment Task Error").error(e, exc_info=True)
            task.update_state(state=celery.states.RETRY)
            raise task.retry(countdown=10)
        logger.info(f"Offer payment processed for payment_id: {payment_id}")
        task.update_state(state=celery.states.SUCCESS)
    except celery.exceptions.Retry as e:
        logger.title("Offer payment Task Retry").info(str(e))
    except celery.exceptions.MaxRetriesExceededError as e:
        task.update_state(state=celery.states.FAILURE)
        logger.title("Offer payment Task Failed").critical(e, exc_info=True)
    except (
        OfferProcessingFailedException,
        InvalidOfferPaymentException,
    ) as e:
        logger.title("Offer payment Task Failed").error(e)
        task.update_state(state=celery.states.FAILURE)
    except Exception as e:
        logger.title("Offer payment Task Failed").critical(e, exc_info=True)
        task.update_state(state=celery.states.FAILURE)
