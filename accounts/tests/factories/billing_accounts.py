from factory.django import DjangoModelFactory

from accounts.billing_accounts.enums import DocTypeEnums
from accounts.billing_accounts.models import DocTypes


class DocTypeFactory(DjangoModelFactory):
    class Meta:
        model = DocTypes

    @classmethod
    def load_doc_type(cls):
        for doc_type in DocTypeEnums:
            DocTypes.objects.get_or_create(
                short_code=doc_type.value,
                name=doc_type.name,
            )
