from factory import Faker, Sequence, SubFactory
from factory.django import DjangoModelFactory

from accounts.users.models import Groups, Roles, UserProfiles


class GroupFactory(DjangoModelFactory):
    name = Faker("text", max_nb_chars=20)
    parent_id = "0"
    lft = Faker("random_int")
    rght = Faker("random_int")

    class Meta:
        model = Groups


class RoleFactory(DjangoModelFactory):
    name = Faker("text", max_nb_chars=20)

    class Meta:
        model = Roles


class UserProfileFactory(DjangoModelFactory):
    mapping_id = 0
    name = Faker("text", max_nb_chars=30)
    email = Sequence(lambda n: "user.{}@example.com".format(n))
    phone = Faker("phone_number")
    group = SubFactory(GroupFactory)
    role = SubFactory(RoleFactory)
    is_admin = Faker("random_element", elements=[0, 1])
    google_profile = Faker("url")
    fb_profile = Faker("url")
    linkedin_profile = Faker("url")
    pipedrive_uid = Faker("random_int")
    zoho_id = Faker("text", max_nb_chars=150)
    zoho_id_type = 0
    timezone = Faker("timezone")

    class Meta:
        model = UserProfiles
