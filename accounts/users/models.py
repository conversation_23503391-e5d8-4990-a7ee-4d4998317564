from django.db import models

from accounts.utils.common import uuid


class Groups(models.Model):
    id = models.CharField(max_length=36, primary_key=True, default=uuid)
    name = models.CharField(max_length=250)
    parent_id = models.CharField(max_length=36, default="0")
    lft = models.IntegerField(default=0)
    rght = models.IntegerField(default=0)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "groups"


class Roles(models.Model):
    id = models.Char<PERSON>ield(max_length=36, primary_key=True, default=uuid)
    name = models.Char<PERSON>ield(max_length=70)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = "roles"


class UserProfiles(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    mapping_id = models.IntegerField(default=0)
    name = models.CharField(max_length=250)
    email = models.EmailField(max_length=250, unique=True)
    phone = models.CharField(max_length=50)
    group = models.ForeignKey(Groups, on_delete=models.CASCADE)
    role = models.ForeignKey(Roles, on_delete=models.CASCADE)
    is_admin = models.SmallIntegerField(
        default=0, help_text="1=> admin, 0 => no admin"
    )
    google_profile = models.CharField(max_length=250, null=True)
    fb_profile = models.CharField(max_length=250, null=True)
    linkedin_profile = models.CharField(max_length=250, null=True)
    pipedrive_uid = models.IntegerField(default=0)
    zoho_id = models.CharField(max_length=250, null=True)
    zoho_id_type = models.IntegerField(default=0, help_text="1- Agent, 2- Team")
    timezone = models.CharField(max_length=200)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "user_profiles"
        indexes = [
            models.Index(fields=["group_id"]),
            models.Index(fields=["role_id"]),
            models.Index(fields=["is_admin"]),
            models.Index(fields=["name"]),
            models.Index(fields=["email"]),
            models.Index(fields=["phone"]),
        ]


class ApiKeys(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    user_profile = models.ForeignKey(
        to=UserProfiles,
        to_field="id",
        db_column="user_profile_id",
        on_delete=models.CASCADE,
    )
    api_key = models.CharField(max_length=200, unique=True)
    description = models.CharField(max_length=100, null=True, blank=True)
    status = models.SmallIntegerField(default=1)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "user_api_keys"


class Prospects(models.Model):
    id = models.AutoField(primary_key=True)
    user_profile = models.ForeignKey(
        UserProfiles, on_delete=models.CASCADE, null=True
    )
    company_name = models.CharField(max_length=100, default="0")
    full_name = models.CharField(max_length=50, default="0")
    email = models.CharField(max_length=100, null=True)
    phone = models.CharField(max_length=12)
    c_short_code = models.CharField(max_length=2, null=True)
    country_code = models.CharField(max_length=10, null=True)
    address = models.CharField(max_length=255, default="0")
    demo_created = models.IntegerField(
        default=0, help_text="0-not created, 1- cretaed"
    )
    is_caf = models.IntegerField(
        default=0, help_text="0-not created, 1- cretaed"
    )
    is_customer = models.IntegerField(
        default=0, help_text="0-not cutomer, 1- customer"
    )
    is_customer_newapp = models.IntegerField(
        default=0, help_text="0-not cutomer, 1- customer"
    )
    pin_code = models.CharField(max_length=16, null=True)
    sales_crm_flag = models.IntegerField(
        default=0, help_text="0-not in sales, 1- in sales"
    )
    status = models.SmallIntegerField(default=1)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "prospects"


class RawProposals(models.Model):
    id = models.AutoField(primary_key=True)
    req_data = models.TextField()
    proposal_data = models.TextField()
    user_profile = models.ForeignKey(
        UserProfiles, on_delete=models.CASCADE, null=True
    )
    proposal_url = models.TextField()
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "raw_proposals"


class PoolingHistories(models.Model):
    id = models.CharField(primary_key=True, max_length=36, default=uuid)
    parent_billing_account_id = models.CharField(max_length=36)
    child_billing_account_id = models.CharField(max_length=36)
    user_profile = models.ForeignKey(
        UserProfiles, on_delete=models.CASCADE, null=True
    )
    action = models.SmallIntegerField(
        default=1,
        help_text="1 => pool, 2 => un_pool, 3 => feature_updated, 4 => package_upgrade, 5 => package_downgrade",
    )
    activity_date = models.DateTimeField()
    status = models.SmallIntegerField(
        default=1, help_text="0 => inactive, 1=>active"
    )
    description = models.TextField()
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "pooling_histories"
