# Generated by Django 3.2.18 on 2025-03-19 11:54

import accounts.utils.common
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Groups',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, max_length=36, primary_key=True, serialize=False)),
                ('name', models.Char<PERSON>ield(max_length=250)),
                ('parent_id', models.CharField(default='0', max_length=36)),
                ('lft', models.IntegerField(default=0)),
                ('rght', models.IntegerField(default=0)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'groups',
            },
        ),
        migrations.CreateModel(
            name='Roles',
            fields=[
                ('id', models.<PERSON><PERSON><PERSON><PERSON>(default=accounts.utils.common.uuid, max_length=36, primary_key=True, serialize=False)),
                ('name', models.Char<PERSON><PERSON>(max_length=70)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'roles',
            },
        ),
        migrations.CreateModel(
            name='UserProfiles',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('mapping_id', models.IntegerField(default=0)),
                ('name', models.CharField(max_length=250)),
                ('email', models.EmailField(max_length=250, unique=True)),
                ('phone', models.CharField(max_length=50)),
                ('is_admin', models.SmallIntegerField(default=0, help_text='1=> admin, 0 => no admin')),
                ('google_profile', models.CharField(max_length=250, null=True)),
                ('fb_profile', models.CharField(max_length=250, null=True)),
                ('linkedin_profile', models.CharField(max_length=250, null=True)),
                ('pipedrive_uid', models.IntegerField(default=0)),
                ('zoho_id', models.CharField(max_length=250, null=True)),
                ('zoho_id_type', models.IntegerField(default=0, help_text='1- Agent, 2- Team')),
                ('timezone', models.CharField(max_length=200)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='users.groups')),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='users.roles')),
            ],
            options={
                'db_table': 'user_profiles',
            },
        ),
        migrations.CreateModel(
            name='RawProposals',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('req_data', models.TextField()),
                ('proposal_data', models.TextField()),
                ('proposal_url', models.TextField()),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('user_profile', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='users.userprofiles')),
            ],
            options={
                'db_table': 'raw_proposals',
            },
        ),
        migrations.CreateModel(
            name='Prospects',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('company_name', models.CharField(default='0', max_length=100)),
                ('full_name', models.CharField(default='0', max_length=50)),
                ('email', models.CharField(max_length=100, null=True)),
                ('phone', models.CharField(max_length=12)),
                ('c_short_code', models.CharField(max_length=2, null=True)),
                ('country_code', models.CharField(max_length=10, null=True)),
                ('address', models.CharField(default='0', max_length=255)),
                ('demo_created', models.IntegerField(default=0, help_text='0-not created, 1- cretaed')),
                ('is_caf', models.IntegerField(default=0, help_text='0-not created, 1- cretaed')),
                ('is_customer', models.IntegerField(default=0, help_text='0-not cutomer, 1- customer')),
                ('is_customer_newapp', models.IntegerField(default=0, help_text='0-not cutomer, 1- customer')),
                ('pin_code', models.CharField(max_length=16, null=True)),
                ('sales_crm_flag', models.IntegerField(default=0, help_text='0-not in sales, 1- in sales')),
                ('status', models.SmallIntegerField(default=1)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('user_profile', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='users.userprofiles')),
            ],
            options={
                'db_table': 'prospects',
            },
        ),
        migrations.CreateModel(
            name='PoolingHistories',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, max_length=36, primary_key=True, serialize=False)),
                ('parent_billing_account_id', models.CharField(max_length=36)),
                ('child_billing_account_id', models.CharField(max_length=36)),
                ('action', models.SmallIntegerField(default=1, help_text='1 => pool, 2 => un_pool, 3 => feature_updated, 4 => package_upgrade, 5 => package_downgrade')),
                ('activity_date', models.DateTimeField()),
                ('status', models.SmallIntegerField(default=1, help_text='0 => inactive, 1=>active')),
                ('description', models.TextField()),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('user_profile', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='users.userprofiles')),
            ],
            options={
                'db_table': 'pooling_histories',
            },
        ),
        migrations.CreateModel(
            name='ApiKeys',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('api_key', models.CharField(max_length=200, unique=True)),
                ('description', models.CharField(blank=True, max_length=100, null=True)),
                ('status', models.SmallIntegerField(default=1)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('user_profile', models.ForeignKey(db_column='user_profile_id', on_delete=django.db.models.deletion.CASCADE, to='users.userprofiles')),
            ],
            options={
                'db_table': 'user_api_keys',
            },
        ),
        migrations.AddIndex(
            model_name='userprofiles',
            index=models.Index(fields=['group_id'], name='user_profil_group_i_a73c5b_idx'),
        ),
        migrations.AddIndex(
            model_name='userprofiles',
            index=models.Index(fields=['role_id'], name='user_profil_role_id_fe4ab9_idx'),
        ),
        migrations.AddIndex(
            model_name='userprofiles',
            index=models.Index(fields=['is_admin'], name='user_profil_is_admi_3674d2_idx'),
        ),
        migrations.AddIndex(
            model_name='userprofiles',
            index=models.Index(fields=['name'], name='user_profil_name_267079_idx'),
        ),
        migrations.AddIndex(
            model_name='userprofiles',
            index=models.Index(fields=['email'], name='user_profil_email_8a1024_idx'),
        ),
        migrations.AddIndex(
            model_name='userprofiles',
            index=models.Index(fields=['phone'], name='user_profil_phone_9c9b5b_idx'),
        ),
    ]
