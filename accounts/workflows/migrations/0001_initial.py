# Generated by Django 3.2.18 on 2025-03-19 11:54

import accounts.utils.common
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='KissflowFailedRequests',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=250)),
                ('url', models.CharField(max_length=250)),
                ('data', models.TextField()),
                ('method', models.CharField(choices=[('GET', 'GET'), ('POST', 'POST')], max_length=4)),
                ('email', models.CharField(max_length=250, null=True)),
                ('response', models.TextField()),
                ('app', models.SmallIntegerField(help_text='1- Activation, 2- KYC')),
                ('error_type', models.SmallIntegerField(help_text='1- ValidationError, 2- GeneralError')),
                ('status', models.SmallIntegerField(default=1)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'kissflow_failed_requests',
            },
        ),
        migrations.CreateModel(
            name='KissflowVtRequests',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('base_model', models.CharField(max_length=250)),
                ('base_model_id', models.CharField(max_length=250)),
                ('kf_app_name', models.CharField(max_length=250, null=True)),
                ('kf_app_action', models.CharField(max_length=250, null=True)),
                ('kf_id', models.CharField(max_length=250, null=True)),
                ('kf_req_date', models.DateTimeField()),
                ('kf_req_initiator', models.CharField(max_length=250, null=True)),
                ('kf_req_inputs', models.TextField()),
                ('kf_req_output', models.TextField(null=True)),
                ('track_request', models.CharField(default='1', max_length=2)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'kissflow_vt_requests',
            },
        ),
        migrations.CreateModel(
            name='PrePaymentRequest',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=50, primary_key=True, serialize=False)),
                ('request_for', models.CharField(max_length=50, null=True)),
                ('data', models.TextField(null=True)),
                ('created', models.DateTimeField(null=True)),
            ],
            options={
                'db_table': 'pre_payment_request',
            },
        ),
        migrations.CreateModel(
            name='KissflowVtResponses',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('kf_response', models.TextField()),
                ('kf_res_action', models.CharField(max_length=250)),
                ('kf_res_done_by', models.TextField()),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('kissflow_vt_request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='workflows.kissflowvtrequests')),
            ],
            options={
                'db_table': 'kissflow_vt_responses',
            },
        ),
        migrations.AddIndex(
            model_name='kissflowvtrequests',
            index=models.Index(fields=['base_model', 'base_model_id', 'kf_app_action'], name='kissflow_vt_base_mo_03aa42_idx'),
        ),
        migrations.AddIndex(
            model_name='kissflowvtrequests',
            index=models.Index(fields=['kf_id'], name='kissflow_vt_kf_id_22e733_idx'),
        ),
        migrations.AddIndex(
            model_name='kissflowfailedrequests',
            index=models.Index(fields=['status'], name='kissflow_fa_status_33c627_idx'),
        ),
        migrations.AddIndex(
            model_name='kissflowvtresponses',
            index=models.Index(fields=['kissflow_vt_request_id', 'kf_res_action'], name='kissflow_vt_kissflo_a30ae8_idx'),
        ),
    ]
