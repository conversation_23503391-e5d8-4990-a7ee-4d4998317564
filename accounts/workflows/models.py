from django.db import models

from accounts.utils.common import uuid


class KissflowVtRequests(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    base_model = models.CharField(max_length=250)
    base_model_id = models.CharField(max_length=250)
    kf_app_name = models.CharField(max_length=250, null=True)
    kf_app_action = models.CharField(max_length=250, null=True)
    kf_id = models.CharField(max_length=250, null=True)
    kf_req_date = models.DateTimeField()
    kf_req_initiator = models.CharField(max_length=250, null=True)
    kf_req_inputs = models.TextField()
    kf_req_output = models.TextField(null=True)
    track_request = models.CharField(max_length=2, default="1")
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "kissflow_vt_requests"
        indexes = [
            models.Index(
                fields=["base_model", "base_model_id", "kf_app_action"]
            ),
            models.Index(fields=["kf_id"]),
        ]


class KissflowVtResponses(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    kissflow_vt_request = models.ForeignKey(
        KissflowVtRequests, on_delete=models.CASCADE
    )
    kf_response = models.TextField()
    kf_res_action = models.CharField(max_length=250)
    kf_res_done_by = models.TextField()
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "kissflow_vt_responses"
        indexes = [
            models.Index(fields=["kissflow_vt_request_id", "kf_res_action"])
        ]


class KissflowFailedRequests(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=250)
    url = models.CharField(max_length=250)
    data = models.TextField()
    method = models.CharField(
        max_length=4,
        choices=[("GET", "GET"), ("POST", "POST")],
    )
    email = models.CharField(max_length=250, null=True)
    response = models.TextField()
    app = models.SmallIntegerField(help_text="1- Activation, 2- KYC")
    error_type = models.SmallIntegerField(
        help_text="1- ValidationError, 2- GeneralError"
    )
    status = models.SmallIntegerField(default=1)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "kissflow_failed_requests"
        indexes = [models.Index(fields=["status"])]


class PrePaymentRequest(models.Model):
    id = models.CharField(
        max_length=50, primary_key=True, default=uuid, editable=False
    )
    request_for = models.CharField(max_length=50, null=True)
    data = models.TextField(null=True)
    created = models.DateTimeField(null=True)

    objects = models.Manager()

    class Meta:
        db_table = "pre_payment_request"
