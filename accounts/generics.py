import json
import logging
from abc import ABC, abstractmethod

import requests
from rest_framework.response import Response
from rest_framework.views import APIView

from .authentication import SnsAuthentication

logger = logging.getLogger(__name__)


class SnsHandlerView(ABC, APIView):
    authentication_classes = [SnsAuthentication]
    message_type_header = "HTTP_X_AMZ_SNS_MESSAGE_TYPE"

    def post(self, request):
        if self.message_type_header in request.META:
            payload = json.loads(request.body.decode("utf-8"))
            logger.debug("Payload: %s", payload)
            logger.debug("Headers: %s", request.META)

            message_type = request.META[self.message_type_header]
            if message_type == "SubscriptionConfirmation":
                subscribe_url = payload.get("SubscribeURL")
                res = requests.get(subscribe_url)
                if res.status_code != 200:
                    logger.error(
                        "Failed to verify SNS Subscription",
                        extra={
                            "verification_response": res.content,
                            "sns_payload": request.body,
                        },
                    )

                    return Response({"status": "error"}, status=400)
            elif message_type == "UnsubscribeConfirmation":
                logger.warning(
                    payload, title="You have unsubscribed from sns events"
                )
                return Response({})
            elif message_type == "Notification":
                if (
                    request.META.get("HTTP_X_AMZ_SNS_RAWDELIVERY", "false")
                    == "false"
                ):
                    # if raw-delivery is disabled, extract payload from `Message` key
                    payload = json.loads(payload.get("Message"))
                logger.info(
                    "SNS Notification received",
                    extra=dict(
                        message_type=message_type,
                        payload_message=payload,
                    ),
                )

                return self.notification_handler(payload)
            else:
                logger.warning(payload, title="Unknown message type")
                return Response({})

        return Response({})

    @abstractmethod
    def notification_handler(self, payload):
        pass
