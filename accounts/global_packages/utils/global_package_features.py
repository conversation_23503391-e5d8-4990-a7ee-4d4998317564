import logging
from accounts.global_packages.models import (
    GlobalPackageFeatures,
)
from accounts.global_packages import constants
from typing import Optional

logger = logging.getLogger(__name__)


def get_active_global_package_feature(
    package_id: str,
    product_feature_id: str,
    product_feature_property_id: Optional[str] = None,
) -> GlobalPackageFeatures:

    filter_args = {
        "package_id": package_id,
        "product_feature_id": product_feature_id,
        "status": constants.GLOBAL_PACKAGE_FEATURE_ENABLED,
    }

    if product_feature_property_id is not None:
        filter_args["product_feature_property_id"] = product_feature_property_id

    query_set = GlobalPackageFeatures.objects.filter(**filter_args)

    logger.title("Query #get_active_global_package_feature").info(
        query_set.query
    )

    return query_set.first()
