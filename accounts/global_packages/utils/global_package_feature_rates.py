import logging
from accounts.global_packages.models import (
    GlobalPackageFeatureRates,
)
from accounts.global_packages import constants

logger = logging.getLogger(__name__)


def get_active_global_package_feature_rate(
    package_feature_id: str,
) -> GlobalPackageFeatureRates:

    query_set = GlobalPackageFeatureRates.objects.filter(
        package_feature_id=package_feature_id,
        status=constants.GLOBAL_PACKAGE_FEATURE_RATE_ACTIVE,
    )

    logger.title("Query #get_active_global_package_feature_rate").info(
        query_set.query
    )

    return query_set.first()
