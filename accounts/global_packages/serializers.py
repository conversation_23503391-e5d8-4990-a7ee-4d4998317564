from rest_framework import serializers
from rest_flex_fields import FlexFieldsModelSerializer
from accounts.global_packages.models import (
    GlobalPackages,
    GlobalPackageFeatures,
    GlobalPackageFeatureRates,
    PackageCategories,
)
from accounts.products.serializers import ProductFeatureSerializer
from .enums import (
    PackageForEnum,
    PackageTypeEnum,
    OcsFlagEnum,
    StatusEnum as PackageStatusEnum,
    GlobalPackageFeatureStatusEnum,
    PackageFeatureRateSlabStatusEnum,
)


class GlobalPackageSerializer(FlexFieldsModelSerializer):
    code = serializers.CharField(read_only=True)
    package_type = serializers.SerializerMethodField()
    package_for = serializers.SerializerMethodField()
    ocs_flag = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()

    def get_package_type(self, obj):
        return PackageTypeEnum.get_name(obj.package_type).lower()

    def get_package_for(self, obj):
        return PackageForEnum.get_name(obj.package_for).lower()

    def get_ocs_flag(self, obj):
        return OcsFlagEnum.get_name(obj.ocs_flag).lower()

    def get_status(self, obj):
        return PackageStatusEnum.get_name(obj.status).lower()

    class Meta:
        model = GlobalPackages
        fields = (
            "id",
            "product_id",
            "package_category_id",
            "name",
            "code",
            "description",
            "package_type",
            "rent_per_month",
            "renew_cycle",
            "is_public",
            "ocs_flag",
            "package_for",
            "package_number",
            "discount_id",
            "status",
            "created",
            "modified",
        )
        read_only_fields = (
            "product_id",
            "code",
            "created",
        )


class GlobalPackageFeatureRateSerializer(serializers.ModelSerializer):
    status = serializers.SerializerMethodField()

    def get_status(self, obj):
        return PackageFeatureRateSlabStatusEnum.get_name(obj.status).lower()

    class Meta:
        model = GlobalPackageFeatureRates
        fields = (
            "id",
            "package_feature_id",
            "min",
            "max",
            "rate",
            "status",
            "created",
            "modified",
        )
        read_only_fields = ("package_feature_id", "created")


class GlobalPackageFeatureSerializer(FlexFieldsModelSerializer):
    product_feature_id = serializers.PrimaryKeyRelatedField(
        source="product_feature", read_only=True
    )
    product_feature_property_id = serializers.PrimaryKeyRelatedField(
        source="product_feature_property", read_only=True
    )
    status = serializers.SerializerMethodField()
    resource_key = serializers.SerializerMethodField()
    memcache_key = serializers.SerializerMethodField()

    def get_status(self, obj: GlobalPackageFeatures):
        return GlobalPackageFeatureStatusEnum.get_name(obj.status).lower()

    def get_resource_key(self, obj: GlobalPackageFeatures):
        return obj.get_resource_key()

    def get_memcache_key(self, obj: GlobalPackageFeatures):
        return obj.get_memcache_key()

    class Meta:
        model = GlobalPackageFeatures
        expandable_fields = {
            "product_feature": ProductFeatureSerializer,
            "rate_slabs": (GlobalPackageFeatureRateSerializer, {"many": True}),
        }
        fields = (
            "id",
            "product_feature_id",
            "product_feature_property_id",
            "free_unit",
            "rent_per_month",
            "last_disabled_date",
            "resource_key",
            "memcache_key",
            "status",
            "created",
            "modified",
        )


class PackageCategorySerializer(FlexFieldsModelSerializer):
    class Meta:
        model = PackageCategories
        fields = (
            "id",
            "product_id",
            "code",
            "name",
            # "proposal_file",
            "description",
            "weightage",
            "created",
            "modified",
        )
