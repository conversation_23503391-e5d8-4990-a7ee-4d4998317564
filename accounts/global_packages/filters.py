from django.core.exceptions import ValidationError
from django_filters import rest_framework as django_filters
from django_filters import rest_framework as filters
from accounts.global_packages.models import (
    GlobalPackages,
    PackageCategories,
    GlobalPackageFeatures,
)
from .enums import (
    PackageForEnum,
    PackageTypeEnum,
    OcsFlagEnum,
    StatusEnum,
    GlobalPackageFeatureStatusEnum,
)


class GlobalPackageFilter(django_filters.FilterSet):
    status = filters.CharFilter(method="filter_status")
    product_short_code = django_filters.CharFilter(
        method="filter_product_short_code"
    )
    package_category_code = django_filters.CharFilter(
        method="filter_package_category_code"
    )
    package_for = django_filters.CharFilter(method="filter_package_for")
    package_type = django_filters.CharFilter(method="filter_package_type")
    ocs_flag = django_filters.CharFilter(method="filter_ocs_flag")

    def filter_status(self, queryset, name, value):
        try:
            value = StatusEnum.get_value(value)
        except KeyError:
            pass
        return queryset.filter(status=value)

    def filter_product_short_code(self, queryset, name, value):
        product_short_codes = value.split(",")
        return queryset.filter(product__short_code__in=product_short_codes)

    def filter_package_category_code(self, queryset, name, value):
        return queryset.filter(package_category__code=value)

    def filter_package_for(self, queryset, name, value):
        try:
            value = PackageForEnum.get_value(value)
        except KeyError:
            pass
        return queryset.filter(package_for=value)

    def filter_package_type(self, queryset, name, value):
        try:
            value = PackageTypeEnum.get_value(value)
        except KeyError:
            pass
        return queryset.filter(package_type=value)

    def filter_ocs_flag(self, queryset, name, value):
        try:
            value = OcsFlagEnum.get_value(value)
        except KeyError:
            pass
        return queryset.filter(ocs_flag=value)

    class Meta:
        model = GlobalPackages
        fields = {
            "code": ["exact"],
            "rent_per_month": ["exact"],
            "package_category_id": ["exact"],
            "is_public": ["exact"],
        }


class HigherGlobalPackageFilter(django_filters.FilterSet):
    class Meta:
        model = GlobalPackages
        fields = {
            "rent_per_month": ["gt"],
            "product_id": ["exact"],
            "package_for": ["exact"],
            "package_category__id": ["exact"],
            "is_public": ["exact"],
            "status": ["exact"],
            "ocs_flag": ["exact"],
        }


class LowerGlobalPackageFilter(django_filters.FilterSet):
    class Meta:
        model = GlobalPackages
        fields = {
            "rent_per_month": ["lt"],
            "product_id": ["exact"],
            "package_for": ["exact"],
            "package_category__id": ["exact"],
            "is_public": ["exact"],
            "status": ["exact"],
            "ocs_flag": ["exact"],
        }


class PackageCategorieFilter(django_filters.FilterSet):
    product_short_code = django_filters.CharFilter(
        method="filter_product_short_code"
    )

    def filter_product_short_code(self, queryset, name, value):
        product_short_codes = value.split(",")
        return queryset.filter(product__short_code__in=product_short_codes)

    class Meta:
        model = PackageCategories
        fields = {
            "code": ["exact"],
        }


class GlobalPackageFeatureFilter(django_filters.FilterSet):
    resource_key = django_filters.CharFilter(method="filter_resource_key")
    status = django_filters.CharFilter(method="filter_status")

    def filter_resource_key(self, queryset, name, value):
        return queryset.filter(
            product_feature__resource_key__in=value.split(",")
        )

    def filter_status(self, queryset, name, value):
        value = GlobalPackageFeatureStatusEnum.get_value(value)
        return queryset.filter(status=value)

    class Meta:
        model = GlobalPackageFeatures
        fields = ["resource_key", "status"]
