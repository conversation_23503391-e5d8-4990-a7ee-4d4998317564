import logging
from rest_framework.response import Response
from rest_framework import generics
from rest_framework import filters
from django_filters import rest_framework as django_filters
from accounts.exceptions import BaseException
from rest_framework.exceptions import NotFound
from accounts.global_packages.models import (
    GlobalPackages,
    GlobalPackageFeatures,
    GlobalPackageFeatureRates,
    PackageCategories,
)
from accounts.global_packages.serializers import (
    GlobalPackageSerializer,
    GlobalPackageFeatureSerializer,
    GlobalPackageFeatureRateSerializer,
    PackageCategorySerializer,
)
from accounts.global_packages.filters import (
    GlobalPackageFilter,
    PackageCategorieFilter,
    GlobalPackageFeatureFilter,
)

logger = logging.getLogger(__name__)


class GlobalPackageListAPIView(generics.ListAPIView):
    serializer_class = GlobalPackageSerializer
    queryset = GlobalPackages.objects.all()
    filter_backends = [
        filters.OrderingFilter,
        django_filters.DjangoFilterBackend,
    ]
    filterset_class = GlobalPackageFilter
    ordering_fields = ["created", "rent_per_month"]

    def get(self, request, *args, **kwargs):
        try:
            return super().get(request, *args, **kwargs)
        except BaseException as e:
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class GlobalPackageRetrieveAPIView(generics.RetrieveAPIView):
    serializer_class = GlobalPackageSerializer

    def get_object(self):
        obj = generics.get_object_or_404(
            GlobalPackages.objects.all(), pk=self.kwargs.get("id")
        )
        return obj

    def get(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return Response(
                {
                    "status": "success",
                    "message": "Global Package Details",
                    "data": serializer.data,
                }
            )
        except BaseException as e:
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class GlobalPackageFeatureRetrieveAPIView(generics.RetrieveAPIView):
    serializer_class = GlobalPackageFeatureSerializer
    filter_backends = [
        filters.OrderingFilter,
        django_filters.DjangoFilterBackend,
    ]
    filterset_class = GlobalPackageFeatureFilter
    ordering_fields = ["created"]
    ordering = ["created"]

    def get_queryset(self):
        package_id = self.kwargs.get("id")
        if not GlobalPackages.objects.filter(id=package_id).exists():
            raise NotFound(detail="Global package not found")
        return GlobalPackageFeatures.objects.filter(package_id=package_id)

    def get(self, request, *args, **kwargs):
        try:
            queryset = self.filter_queryset(self.get_queryset())
            serializer = self.get_serializer(queryset, many=True)
            return Response(
                {
                    "status": "success",
                    "message": "Global Package Features",
                    "data": serializer.data,
                }
            )
        except BaseException as e:
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class GlobalPackageFeatureRateRetrieveAPIView(generics.RetrieveAPIView):
    serializer_class = GlobalPackageFeatureRateSerializer

    def get_queryset(self):
        feature_id = self.kwargs.get("feature_id")
        query = GlobalPackageFeatureRates.objects.filter(
            package_feature_id=feature_id
        )
        return query

    def get(self, request, *args, **kwargs):
        try:
            try:
                global_package = GlobalPackages.objects.filter(
                    id=self.kwargs.get("pk")
                )
            except global_package.DoesNotExist:
                raise NotFound(detail="Global package not found", code=404)
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)
            return Response(
                {"status": "success", "message": "", "data": serializer.data}
            )
        except BaseException as e:
            logger.title("Global Package feature Rate Detail Error").error(
                e, exc_info=True
            )
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class GlobalPackageCategoriesListAPIView(generics.ListAPIView):
    serializer_class = PackageCategorySerializer
    queryset = PackageCategories.objects.all()
    filter_backends = [
        filters.OrderingFilter,
        django_filters.DjangoFilterBackend,
    ]
    filterset_class = PackageCategorieFilter
    ordering_fields = ["created", "weightage"]

    def get(self, request, *args, **kwargs):
        try:
            return super().get(request, *args, **kwargs)
        except BaseException as e:
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )
