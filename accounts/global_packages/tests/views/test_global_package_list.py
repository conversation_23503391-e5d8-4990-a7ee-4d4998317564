from freezegun import freeze_time
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

from accounts.products.tests.factories import ProductFactory
from accounts.global_packages.tests.factories import (
    GlobalPackageFactory,
    PackageCategoryFactory,
)
from accounts.global_packages.models import GlobalPackages
from accounts.global_packages.enums import (
    PackageForEnum,
    PackageTypeEnum,
    OcsFlagEnum,
    StatusEnum as PackageStatusEnum,
)


class TestGlobalPackageListView(APITestCase):

    def test_list_all_global_packages(self):
        global_packages = GlobalPackageFactory.create_batch(size=5)
        url = reverse("global_packages:listing")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_json = response.json()
        self.assertEqual(response_json["pagination"]["count"], 5)
        self.assertEqual(response_json["code"], 200)
        self.assertEqual(
            response_json["data"][0],
            {
                "id": str(global_packages[0].id),
                "product_id": str(global_packages[0].product_id),
                "package_category_id": global_packages[0].package_category_id,
                "name": global_packages[0].name,
                "code": global_packages[0].code,
                "description": global_packages[0].description,
                "package_type": global_packages[0].package_type,
                "rent_per_month": str(global_packages[0].rent_per_month),
                "renew_cycle": global_packages[0].renew_cycle,
                "is_public": True,
                "ocs_flag": OcsFlagEnum.get_name(
                    global_packages[0].ocs_flag
                ).lower(),
                "package_for": PackageForEnum.get_name(
                    global_packages[0].package_for
                ).lower(),
                "package_number": global_packages[0].package_number,
                "discount_id": None,
                "status": PackageStatusEnum.ACTIVE.name.lower(),
                "created": global_packages[0].created.strftime(
                    "%Y-%m-%dT%H:%M:%SZ"
                ),
                "modified": global_packages[0].modified.strftime(
                    "%Y-%m-%dT%H:%M:%SZ"
                ),
            },
        )

    def test_filter_by_product_short_code(self):
        product_myopin = ProductFactory.create(short_code="myopin")
        product_myopus = ProductFactory.create(short_code="myopus")
        product_heyoin = ProductFactory.create(short_code="heyoin")
        GlobalPackageFactory.create_batch(size=2, product=product_myopin)
        GlobalPackageFactory.create_batch(size=2, product=product_myopus)
        GlobalPackageFactory.create_batch(size=2, product=product_heyoin)

        url = reverse("global_packages:listing")
        for product in [product_myopin, product_myopus, product_heyoin]:
            response = self.client.get(
                url, data={"product_short_code": product.short_code}
            )
            self.assertEqual(
                response.json()["pagination"]["count"],
                GlobalPackages.objects.filter(product=product).count(),
            )
            for gp in response.json()["data"]:
                self.assertEqual(gp["product_id"], str(product.id))

        # Validate Comma Seperated Filter
        response = self.client.get(
            url,
            data={
                "product_short_code": f"{product_myopin.short_code},{product_myopus.short_code}"
            },
        )
        self.assertEqual(
            response.json()["pagination"]["count"],
            GlobalPackages.objects.filter(
                product__in=[product_myopin, product_myopus]
            ).count(),
        )

    def test_filter_by_package_category_id(self):
        package_category_1 = PackageCategoryFactory.create()
        package_category_2 = PackageCategoryFactory.create()
        GlobalPackageFactory.create_batch(
            size=2, package_category=package_category_1
        )
        GlobalPackageFactory.create_batch(
            size=2, package_category=package_category_2
        )

        url = reverse("global_packages:listing")
        for package_category in [package_category_1, package_category_2]:
            response = self.client.get(
                url, data={"package_category_id": package_category.id}
            )
            self.assertEqual(
                response.json()["pagination"]["count"],
                GlobalPackages.objects.filter(
                    package_category=package_category
                ).count(),
            )
            for gp in response.json()["data"]:
                self.assertEqual(gp["package_category_id"], package_category.id)

    def test_filter_by_is_public(self):
        GlobalPackageFactory.create_batch(size=2, is_public=True)
        GlobalPackageFactory.create_batch(size=4, is_public=False)

        url = reverse("global_packages:listing")
        for is_public in [True, False]:
            response = self.client.get(url, data={"is_public": is_public})
            self.assertEqual(
                response.json()["pagination"]["count"],
                GlobalPackages.objects.filter(is_public=is_public).count(),
            )
            for gp in response.json()["data"]:
                self.assertEqual(gp["is_public"], is_public)
                self.assertEqual(type(gp["is_public"]), bool)

    def test_filter_by_package_for(self):
        GlobalPackageFactory.create(package_for=PackageForEnum.TOLLFREE.value)
        GlobalPackageFactory.create(
            package_for=PackageForEnum.MOBILE_TRACKING.value
        )
        GlobalPackageFactory.create(
            package_for=PackageForEnum.VIRTUAL_NUMBER.value
        )
        GlobalPackageFactory.create(package_for=PackageForEnum.HEYO.value)

        url = reverse("global_packages:listing")
        for package_for in [
            PackageForEnum.TOLLFREE,
            PackageForEnum.MOBILE_TRACKING,
            PackageForEnum.VIRTUAL_NUMBER,
            PackageForEnum.HEYO,
        ]:
            response = self.client.get(
                url, data={"package_for": package_for.name.lower()}
            )
            self.assertEqual(
                response.json()["pagination"]["count"],
                GlobalPackages.objects.filter(
                    package_for=package_for.value
                ).count(),
            )
            for gp in response.json()["data"]:
                self.assertEqual(gp["package_for"], package_for.name.lower())

    def test_filter_by_package_type(self):
        GlobalPackageFactory.create(package_type=PackageTypeEnum.MAIN.value)
        GlobalPackageFactory.create(package_type=PackageTypeEnum.ADDON.value)

        url = reverse("global_packages:listing")
        for package_type in [PackageTypeEnum.MAIN, PackageTypeEnum.ADDON]:
            response = self.client.get(
                url, data={"package_type": package_type.name.lower()}
            )
            self.assertEqual(
                response.json()["pagination"]["count"],
                GlobalPackages.objects.filter(
                    package_type=package_type.value
                ).count(),
            )
            for gp in response.json()["data"]:
                self.assertEqual(gp["package_type"], package_type.name.lower())

    def test_filter_by_ocs_flag(self):
        url = reverse("global_packages:listing")
        for ocs_flag in OcsFlagEnum.values():
            GlobalPackageFactory.create(ocs_flag=ocs_flag)
            response = self.client.get(
                url, data={"ocs_flag": OcsFlagEnum.get_name(ocs_flag).lower()}
            )
            self.assertEqual(
                response.json()["pagination"]["count"],
                GlobalPackages.objects.filter(ocs_flag=ocs_flag).count(),
            )
            for gp in response.json()["data"]:
                self.assertEqual(
                    gp["ocs_flag"], OcsFlagEnum.get_name(ocs_flag).lower()
                )

    def test_filter_by_code(self):
        GlobalPackageFactory.create(code="111")
        GlobalPackageFactory.create(code="222")

        url = reverse("global_packages:listing")
        for code in ["111", "222"]:
            response = self.client.get(url, data={"code": code})
            self.assertEqual(
                response.json()["pagination"]["count"],
                GlobalPackages.objects.filter(code=code).count(),
            )
            for gp in response.json()["data"]:
                self.assertEqual(gp["code"], code)

    def test_filter_by_rent_per_month(self):
        GlobalPackageFactory.create(rent_per_month="199")
        GlobalPackageFactory.create(rent_per_month="299")

        url = reverse("global_packages:listing")
        for rent_per_month in ["199", "299"]:
            response = self.client.get(url, data={"rent_per_month": rent_per_month})
            self.assertEqual(
                response.json()["pagination"]["count"],
                GlobalPackages.objects.filter(rent_per_month=rent_per_month).count(),
            )

    def test_filter_by_status(self):
        url = reverse("global_packages:listing")
        for status in PackageStatusEnum.values():  # noqa: F402
            GlobalPackageFactory.create(status=status)
            response = self.client.get(
                url, data={"status": PackageStatusEnum.get_name(status).lower()}
            )
            self.assertEqual(
                response.json()["pagination"]["count"],
                GlobalPackages.objects.filter(status=status).count(),
            )
            for gp in response.json()["data"]:
                self.assertEqual(
                    gp["status"], PackageStatusEnum.get_name(status).lower()
                )

    def test_default_ordering_by_created_asc(self):
        with freeze_time("2023-04-01 18:30:00"):
            global_package_1 = GlobalPackageFactory.create(code="111")
        with freeze_time("2023-04-02 18:30:00"):
            global_package_2 = GlobalPackageFactory.create(code="222")

        url = reverse("global_packages:listing")
        response = self.client.get(url)
        response_json = response.json()

        self.assertEqual(
            response_json["data"][0]["id"], str(global_package_1.id)
        )
        self.assertEqual(
            response_json["data"][1]["id"], str(global_package_2.id)
        )

    def test_ordering_by_created_desc(self):
        with freeze_time("2023-04-01 18:30:00"):
            global_package_1 = GlobalPackageFactory.create(code="111")
        with freeze_time("2023-04-02 18:30:00"):
            global_package_2 = GlobalPackageFactory.create(code="222")

        url = reverse("global_packages:listing")
        response = self.client.get(url, data={"ordering": "-created"})
        response_json = response.json()

        self.assertEqual(
            response_json["data"][0]["id"], str(global_package_2.id)
        )
        self.assertEqual(
            response_json["data"][1]["id"], str(global_package_1.id)
        )

    def test_ordering_by_created_asc(self):
        with freeze_time("2023-04-01 18:30:00"):
            global_package_1 = GlobalPackageFactory.create(code="111")
        with freeze_time("2023-04-02 18:30:00"):
            global_package_2 = GlobalPackageFactory.create(code="222")

        url = reverse("global_packages:listing")
        response = self.client.get(url, data={"ordering": "created"})
        response_json = response.json()

        self.assertEqual(
            response_json["data"][0]["id"], str(global_package_1.id)
        )
        self.assertEqual(
            response_json["data"][1]["id"], str(global_package_2.id)
        )

    def test_ordering_by_rent_per_month_asc(self):
        global_package_1 = GlobalPackageFactory.create(rent_per_month="199")
        global_package_2 = GlobalPackageFactory.create(rent_per_month="299")

        url = reverse("global_packages:listing")
        response = self.client.get(url, data={"ordering": "rent_per_month"})
        response_json = response.json()

        self.assertEqual(
            response_json["data"][0]["id"], str(global_package_1.id)
        )
        self.assertEqual(
            response_json["data"][1]["id"], str(global_package_2.id)
        )

    def test_ordering_by_rent_per_month_desc(self):
        global_package_1 = GlobalPackageFactory.create(rent_per_month="199")
        global_package_2 = GlobalPackageFactory.create(rent_per_month="299")

        url = reverse("global_packages:listing")
        response = self.client.get(url, data={"ordering": "-rent_per_month"})
        response_json = response.json()

        self.assertEqual(
            response_json["data"][0]["id"], str(global_package_2.id)
        )
        self.assertEqual(
            response_json["data"][1]["id"], str(global_package_1.id)
        )

    def test_with_fields(self):
        global_packages = GlobalPackageFactory.create_batch(size=5)
        url = reverse("global_packages:listing")
        response = self.client.get(url, data={"fields": "id,name,code"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_json = response.json()
        self.assertEqual(response_json["pagination"]["count"], 5)
        self.assertEqual(response_json["code"], 200)
        self.assertEqual(
            response_json["data"][0],
            {
                "id": str(global_packages[0].id),
                "name": global_packages[0].name,
                "code": global_packages[0].code,
            },
        )


class TestGlobalPackageDetailView(APITestCase):

    def test_fetch_global_package_detail(self):
        # Created multiple packages to validate that api return the correct package
        GlobalPackageFactory.create_batch(size=5)
        global_package = GlobalPackageFactory.create()
        url = reverse("global_packages:retrieve", kwargs={"id": global_package.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_json = response.json()
        self.assertEqual(response_json["code"], 200)
        self.assertEqual(response_json["message"], "Global Package Details")
        self.assertEqual(
            response_json["data"],
            {
                "id": str(global_package.id),
                "product_id": str(global_package.product_id),
                "package_category_id": global_package.package_category_id,
                "name": global_package.name,
                "code": global_package.code,
                "description": global_package.description,
                "package_type": global_package.package_type,
                "rent_per_month": str(global_package.rent_per_month),
                "renew_cycle": global_package.renew_cycle,
                "is_public": True,
                "ocs_flag": OcsFlagEnum.get_name(
                    global_package.ocs_flag
                ).lower(),
                "package_for": PackageForEnum.get_name(
                    global_package.package_for
                ).lower(),
                "package_number": global_package.package_number,
                "discount_id": None,
                "status": PackageStatusEnum.ACTIVE.name.lower(),
                "created": global_package.created.strftime(
                    "%Y-%m-%dT%H:%M:%SZ"
                ),
                "modified": global_package.modified.strftime(
                    "%Y-%m-%dT%H:%M:%SZ"
                ),
            },
        )
    
    def test_fetch_global_package_detail_404(self):
        url = reverse("global_packages:retrieve", kwargs={"id": "123abc"})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        response_json = response.json()
        self.assertEqual(response_json["code"], "not_found")
        self.assertEqual(response_json["message"], "Not found.")

    def test_fetch_global_package_detail_with_fields(self):
        global_package = GlobalPackageFactory.create()
        url = reverse("global_packages:retrieve", kwargs={"id": global_package.id})
        response = self.client.get(url, data={"fields": "id,name,code,rent_per_month"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_json = response.json()
        self.assertEqual(response_json["code"], 200)
        self.assertEqual(response_json["message"], "Global Package Details")
        self.assertEqual(
            response_json["data"],
            {
                "id": str(global_package.id),
                "name": global_package.name,
                "code": global_package.code,
                "rent_per_month": str(global_package.rent_per_month),
            },
        )
