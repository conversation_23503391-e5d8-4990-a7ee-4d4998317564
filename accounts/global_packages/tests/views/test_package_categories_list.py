from freezegun import freeze_time
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

from accounts.products.tests.factories import ProductFactory
from accounts.global_packages.tests.factories import PackageCategoryFactory
from accounts.global_packages.models import PackageCategories


class TestPackageCategoriesView(APITestCase):

    def test_list_all_package_categories(self):
        package_categories = PackageCategoryFactory.create_batch(size=5)
        url = reverse("global_packages:categories")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_json = response.json()
        self.assertEqual(response_json["pagination"]["count"], 5)
        self.assertEqual(response_json["code"], 200)
        self.assertEqual(
            response_json["data"][0],
            {
                "id": package_categories[0].id,
                "product_id": str(package_categories[0].product_id),
                "name": package_categories[0].name,
                "code": package_categories[0].code,
                "description": package_categories[0].description,
                "weightage": package_categories[0].weightage,
                "created": package_categories[0].created.strftime(
                    "%Y-%m-%dT%H:%M:%SZ"
                ),
                "modified": package_categories[0].modified.strftime(
                    "%Y-%m-%dT%H:%M:%SZ"
                ),
            },
        )

    def test_filter_by_product_short_code(self):
        product_myopin = ProductFactory.create(short_code="myopin")
        product_myopus = ProductFactory.create(short_code="myopus")
        product_heyoin = ProductFactory.create(short_code="heyoin")
        PackageCategoryFactory.create_batch(size=2, product=product_myopin)
        PackageCategoryFactory.create_batch(size=2, product=product_myopus)
        PackageCategoryFactory.create_batch(size=2, product=product_heyoin)

        url = reverse("global_packages:categories")
        for product in [product_myopin, product_myopus, product_heyoin]:
            response = self.client.get(
                url, data={"product_short_code": product.short_code}
            )
            self.assertEqual(
                response.json()["pagination"]["count"],
                PackageCategories.objects.filter(product=product).count(),
            )
            for gp in response.json()["data"]:
                self.assertEqual(gp["product_id"], str(product.id))

        # Validate Comma Seperated Filter
        response = self.client.get(
            url,
            data={
                "product_short_code": f"{product_myopin.short_code},{product_myopus.short_code}"
            },
        )
        self.assertEqual(
            response.json()["pagination"]["count"],
            PackageCategories.objects.filter(
                product__in=[product_myopin, product_myopus]
            ).count(),
        )

    def test_filter_by_code(self):
        package_category_1 = PackageCategoryFactory.create(code="L")
        package_category_2 = PackageCategoryFactory.create(code="D")
        
        url = reverse("global_packages:categories")
        for package_category in [package_category_1, package_category_2]:
            response = self.client.get(
                url, data={"code": package_category.code}
            )
            self.assertEqual(
                response.json()["pagination"]["count"],
                PackageCategories.objects.filter(
                    code=package_category.code
                ).count(),
            )
            for gp in response.json()["data"]:
                self.assertEqual(gp["code"], package_category.code)

    def test_default_ordering_by_created_asc(self):
        with freeze_time("2023-04-01 18:30:00"):
            pacakge_category_1 = PackageCategoryFactory.create(code="L")
        with freeze_time("2023-04-02 18:30:00"):
            pacakge_category_2 = PackageCategoryFactory.create(code="D")

        url = reverse("global_packages:categories")
        response = self.client.get(url)
        response_json = response.json()

        self.assertEqual(
            response_json["data"][0]["id"], pacakge_category_1.id
        )
        self.assertEqual(
            response_json["data"][1]["id"], pacakge_category_2.id
        )

    def test_ordering_by_created_desc(self):
        with freeze_time("2023-04-01 18:30:00"):
            pacakge_category_1 = PackageCategoryFactory.create(code="L")
        with freeze_time("2023-04-02 18:30:00"):
            pacakge_category_2 = PackageCategoryFactory.create(code="D")

        url = reverse("global_packages:categories")
        response = self.client.get(url, data={"ordering": "-created"})
        response_json = response.json()

        self.assertEqual(
            response_json["data"][0]["id"], pacakge_category_2.id
        )
        self.assertEqual(
            response_json["data"][1]["id"], pacakge_category_1.id
        )

    def test_ordering_by_created_asc(self):
        with freeze_time("2023-04-01 18:30:00"):
            pacakge_category_1 = PackageCategoryFactory.create(code="L")
        with freeze_time("2023-04-02 18:30:00"):
            pacakge_category_2 = PackageCategoryFactory.create(code="D")

        url = reverse("global_packages:categories")
        response = self.client.get(url, data={"ordering": "created"})
        response_json = response.json()

        self.assertEqual(
            response_json["data"][0]["id"], pacakge_category_1.id
        )
        self.assertEqual(
            response_json["data"][1]["id"], pacakge_category_2.id
        )

    def test_ordering_by_weightage_asc(self):
        pacakge_category_1 = PackageCategoryFactory.create(weightage=1)
        pacakge_category_2 = PackageCategoryFactory.create(weightage=2)

        url = reverse("global_packages:categories")
        response = self.client.get(url, data={"ordering": "weightage"})
        response_json = response.json()

        self.assertEqual(
            response_json["data"][0]["id"], pacakge_category_1.id
        )
        self.assertEqual(
            response_json["data"][1]["id"], pacakge_category_2.id
        )

    def test_ordering_by_weightage_desc(self):
        pacakge_category_1 = PackageCategoryFactory.create(weightage=1)
        pacakge_category_2 = PackageCategoryFactory.create(weightage=2)

        url = reverse("global_packages:categories")
        response = self.client.get(url, data={"ordering": "-weightage"})
        response_json = response.json()

        self.assertEqual(
            response_json["data"][0]["id"], pacakge_category_2.id
        )
        self.assertEqual(
            response_json["data"][1]["id"], pacakge_category_1.id
        )

    def test_with_fields(self):
        pacakge_categories = PackageCategoryFactory.create_batch(size=5)
        url = reverse("global_packages:categories")
        response = self.client.get(url, data={"fields": "id,name,code"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_json = response.json()
        self.assertEqual(response_json["pagination"]["count"], 5)
        self.assertEqual(response_json["code"], 200)
        self.assertEqual(
            response_json["data"][0],
            {
                "id": pacakge_categories[0].id,
                "name": pacakge_categories[0].name,
                "code": pacakge_categories[0].code,
            },
        )
