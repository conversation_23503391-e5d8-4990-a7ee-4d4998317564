from freezegun import freeze_time
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

from accounts.products.tests.factories import (
    ProductFactory,
    ProductFeatureFactory,
)
from accounts.global_packages.tests.factories import (
    GlobalPackageFactory,
    PackageCategoryFactory,
    GlobalPackageFeatureFactory,
    GlobalPackageFeatureRatesFactory,
)
from accounts.global_packages.models import (
    GlobalPackages,
    GlobalPackageFeatures,
)
from accounts.global_packages.enums import (
    PackageForEnum,
    PackageTypeEnum,
    OcsFlagEnum,
    StatusEnum as PackageStatusEnum,
    GlobalPackageFeatureStatusEnum,
)
from accounts.products.enums import ProductFeatureStatusEnum


class TestGlobalPackageFeatures(APITestCase):
    def setUp(self):
        self.product = ProductFactory.create()
        self.product_features = ProductFeatureFactory.create_batch(
            5, product=self.product
        )
        self.global_package = GlobalPackageFactory.create()
        for pf in self.product_features:
            GlobalPackageFeatureFactory.create(
                package=self.global_package, product_feature=pf
            )

    def test_fetch_package_features(self):
        url = reverse(
            "global_packages:features", kwargs={"id": self.global_package.id}
        )
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_json = response.json()
        self.assertEqual(response_json["code"], 200)
        self.assertEqual(response_json["message"], "Global Package Features")
        self.assertNotIn("pagination", response_json)
        package_features = GlobalPackageFeatures.objects.order_by("created")
        package_feature_resonse = []
        # Validate that default order of data in created ASC
        for pf in package_features:
            package_feature_resonse.append(
                {
                    "id": str(pf.id),
                    "product_feature_id": pf.product_feature_id,
                    "product_feature_property_id": pf.product_feature_property_id,
                    "free_unit": pf.free_unit,
                    "rent_per_month": str(pf.rent_per_month),
                    "status": GlobalPackageFeatureStatusEnum.ENABLED.name.lower(),
                    "last_disabled_date": None,
                    "resource_key": pf.get_resource_key(),
                    "memcache_key": pf.get_memcache_key(),
                    "created": pf.created.strftime("%Y-%m-%dT%H:%M:%SZ"),
                    "modified": pf.modified.strftime("%Y-%m-%dT%H:%M:%SZ"),
                }
            )
        self.assertEqual(response_json["data"], package_feature_resonse)

    def test_filter_by_resource_key(self):
        gpf_1 = GlobalPackageFeatureFactory.create(
            package=self.global_package,
            product_feature=ProductFeatureFactory(
                product=self.product, resource_key="incoming"
            ),
        )
        gpf_2 = GlobalPackageFeatureFactory.create(
            package=self.global_package,
            product_feature=ProductFeatureFactory(
                product=self.product, resource_key="outgoing"
            ),
        )

        url = reverse(
            "global_packages:features", kwargs={"id": self.global_package.id}
        )
        response = self.client.get(
            url, data={"resource_key": "incoming,outgoing"}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_json = response.json()
        self.assertEqual(response_json["code"], 200)
        self.assertEqual(response_json["message"], "Global Package Features")
        self.assertNotIn("pagination", response_json)
        self.assertEqual(len(response_json["data"]), 2)
        pf_ids = [pf["id"] for pf in response_json["data"]]
        self.assertEqual(pf_ids, [str(gpf_1.id), str(gpf_2.id)])

    def test_filter_by_status_enabled(self):
        gpf_1 = GlobalPackageFeatureFactory.create(
            package=self.global_package,
            status=GlobalPackageFeatureStatusEnum.DISABLED.value,
        )
        gpf_2 = GlobalPackageFeatureFactory.create(
            package=self.global_package,
            status=GlobalPackageFeatureStatusEnum.DELETED.value,
        )

        url = reverse(
            "global_packages:features", kwargs={"id": self.global_package.id}
        )
        response = self.client.get(url, data={"status": "enabled"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_json = response.json()
        self.assertEqual(response_json["message"], "Global Package Features")
        self.assertNotIn("pagination", response_json)
        self.assertEqual(len(response_json["data"]), 5)
        pf_ids = [pf["id"] for pf in response_json["data"]]
        self.assertNotIn(str(gpf_1.id), pf_ids)
        self.assertNotIn(str(gpf_2.id), pf_ids)

    def test_filter_by_status_disabled(self):
        gpf_1 = GlobalPackageFeatureFactory.create(
            package=self.global_package,
            status=GlobalPackageFeatureStatusEnum.DISABLED.value,
        )
        GlobalPackageFeatureFactory.create(
            package=self.global_package,
            status=GlobalPackageFeatureStatusEnum.DELETED.value,
        )

        url = reverse(
            "global_packages:features", kwargs={"id": self.global_package.id}
        )
        response = self.client.get(url, data={"status": "disabled"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_json = response.json()
        self.assertEqual(response_json["message"], "Global Package Features")
        self.assertNotIn("pagination", response_json)
        self.assertEqual(len(response_json["data"]), 1)
        pf_ids = [pf["id"] for pf in response_json["data"]]
        self.assertEqual([str(gpf_1.id)], pf_ids)

    def test_filter_by_status_deleted(self):
        GlobalPackageFeatureFactory.create(
            package=self.global_package,
            status=GlobalPackageFeatureStatusEnum.DISABLED.value,
        )
        gpf_2 = GlobalPackageFeatureFactory.create(
            package=self.global_package,
            status=GlobalPackageFeatureStatusEnum.DELETED.value,
        )

        url = reverse(
            "global_packages:features", kwargs={"id": self.global_package.id}
        )
        response = self.client.get(url, data={"status": "deleted"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_json = response.json()
        self.assertEqual(response_json["message"], "Global Package Features")
        self.assertNotIn("pagination", response_json)
        self.assertEqual(len(response_json["data"]), 1)
        pf_ids = [pf["id"] for pf in response_json["data"]]
        self.assertEqual([str(gpf_2.id)], pf_ids)

    def test_fetch_package_features_with_expand_product_feature(self):
        url = reverse(
            "global_packages:features", kwargs={"id": self.global_package.id}
        )
        response = self.client.get(url, data={"expand": "product_feature"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_json = response.json()
        self.assertEqual(response_json["code"], 200)
        self.assertEqual(response_json["message"], "Global Package Features")
        self.assertNotIn("pagination", response_json)
        package_features = GlobalPackageFeatures.objects.order_by("created")
        package_feature_resonse = []
        # Validate that default order of data in created ASC
        for pf in package_features:
            package_feature_resonse.append(
                {
                    "id": str(pf.id),
                    "product_feature_id": pf.product_feature_id,
                    "product_feature_property_id": pf.product_feature_property_id,
                    "free_unit": pf.free_unit,
                    "rent_per_month": str(pf.rent_per_month),
                    "status": GlobalPackageFeatureStatusEnum.ENABLED.name.lower(),
                    "last_disabled_date": None,
                    "resource_key": pf.get_resource_key(),
                    "memcache_key": pf.get_memcache_key(),
                    "created": pf.created.strftime("%Y-%m-%dT%H:%M:%SZ"),
                    "modified": pf.modified.strftime("%Y-%m-%dT%H:%M:%SZ"),
                    "product_feature": {
                        "id": str(pf.product_feature.id),
                        "product_id": str(pf.product_feature.product_id),
                        "name": pf.product_feature.name,
                        "unit": pf.product_feature.unit,
                        "billing_type": pf.product_feature.billing_type,
                        "resource_key": pf.product_feature.resource_key,
                        "memcache_key": pf.product_feature.memcache_key,
                        "is_highlighted": pf.product_feature.is_highlighted,
                        "type": pf.product_feature.type,
                        "status": ProductFeatureStatusEnum.ACTIVE.name.lower(),
                        "created": pf.product_feature.created.strftime(
                            "%Y-%m-%dT%H:%M:%SZ"
                        ),
                    },
                }
            )
        self.assertEqual(response_json["data"], package_feature_resonse)

    def test_fetch_package_features_with_expand_rate_slabs(self):
        # create rate slabs for 2 features only, others should return blank
        product_feature_1 = ProductFeatureFactory.create(product=self.product)

        global_package = GlobalPackageFactory.create()

        package_feature_1 = GlobalPackageFeatureFactory.create(
            package=global_package, product_feature=product_feature_1
        )
        pfrs_1 = GlobalPackageFeatureRatesFactory.create(
            package_feature=package_feature_1, min=1, max=100
        )
        pfrs_2 = GlobalPackageFeatureRatesFactory.create(
            package_feature=package_feature_1, min=101, max=200
        )

        url = reverse(
            "global_packages:features", kwargs={"id": global_package.id}
        )
        response = self.client.get(url, data={"expand": "rate_slabs"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_json = response.json()
        self.assertEqual(response_json["code"], 200)
        self.assertEqual(response_json["message"], "Global Package Features")
        self.assertNotIn("pagination", response_json)
        package_feature_resonse = [
            {
                "id": str(package_feature_1.id),
                "product_feature_id": package_feature_1.product_feature_id,
                "product_feature_property_id": package_feature_1.product_feature_property_id,
                "free_unit": package_feature_1.free_unit,
                "rent_per_month": str(package_feature_1.rent_per_month),
                "status": GlobalPackageFeatureStatusEnum.ENABLED.name.lower(),
                "last_disabled_date": None,
                "resource_key": package_feature_1.get_resource_key(),
                "memcache_key": package_feature_1.get_memcache_key(),
                "created": package_feature_1.created.strftime(
                    "%Y-%m-%dT%H:%M:%SZ"
                ),
                "modified": package_feature_1.modified.strftime(
                    "%Y-%m-%dT%H:%M:%SZ"
                ),
                "rate_slabs": [
                    {
                        "id": str(pfrs_1.id),
                        "package_feature_id": str(pfrs_1.package_feature_id),
                        "min": pfrs_1.min,
                        "max": pfrs_1.max,
                        "rate": str(pfrs_1.rate),
                        "status": "active",
                        "created": pfrs_1.created.strftime(
                            "%Y-%m-%dT%H:%M:%SZ"
                        ),
                        "modified": pfrs_1.modified.strftime(
                            "%Y-%m-%dT%H:%M:%SZ"
                        ),
                    },
                    {
                        "id": str(pfrs_2.id),
                        "package_feature_id": str(pfrs_2.package_feature_id),
                        "min": pfrs_2.min,
                        "max": pfrs_2.max,
                        "rate": str(pfrs_2.rate),
                        "status": "active",
                        "created": pfrs_2.created.strftime(
                            "%Y-%m-%dT%H:%M:%SZ"
                        ),
                        "modified": pfrs_2.modified.strftime(
                            "%Y-%m-%dT%H:%M:%SZ"
                        ),
                    },
                ],
            }
        ]
        self.assertEqual(response_json["data"], package_feature_resonse)

    def test_fetch_package_features_with_fields(self):
        url = reverse(
            "global_packages:features", kwargs={"id": self.global_package.id}
        )
        response = self.client.get(
            url, data={"fields": "id,free_unit,rent_per_month,status"}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_json = response.json()
        self.assertEqual(response_json["code"], 200)
        self.assertEqual(response_json["message"], "Global Package Features")
        self.assertNotIn("pagination", response_json)
        package_features = GlobalPackageFeatures.objects.order_by("created")
        package_feature_resonse = []
        for pf in package_features:
            package_feature_resonse.append(
                {
                    "id": str(pf.id),
                    "free_unit": pf.free_unit,
                    "rent_per_month": str(pf.rent_per_month),
                    "status": GlobalPackageFeatureStatusEnum.ENABLED.name.lower(),
                }
            )
        self.assertEqual(response_json["data"], package_feature_resonse)
