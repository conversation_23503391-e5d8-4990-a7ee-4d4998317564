from django.test import TestCase
from accounts.global_packages.tests.factories import (
    GlobalPackageFeatureRatesFactory,
    GlobalPackageFeatureFactory,
)
from accounts.global_packages.utils.global_package_feature_rates import (
    get_active_global_package_feature_rate,
)


class TestGlobalPackageFeatureRates(TestCase):
    def test_get_active_global_package_feature_rate(self):
        global_package_feature = GlobalPackageFeatureFactory.create()
        global_package_feature_rate = GlobalPackageFeatureRatesFactory(
            package_feature=global_package_feature
        )
        result = get_active_global_package_feature_rate(
            global_package_feature.id
        )
        assert result.id == global_package_feature_rate.id
        assert result.package_feature_id == global_package_feature.id
