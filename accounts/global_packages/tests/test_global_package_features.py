from django.test import TestCase
from accounts.global_packages.tests.factories import (
    GlobalPackageFeatureFactory,
    GlobalPackageFactory,
)
from accounts.products.tests.factories import (
    ProductFeaturePropertyFactory,
    ProductFeatureFactory,
)
from accounts.global_packages.utils.global_package_features import (
    get_active_global_package_feature,
)


class TestPackageFeature(TestCase):
    def setUp(self):
        self.product_feature = ProductFeatureFactory.create()
        self.product_feature_property = ProductFeaturePropertyFactory.create()
        self.global_package = GlobalPackageFactory.create()

    def test_get_active_global_package_feature_success(self):
        global_package_feature = GlobalPackageFeatureFactory.create(
            package=self.global_package,
            product_feature_id=self.product_feature.id,
            product_feature_property_id=self.product_feature_property.id,
        )
        result = get_active_global_package_feature(
            self.global_package.id,
            self.product_feature.id,
            self.product_feature_property.id,
        )

        assert result.id == global_package_feature.id
        assert result.package_id == global_package_feature.package_id
        assert (
            result.product_feature_id
            == global_package_feature.product_feature_id
        )
        assert (
            result.product_feature_property_id
            == global_package_feature.product_feature_property_id
        )
        assert result.status == 1

    def test_get_active_global_package_feature_without_property_id(self):
        global_package_feature = GlobalPackageFeatureFactory.create(
            package=self.global_package,
            product_feature_id=self.product_feature.id,
            product_feature_property_id=self.product_feature_property.id,
        )
        result = get_active_global_package_feature(
            self.global_package.id,
            self.product_feature.id,
        )

        assert result.id == global_package_feature.id
        assert result.package_id == global_package_feature.package_id
        assert (
            result.product_feature_id
            == global_package_feature.product_feature_id
        )
        assert (
            result.product_feature_property_id
            == global_package_feature.product_feature_property_id
        )
        assert result.status == 1
