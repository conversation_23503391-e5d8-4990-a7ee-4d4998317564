from factory import Faker, SubFactory
from factory.django import DjangoModelFactory

from accounts.discounts.tests.factories import DiscountFactory
from accounts.global_packages.models import (
    PackageCategories,
    GlobalPackageFeatureRates,
    GlobalPackageFeatures,
    GlobalPackages,
)
from accounts.products.tests.factories import (
    ProductFactory,
    ProductFeatureFactory,
    ProductFeaturePropertyFactory,
)
from accounts.global_packages import constants
from django.utils import timezone as tz
from accounts.global_packages.enums import PackageForEnum, PackageTypeEnum


class PackageCategoryFactory(DjangoModelFactory):

    product = SubFactory(ProductFactory)
    code = "L"
    name = Faker("word")
    proposal_file = Faker("file_name")
    description = Faker("text")
    weightage = 1

    class Meta:
        model = PackageCategories


class GlobalPackageFactory(DjangoModelFactory):
    class Meta:
        model = GlobalPackages

    id = Faker("uuid4")
    product = SubFactory(ProductFactory)
    package_category = SubFactory(PackageCategoryFactory)
    name = Faker("word")
    package_type = PackageTypeEnum.MAIN.value
    rent_per_month = Faker(
        "pydecimal", left_digits=4, right_digits=3, positive=True
    )
    renew_cycle = Faker("random_int", min=1, max=10)
    code = Faker("pystr", max_chars=8)
    ocs_flag = Faker("random_element", elements=[0, 1, 2, 3])
    package_for = Faker(
        "random_element",
        elements=[
            PackageForEnum.VIRTUAL_NUMBER.value,
            PackageForEnum.TOLLFREE.value,
            PackageForEnum.MOBILE_TRACKING.value,
        ],
    )
    package_number = Faker("pystr", max_chars=4)
    discount = None


class GlobalPackageFeatureFactory(DjangoModelFactory):
    class Meta:
        model = GlobalPackageFeatures

    id = Faker("uuid4")
    package = SubFactory(GlobalPackageFactory)
    product_feature = SubFactory(ProductFeatureFactory)
    product_feature_property = SubFactory(ProductFeaturePropertyFactory)
    free_unit = Faker("random_int", min=1, max=10)
    rent_per_month = Faker(
        "pydecimal", left_digits=4, right_digits=3, positive=True
    )
    last_disabled_date = None


class GlobalPackageFeatureRatesFactory(DjangoModelFactory):

    package_feature = SubFactory(GlobalPackageFeatureFactory)
    min = Faker("random_int", min=1, max=100)
    max = Faker("random_int", min=101, max=200)
    rate = Faker("pydecimal", left_digits=4, right_digits=3, positive=True)
    status = 1

    class Meta:
        model = GlobalPackageFeatureRates
