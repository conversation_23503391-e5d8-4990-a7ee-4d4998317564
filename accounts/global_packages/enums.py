from accounts.enums import BaseEnum


class PackageForEnum(BaseEnum):
    VIRTUAL_NUMBER = "vn"
    TOLLFREE = "tn"
    MOBILE_TRACKING = "mt"
    HEYO = "hn"


class PackageTypeEnum(BaseEnum):
    MAIN = "main"
    ADDON = "addon"


class OcsFlagEnum(BaseEnum):
    ACCOUNT = 0
    OCS = 1
    MYOPERATOR_ACCOUNT = 2
    NONE = 3


class StatusEnum(BaseEnum):
    INACTIVE = 0
    ACTIVE = 1


class GlobalPackageFeatureStatusEnum(BaseEnum):
    DISABLED = 0
    ENABLED = 1
    DELETED = 2


class PackageFeatureRateSlabStatusEnum(BaseEnum):
    INACTIVE = 0
    ACTIVE = 1
