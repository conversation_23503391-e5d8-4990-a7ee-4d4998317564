from django.urls import path
from accounts.global_packages.views import (
    GlobalPackageListAPIView,
    GlobalPackageRetrieveAPIView,
    GlobalPackageFeatureRetrieveAPIView,
    GlobalPackageFeatureRateRetrieveAPIView,
    GlobalPackageCategoriesListAPIView,
)

app_name = "global_packages"

urlpatterns = [
    path(
        "",
        view=GlobalPackageListAPIView.as_view(),
        name="listing",
    ),
    path(
        "categories",
        view=GlobalPackageCategoriesListAPIView.as_view(),
        name="categories",
    ),
    path(
        "<str:id>",
        view=GlobalPackageRetrieveAPIView.as_view(),
        name="retrieve",
    ),
    path(
        "<str:id>/features",
        view=GlobalPackageFeatureRetrieveAPIView.as_view(),
        name="features",
    ),
    path(
        "<str:pk>/feature/<str:feature_id>/rates",
        view=GlobalPackageFeatureRateRetrieveAPIView.as_view(),
        name="feature_rates",
    ),
]
