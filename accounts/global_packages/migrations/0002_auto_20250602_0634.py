# Generated by Django 3.2.18 on 2025-06-02 06:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('global_packages', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='globalpackagefeaturerates',
            options={'ordering': ['created']},
        ),
        migrations.AlterModelOptions(
            name='globalpackagefeatures',
            options={'ordering': ['created']},
        ),
        migrations.AlterModelOptions(
            name='globalpackagegroups',
            options={'ordering': ['created']},
        ),
        migrations.AlterModelOptions(
            name='globalpackages',
            options={'ordering': ['created']},
        ),
        migrations.AlterModelOptions(
            name='packagecategories',
            options={'ordering': ['created']},
        ),
        migrations.AlterField(
            model_name='globalpackagefeatures',
            name='status',
            field=models.SmallIntegerField(choices=[(0, 'DISABLED'), (1, 'ENABLED'), (2, 'DELETED')], default=1, help_text="1: enabled, 0:disabled, 2:deleted (option 2 can be set directly from database only, don''t keep this option in frontend)"),
        ),
        migrations.AlterField(
            model_name='globalpackages',
            name='ocs_flag',
            field=models.SmallIntegerField(choices=[(0, 'ACCOUNT'), (1, 'OCS'), (2, 'MYOPERATOR_ACCOUNT'), (3, 'NONE')]),
        ),
        migrations.AlterField(
            model_name='globalpackages',
            name='package_for',
            field=models.CharField(choices=[('vn', 'VIRTUAL_NUMBER'), ('tn', 'TOLLFREE'), ('mt', 'MOBILE_TRACKING'), ('hn', 'HEYO')], max_length=15),
        ),
        migrations.AlterField(
            model_name='globalpackages',
            name='package_type',
            field=models.CharField(choices=[('main', 'MAIN'), ('addon', 'ADDON')], max_length=6),
        ),
        migrations.AlterField(
            model_name='globalpackages',
            name='status',
            field=models.SmallIntegerField(choices=[(0, 'INACTIVE'), (1, 'ACTIVE')], default=1),
        ),
    ]
