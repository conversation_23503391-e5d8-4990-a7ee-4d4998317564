# Generated by Django 3.2.18 on 2025-03-19 11:54

import accounts.utils.common
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('products', '0001_initial'),
        ('discounts', '0001_initial'),
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PackageCategories',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('code', models.CharField(max_length=10)),
                ('name', models.CharField(max_length=100)),
                ('proposal_file', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('weightage', models.IntegerField(default=1)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.products')),
            ],
            options={
                'db_table': 'package_categories',
            },
        ),
        migrations.CreateModel(
            name='GlobalPackages',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=40)),
                ('package_type', models.CharField(choices=[('main', 'MAIN'), ('addon', 'ADDON')], max_length=6)),
                ('rent_per_month', models.DecimalField(decimal_places=3, max_digits=10)),
                ('renew_cycle', models.PositiveIntegerField(help_text='shift in package proerpty table')),
                ('is_public', models.BooleanField(default=True)),
                ('code', models.CharField(help_text='public package->package_for + package_number,custom_plan->random alaphnumeric', max_length=8)),
                ('status', models.SmallIntegerField(choices=[(0, 'INACTIVE'), (1, 'ACTIVE')], default=1)),
                ('ocs_flag', models.SmallIntegerField(choices=[(0, 'ACCOUNT'), (1, 'OCS'), (2, 'MYOPERATOR_ACCOUNT'), (3, 'NONE')])),
                ('package_for', models.CharField(choices=[('vn', 'VIRTUAL_NUMBER'), ('tn', 'TOLLFREE'), ('mt', 'MOBILE_TRACKING'), ('hn', 'HEYO')], max_length=3)),
                ('package_number', models.CharField(max_length=4)),
                ('description', models.TextField(blank=True, null=True, default=None)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('discount', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='discounts.discounts')),
                ('package_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='global_packages.packagecategories')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.products')),
            ],
            options={
                'db_table': 'global_packages',
            },
        ),
        migrations.CreateModel(
            name='GlobalPackageGroups',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('global_package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='global_packages.globalpackages')),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='users.groups')),
            ],
            options={
                'db_table': 'global_package_groups',
            },
        ),
        migrations.CreateModel(
            name='GlobalPackageFeatures',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('free_unit', models.IntegerField()),
                ('rent_per_month', models.DecimalField(decimal_places=3, max_digits=10)),
                ('status', models.SmallIntegerField(choices=[(0, 'DISABLED'), (1, 'ENABLED'), (2, 'DELETED')], default=1, help_text="1: enabled, 0:disabled, 2:deleted (option 2 can be set directly from database only, don''t keep this option in frontend)")),
                ('last_disabled_date', models.DateTimeField(default=None, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='global_packages.globalpackages')),
                ('product_feature', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.productfeatures')),
                ('product_feature_property', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='products.productfeatureproperties')),
            ],
            options={
                'db_table': 'global_package_features',
            },
        ),
        migrations.CreateModel(
            name='GlobalPackageFeatureRates',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('min', models.IntegerField()),
                ('max', models.IntegerField()),
                ('rate', models.DecimalField(decimal_places=3, max_digits=10)),
                ('status', models.SmallIntegerField(default=1)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('package_feature', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rate_slabs', to='global_packages.globalpackagefeatures')),
            ],
            options={
                'db_table': 'global_package_feature_rates',
            },
        ),
        migrations.AddIndex(
            model_name='globalpackages',
            index=models.Index(fields=['is_public', 'status', 'ocs_flag', 'product_id'], name='global_pack_is_publ_82ceb8_idx'),
        ),
        migrations.AddIndex(
            model_name='globalpackages',
            index=models.Index(fields=['product_id'], name='global_pack_product_6024ad_idx'),
        ),
        migrations.AddIndex(
            model_name='globalpackages',
            index=models.Index(fields=['name'], name='global_pack_name_7c32c6_idx'),
        ),
        migrations.AddIndex(
            model_name='globalpackages',
            index=models.Index(fields=['package_type'], name='global_pack_package_f389ba_idx'),
        ),
        migrations.AddIndex(
            model_name='globalpackages',
            index=models.Index(fields=['rent_per_month'], name='global_pack_rent_pe_899f94_idx'),
        ),
        migrations.AddIndex(
            model_name='globalpackages',
            index=models.Index(fields=['is_public'], name='global_pack_is_publ_4cb16b_idx'),
        ),
        migrations.AddIndex(
            model_name='globalpackages',
            index=models.Index(fields=['code'], name='global_pack_code_b22e79_idx'),
        ),
        migrations.AddIndex(
            model_name='globalpackages',
            index=models.Index(fields=['status'], name='global_pack_status_f6d2d4_idx'),
        ),
        migrations.AddIndex(
            model_name='globalpackages',
            index=models.Index(fields=['ocs_flag'], name='global_pack_ocs_fla_37cad2_idx'),
        ),
        migrations.AddIndex(
            model_name='globalpackages',
            index=models.Index(fields=['package_for'], name='global_pack_package_267633_idx'),
        ),
        migrations.AddIndex(
            model_name='globalpackagefeatures',
            index=models.Index(fields=['package_id'], name='global_pack_package_9f11e6_idx'),
        ),
        migrations.AddIndex(
            model_name='globalpackagefeatures',
            index=models.Index(fields=['product_feature_id'], name='global_pack_product_8abe49_idx'),
        ),
        migrations.AddIndex(
            model_name='globalpackagefeatures',
            index=models.Index(fields=['product_feature_property_id'], name='global_pack_product_9193fd_idx'),
        ),
        migrations.AddIndex(
            model_name='globalpackagefeatures',
            index=models.Index(fields=['status'], name='global_pack_status_7bf297_idx'),
        ),
        migrations.AddIndex(
            model_name='globalpackagefeaturerates',
            index=models.Index(fields=['package_feature_id'], name='global_pack_package_03192e_idx'),
        ),
    ]
