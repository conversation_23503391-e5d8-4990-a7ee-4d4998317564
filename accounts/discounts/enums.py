from accounts.enums import BaseEnum


class DiscountBucketStatusEnum(BaseEnum):
    INACTIVE = 0
    ACTIVE = 1


class DiscountApplyOnEnum(BaseEnum):
    RENTAL = 1
    USAGES = 2
    ALL = 3


class DiscountTermEnum(BaseEnum):
    FLAT = 1
    PERCENT = 2


class DiscountModelEnum(BaseEnum):
    ONE_TIME = 1
    PERIODIC = 2


class DiscountBucketAppliedOnEnum(BaseEnum):
    RENTAL = "R"
    USAGES = "U"
    ALL = "A"
