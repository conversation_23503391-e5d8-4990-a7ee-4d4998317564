import typing as t
from decimal import Decimal
from django.db import models
from accounts.billing_accounts.enums import (
    DiscountBucketStatusEnum,
)
from .queryset import DiscountBucketQuerySet

if t.TYPE_CHECKING:
    from accounts.billing_accounts.models import BillingAccounts


class ActiveDiscountBucketManager(models.Manager):
    def get_queryset(self):
        return DiscountBucketQuerySet(self.model, using=self._db).filter(
            status=DiscountBucketStatusEnum.ACTIVE.value
        )

    def billing_account(self, billing_account: "BillingAccounts"):
        return self.get_queryset().billing_account(billing_account)

    def get_discount(self) -> Decimal:
        return self.get_discount()
