from decimal import Decimal
from django.test import TestCase
from django.utils import timezone
from datetime import timedelta

from accounts.discounts.models import Discounts
from accounts.discounts.enums import DiscountApplyOnEnum, DiscountTermEnum


class DiscountsModelTestCase(TestCase):
    def test_discount_is_expired(self):
        discount = Discounts.objects.create(
            name="Test Discount",
            code="TEST123",
            term=DiscountTermEnum.FLAT.value,
            apply_on=DiscountApplyOnEnum.RENTAL.value,
            value=100,
            min_range=0,
            max_range=1000,
            valid_till=timezone.now() + timedelta(days=30),
        )
        discount.valid_till = timezone.now() - timedelta(days=1)
        self.assertTrue(discount.is_expired())
        discount.valid_till = timezone.now() + timedelta(days=30)
        self.assertFalse(discount.is_expired())

    def test_calculate_discount_amount_with_term_flat(self):
        discount = Discounts.objects.create(
            name="Test Discount",
            code="TEST123",
            term=DiscountTermEnum.FLAT.value,
            apply_on=DiscountApplyOnEnum.RENTAL.value,
            value=100,
            min_range=0,
            max_range=1000,
            valid_till=timezone.now() + timedelta(days=30),
        )
        self.assertEqual(discount.calculate_discount_amount(100), 100)
        self.assertEqual(discount.calculate_discount_amount(1), 100)
        self.assertEqual(discount.calculate_discount_amount(1000), 100)

    def test_calculate_discount_amount_with_term_percent(self):
        discount = Discounts.objects.create(
            name="Test Discount",
            code="TEST123",
            term=DiscountTermEnum.PERCENT.value,
            apply_on=DiscountApplyOnEnum.RENTAL.value,
            value=10,
            min_range=0,
            max_range=1000,
            valid_till=timezone.now() + timedelta(days=30),
        )
        self.assertEqual(discount.calculate_discount_amount(100), 10)
        self.assertEqual(discount.calculate_discount_amount(1000), 100)
        self.assertEqual(discount.calculate_discount_amount(1), 0.1)

    def test_calculate_discount_amount_with_flat_min_max_range(self):
        discount = Discounts.objects.create(
            name="Test Discount",
            code="TEST123",
            term=DiscountTermEnum.FLAT.value,
            apply_on=DiscountApplyOnEnum.RENTAL.value,
            value=100,
            min_range=100,
            max_range=1000,
            valid_till=timezone.now() + timedelta(days=30),
        )
        self.assertEqual(discount.calculate_discount_amount(99), 0)
        self.assertEqual(discount.calculate_discount_amount(0), 0)

    def test_calculate_discount_amount_with_flat_discount_value_zero(self):
        discount = Discounts.objects.create(
            name="Test Discount",
            code="TEST123",
            term=DiscountTermEnum.FLAT.value,
            apply_on=DiscountApplyOnEnum.RENTAL.value,
            value=0,
            min_range=1,
            max_range=1000,
            valid_till=timezone.now() + timedelta(days=30),
        )
        self.assertEqual(discount.calculate_discount_amount(0), 0)
        self.assertEqual(discount.calculate_discount_amount(100), 0)
        self.assertEqual(discount.calculate_discount_amount(1000), 0)

    def test_calculate_discount_amount_with_precent_min_max_range(self):
        discount = Discounts.objects.create(
            name="Test Discount",
            code="TEST123",
            term=DiscountTermEnum.PERCENT.value,
            apply_on=DiscountApplyOnEnum.RENTAL.value,
            value=100,
            min_range=100,
            max_range=1000,
            valid_till=timezone.now() + timedelta(days=30),
        )
        self.assertEqual(discount.calculate_discount_amount(99), 0)
        self.assertEqual(discount.calculate_discount_amount(0), 0)

    def test_calculate_discount_amount_with_percent_discount_value_zero(self):
        discount = Discounts.objects.create(
            name="Test Discount",
            code="TEST123",
            term=DiscountTermEnum.PERCENT.value,
            apply_on=DiscountApplyOnEnum.RENTAL.value,
            value=0,
            min_range=1,
            max_range=1000,
            valid_till=timezone.now() + timedelta(days=30),
        )
        self.assertEqual(discount.calculate_discount_amount(0), 0)
        self.assertEqual(discount.calculate_discount_amount(100), 0)
        self.assertEqual(discount.calculate_discount_amount(1000), 0)
