from django.test import TestCase
from accounts.discounts.models import (
    Discounts,
)
import pytest
import unittest.mock as mock
from django.utils import timezone

from accounts.discounts.utils import (
    create_discount,
)
from django.core.exceptions import ValidationError


class TestDiscountUtils(TestCase):
    def setUp(self):
        pass

    @mock.patch("accounts.discounts.utils.generate_discount_code")
    def test_create_discount_success(self, mock_generate_discount_code):
        discount_data = {
            "name": "Test Discount",
            "model": 1,
            "apply_on": 1,
            "period": 1,
            "value": 10,
            "min_range": 0,
            "max_range": 100,
            "term": 1,
            "product_for": "Test Product For",
            "valid_till": timezone.now(),
            "is_public": True,
        }
        mock_generate_discount_code.return_value = "TEST12"
        created_discount = create_discount(discount_data)
        result = Discounts.objects.get(id=created_discount.id)
        assert result.name == discount_data["name"]
        assert result.model == discount_data["model"]
        assert result.apply_on == discount_data["apply_on"]
        assert result.period == discount_data["period"]
        assert result.min_range == discount_data["min_range"]
        assert result.max_range == discount_data["max_range"]
        assert result.term == discount_data["term"]
        assert result.product_for == discount_data["product_for"]
        assert result.valid_till == discount_data["valid_till"]
        assert result.is_public == discount_data["is_public"]
        assert mock_generate_discount_code.call_count == 1

    @mock.patch("accounts.discounts.utils.generate_discount_code")
    def test_create_discount_failure(self, mock_generate_discount_code):
        # with value as string field
        discount_data = {
            "name": "Test Discount",
            "model": 1,
            "apply_on": 1,
            "period": 1,
            "value": "sdsd",
            "min_range": 0,
            "max_range": 100,
            "term": 1,
            "product_for": "Test Product For",
            "valid_till": timezone.now(),
            "is_public": True,
        }
        mock_generate_discount_code.return_value = "TEST1256"
        with pytest.raises(ValidationError):
            create_discount(discount_data)
