# Generated by Django 3.2.18 on 2025-03-19 11:54

import accounts.utils.common
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Discounts',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('code', models.CharField(max_length=6, unique=True)),
                ('term', models.IntegerField(choices=[(1, 'FLAT'), (2, 'PERCENT')], default=1)),
                ('model', models.IntegerField(choices=[(1, 'ONE_TIME'), (2, 'PERIODIC')], default=1, help_text='1=> one time, 2 => periodic')),
                ('apply_on', models.IntegerField(choices=[(1, 'RENTAL'), (2, 'USAGES'), (3, 'ALL')], help_text='1=> rent, 2=>usages, 3 => all')),
                ('period', models.IntegerField(default=1, help_text='period is number of months if discount is periodic. if discount is one time then its value is one')),
                ('value', models.DecimalField(decimal_places=2, default=0.0, help_text='this is discount value if flat then value is in amount and if percent then value is in percent', max_digits=10)),
                ('min_range', models.IntegerField(default=0, help_text='can be applied only on amount greated or equal to this value')),
                ('max_range', models.IntegerField(help_text='can be applied on amount equal or less than this value')),
                ('product_for', models.CharField(default='0', help_text='0=> valid for all products, else this column will product id which means valid for specific product only', max_length=36)),
                ('valid_till', models.DateTimeField(help_text='mentioned discount will be only valid till date')),
                ('is_public', models.SmallIntegerField(default=0, help_text='0 - do not  show this to anyone, specific to particular package only, 1 - public, can be shared with packages')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'discounts',
            },
        ),
    ]
