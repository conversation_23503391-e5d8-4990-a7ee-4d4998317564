import logging
from accounts.discounts.models import Discounts
import secrets
import string

logger = logging.getLogger(__name__)


def generate_discount_code(length=6):
    characters = string.ascii_letters + string.digits
    code = "".join(secrets.choice(characters) for _ in range(length))
    return code


def create_discount(data: dict) -> Discounts:
    discount_code = generate_discount_code()
    return Discounts.objects.create(
        name=data["name"],
        code=discount_code,
        model=data["model"],
        apply_on=data["apply_on"],
        period=data["period"],
        value=data["value"],
        min_range=data["min_range"],
        max_range=data["max_range"],
        term=data["term"],
        product_for=data["product_for"],
        valid_till=data["valid_till"],
        is_public=data["is_public"],
    )
