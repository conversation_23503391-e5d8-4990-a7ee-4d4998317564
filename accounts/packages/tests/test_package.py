from django.test import TestCase
from accounts.packages.tests.factories import (
    PackageFactory,
    PackageCustomFactory,
    GlobalPackageFactory,
)
from accounts.global_packages.tests.factories import (
    PackageCategoryFactory,
)
from accounts.products.tests.factories import (
    ProductFactory,
)
import unittest.mock as mock

from accounts.packages.utils.packages import (
    create_package,
)
import pytest
from django.utils import timezone
from accounts.packages.models import Packages
from accounts.discounts.tests.factories import DiscountFactory


class TestPackage(TestCase):
    def setUp(self):
        self.global_package = GlobalPackageFactory.create()
        self.product = ProductFactory.create()
        self.package = PackageFactory.create()
        self.package_custom = PackageCustomFactory.create()
        self.package_category = PackageCategoryFactory.create()
        self.discount = DiscountFactory.create()

    @mock.patch("accounts.packages.utils.packages.generate_code")
    def test_create_package_success(self, mock_generate_code):
        code = "ABCD"
        mock_generate_code.return_value = code
        data = {
            "global_package_id": self.global_package.id,
            "product_id": self.product.id,
            "name": "Test package",
            "package_type": "test",
            "rent_per_month": 500,
            "renew_cycle": 12,
            "package_custom_id": self.package_custom.id,
            "is_public": 1,
            "ocs_flag": 1,
            "package_for": "test",
            "package_category_id": self.package_category.id,
            "discount_id": self.discount.id,
            "description": "Some test description",
        }

        created_package = create_package(data)

        result = Packages.objects.get(id=created_package.id)

        assert result.product_id == data["product_id"]
        assert result.name == data["name"]
        assert result.package_type == data["package_type"]
        assert result.rent_per_month == data["rent_per_month"]
        assert result.renew_cycle == data["renew_cycle"]
        assert result.package_custom_id == data["package_custom_id"]
        assert result.is_public == data["is_public"]
        assert result.ocs_flag == data["ocs_flag"]
        assert result.package_for == data["package_for"]
        assert result.package_category_id == data["package_category_id"]
        assert result.discount_id == data["discount_id"]
        assert result.description == data["description"]
        assert result.code == code
        assert result.package_number == code
        assert mock_generate_code.call_count == 2
