from freezegun import freeze_time
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

from accounts.products.tests.factories import ProductFactory
from accounts.packages.tests.factories import (
    PackageFactory,
    PackageCategoryFactory,
)
from accounts.packages.models import Packages
from accounts.global_packages.enums import (
    PackageForEnum,
    OcsFlagEnum,
)
from accounts.packages.enums import PackageStatusEnum


class TestPackageListView(APITestCase):

    def test_list_all_packages(self):
        packages = PackageFactory.create_batch(size=5)
        url = reverse("packages:listing")
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_json = response.json()
        self.assertEqual(response_json["pagination"]["count"], 5)
        self.assertEqual(response_json["code"], 200)
        self.assertEqual(
            response_json["data"][0],
            {
                "id": str(packages[0].id),
                "product_id": str(packages[0].product_id),
                "package_custom_id": packages[0].package_custom_id,
                "global_package_id": str(packages[0].global_package_id),
                "package_category_id": packages[0].package_category_id,
                "name": packages[0].name,
                "code": packages[0].code,
                "description": packages[0].description,
                "package_type": packages[0].package_type,
                "rent_per_month": str(packages[0].rent_per_month),
                "renew_cycle": packages[0].renew_cycle,
                "is_public": True,
                "ocs_flag": OcsFlagEnum.get_name(packages[0].ocs_flag).lower(),
                "package_for": PackageForEnum.get_name(
                    packages[0].package_for
                ).lower(),
                "package_number": packages[0].package_number,
                "discount_id": None,
                "status": PackageStatusEnum.ACTIVE.name.lower(),
                "created": packages[0].created.strftime("%Y-%m-%dT%H:%M:%SZ"),
                "modified": packages[0].modified.strftime("%Y-%m-%dT%H:%M:%SZ"),
            },
        )

    def test_filter_by_product_short_code(self):
        product_myopin = ProductFactory.create(short_code="myopin")
        product_myopus = ProductFactory.create(short_code="myopus")
        product_heyoin = ProductFactory.create(short_code="heyoin")
        PackageFactory.create_batch(size=2, product=product_myopin)
        PackageFactory.create_batch(size=2, product=product_myopus)
        PackageFactory.create_batch(size=2, product=product_heyoin)

        url = reverse("packages:listing")
        for product in [product_myopin, product_myopus, product_heyoin]:
            response = self.client.get(
                url, data={"product_short_code": product.short_code}
            )
            self.assertEqual(
                response.json()["pagination"]["count"],
                Packages.objects.filter(product=product).count(),
            )
            for gp in response.json()["data"]:
                self.assertEqual(gp["product_id"], str(product.id))

        # Validate Comma Seperated Filter
        response = self.client.get(
            url,
            data={
                "product_short_code": f"{product_myopin.short_code},{product_myopus.short_code}"
            },
        )
        self.assertEqual(
            response.json()["pagination"]["count"],
            Packages.objects.filter(
                product__in=[product_myopin, product_myopus]
            ).count(),
        )

    def test_filter_by_code(self):
        PackageFactory.create(code="111")
        PackageFactory.create(code="222")

        url = reverse("packages:listing")
        for code in ["111", "222"]:
            response = self.client.get(url, data={"code": code})
            self.assertEqual(
                response.json()["pagination"]["count"],
                Packages.objects.filter(code=code).count(),
            )
            for gp in response.json()["data"]:
                self.assertEqual(gp["code"], code)

    def test_filter_by_rent_per_month(self):
        PackageFactory.create(rent_per_month="199")
        PackageFactory.create(rent_per_month="299")

        url = reverse("packages:listing")
        for rent_per_month in ["199", "299"]:
            response = self.client.get(
                url, data={"rent_per_month": rent_per_month}
            )
            self.assertEqual(
                response.json()["pagination"]["count"],
                Packages.objects.filter(rent_per_month=rent_per_month).count(),
            )

    def test_filter_by_status(self):
        url = reverse("packages:listing")
        for status in PackageStatusEnum.values():  # noqa: F402
            PackageFactory.create(status=status)
            response = self.client.get(
                url, data={"status": PackageStatusEnum.get_name(status).lower()}
            )
            self.assertEqual(
                response.json()["pagination"]["count"],
                Packages.objects.filter(status=status).count(),
            )
            for gp in response.json()["data"]:
                self.assertEqual(
                    gp["status"], PackageStatusEnum.get_name(status).lower()
                )

    def test_default_ordering_by_created_asc(self):
        with freeze_time("2023-04-01 18:30:00"):
            package_1 = PackageFactory.create(code="111")
        with freeze_time("2023-04-02 18:30:00"):
            package_2 = PackageFactory.create(code="222")

        url = reverse("packages:listing")
        response = self.client.get(url)
        response_json = response.json()

        self.assertEqual(
            response_json["data"][0]["id"], str(package_1.id)
        )
        self.assertEqual(
            response_json["data"][1]["id"], str(package_2.id)
        )

    def test_ordering_by_created_desc(self):
        with freeze_time("2023-04-01 18:30:00"):
            package_1 = PackageFactory.create(code="111")
        with freeze_time("2023-04-02 18:30:00"):
            package_2 = PackageFactory.create(code="222")

        url = reverse("packages:listing")
        response = self.client.get(url, data={"ordering": "-created"})
        response_json = response.json()

        self.assertEqual(
            response_json["data"][0]["id"], str(package_2.id)
        )
        self.assertEqual(
            response_json["data"][1]["id"], str(package_1.id)
        )

    def test_ordering_by_created_asc(self):
        with freeze_time("2023-04-01 18:30:00"):
            package_1 = PackageFactory.create(code="111")
        with freeze_time("2023-04-02 18:30:00"):
            package_2 = PackageFactory.create(code="222")

        url = reverse("packages:listing")
        response = self.client.get(url, data={"ordering": "created"})
        response_json = response.json()

        self.assertEqual(
            response_json["data"][0]["id"], str(package_1.id)
        )
        self.assertEqual(
            response_json["data"][1]["id"], str(package_2.id)
        )

    def test_ordering_by_rent_per_month_asc(self):
        package_1 = PackageFactory.create(rent_per_month="199")
        package_2 = PackageFactory.create(rent_per_month="299")

        url = reverse("packages:listing")
        response = self.client.get(url, data={"ordering": "rent_per_month"})
        response_json = response.json()

        self.assertEqual(
            response_json["data"][0]["id"], str(package_1.id)
        )
        self.assertEqual(
            response_json["data"][1]["id"], str(package_2.id)
        )

    def test_ordering_by_rent_per_month_desc(self):
        package_1 = PackageFactory.create(rent_per_month="199")
        package_2 = PackageFactory.create(rent_per_month="299")

        url = reverse("packages:listing")
        response = self.client.get(url, data={"ordering": "-rent_per_month"})
        response_json = response.json()

        self.assertEqual(
            response_json["data"][0]["id"], str(package_2.id)
        )
        self.assertEqual(
            response_json["data"][1]["id"], str(package_1.id)
        )

    def test_with_fields(self):
        packages = PackageFactory.create_batch(size=5)
        url = reverse("packages:listing")
        response = self.client.get(url, data={"fields": "id,name,code"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_json = response.json()
        self.assertEqual(response_json["pagination"]["count"], 5)
        self.assertEqual(response_json["code"], 200)
        self.assertEqual(
            response_json["data"][0],
            {
                "id": str(packages[0].id),
                "name": packages[0].name,
                "code": packages[0].code,
            },
        )


class TestGlobalPackageDetailView(APITestCase):

    def test_fetch_package_detail(self):
        # Created multiple packages to validate that api return the correct package
        PackageFactory.create_batch(size=5)
        package = PackageFactory.create()
        url = reverse("packages:detail", kwargs={"pk": package.id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_json = response.json()
        self.assertEqual(response_json["code"], 200)
        self.assertEqual(response_json["message"], "Package Details")
        self.assertEqual(
            response_json["data"],
            {
                "id": str(package.id),
                "product_id": str(package.product_id),
                "package_custom_id": package.package_custom_id,
                "global_package_id": str(package.global_package_id),
                "package_category_id": package.package_category_id,
                "name": package.name,
                "code": package.code,
                "description": package.description,
                "package_type": package.package_type,
                "rent_per_month": str(package.rent_per_month),
                "renew_cycle": package.renew_cycle,
                "is_public": True,
                "ocs_flag": OcsFlagEnum.get_name(package.ocs_flag).lower(),
                "package_for": PackageForEnum.get_name(
                    package.package_for
                ).lower(),
                "package_number": package.package_number,
                "discount_id": None,
                "status": PackageStatusEnum.ACTIVE.name.lower(),
                "created": package.created.strftime("%Y-%m-%dT%H:%M:%SZ"),
                "modified": package.modified.strftime("%Y-%m-%dT%H:%M:%SZ"),
            },
        )

    def test_fetch_package_detail_404(self):
        url = reverse("packages:detail", kwargs={"pk": "123abc"})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        response_json = response.json()
        self.assertEqual(response_json["code"], "not_found")
        self.assertEqual(response_json["message"], "Not found.")

    def test_fetch_package_detail_with_fields(self):
        package = PackageFactory.create()
        url = reverse("packages:detail", kwargs={"pk": package.id})
        response = self.client.get(
            url, data={"fields": "id,name,code,rent_per_month"}
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_json = response.json()
        self.assertEqual(response_json["code"], 200)
        self.assertEqual(response_json["message"], "Package Details")
        self.assertEqual(
            response_json["data"],
            {
                "id": str(package.id),
                "name": package.name,
                "code": package.code,
                "rent_per_month": str(package.rent_per_month),
            },
        )
