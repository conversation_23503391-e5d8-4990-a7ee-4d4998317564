from django.test import TestCase

from accounts.products.tests.factories import (
    ProductFeatureFactory,
    ProductFeaturePropertyFactory,
)
from .factories import PackageFeatureFactory


class PackageFeatureTestCase(TestCase):
    def test_get_resource_key_without_property(self):
        product_feature = ProductFeatureFactory(resource_key="duration")
        package_feature = PackageFeatureFactory(
            product_feature=product_feature, product_feature_property=None
        )
        self.assertEqual(package_feature.get_resource_key(), "duration")

    def test_get_resource_key_with_property(self):
        product_feature = ProductFeatureFactory(resource_key="duration")
        product_feature_property = ProductFeaturePropertyFactory(
            post_fix_resource="incoming"
        )
        package_feature = PackageFeatureFactory(
            product_feature=product_feature,
            product_feature_property=product_feature_property,
        )
        self.assertEqual(package_feature.get_resource_key(), "durationincoming")

    def test_get_memcache_key_without_property(self):
        product_feature = ProductFeatureFactory(memcache_key="fix_did")
        package_feature = PackageFeatureFactory(
            product_feature=product_feature, product_feature_property=None
        )
        self.assertEqual(package_feature.get_memcache_key(), "fix_did")

    def test_get_memcache_key_with_property(self):
        product_feature = ProductFeatureFactory(memcache_key="duration")
        product_feature_property = ProductFeaturePropertyFactory(
            post_fix_memcache="incoming"
        )
        package_feature = PackageFeatureFactory(
            product_feature=product_feature,
            product_feature_property=product_feature_property,
        )
        self.assertEqual(package_feature.get_memcache_key(), "durationincoming")
