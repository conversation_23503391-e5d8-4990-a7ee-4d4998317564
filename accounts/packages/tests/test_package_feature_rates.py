from django.test import TestCase
from accounts.packages.tests.factories import (
    PackageFeatureFactory,
)
from accounts.packages.utils.package_feature_rates import (
    create_package_feature_rate,
)
import pytest
from accounts.packages.models import PackageFeatureRates


class TestPackageFeatureRates(TestCase):
    def setUp(self):
        self.package_feature = PackageFeatureFactory.create()

    def test_create_package_feature_rate_success(self):
        data = {
            "package_feature_id": self.package_feature.id,
            "min": 10,
            "max": 20,
            "rate": 5.0,
            "status": 1,
        }

        created_rate = create_package_feature_rate(data)
        result = PackageFeatureRates.objects.get(id=created_rate.id)
        assert result.package_feature_id == data["package_feature_id"]
        assert result.min == data["min"]
        assert result.max == data["max"]
        assert result.rate == data["rate"]
        assert result.status == data["status"]

    def test_create_package_feature_rate_default_status(self):
        data = {
            "package_feature_id": self.package_feature.id,
            "min": 10,
            "max": 20,
            "rate": 5.0,
        }

        created_rate = create_package_feature_rate(data)

        assert created_rate.status == 1

    def test_create_package_feature_rate_missing_required_field(self):
        data = {
            "min": 10,
            "max": 20,
            "rate": 5.0,
        }
        with pytest.raises(KeyError):
            create_package_feature_rate(data)
