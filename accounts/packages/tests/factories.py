from decimal import Decimal
from factory import Faker, SubFactory
from factory.django import DjangoModelFactory

from accounts.discounts.tests.factories import DiscountFactory
from accounts.packages.models import (
    PackageCustoms,
    Packages,
    PackageFeatures,
    PackageFeatureRates,
)
from accounts.products.tests.factories import (
    ProductFactory,
    ProductFeatureFactory,
    ProductFeaturePropertyFactory,
)
from accounts.global_packages.tests.factories import (
    GlobalPackageFactory,
    PackageCategoryFactory,
)
from accounts.packages import constants
from django.utils import timezone as tz
from accounts.global_packages.enums import PackageForEnum, PackageTypeEnum
from accounts.users.tests.factories import UserProfileFactory


class PackageCustomFactory(DjangoModelFactory):

    parent_package = SubFactory(GlobalPackageFactory)
    package_rent = "300.00"
    additional_rent = "100.00"
    additional_cost = "50.00"
    additional_cost_description = Faker("text")
    payment_cycle = 12
    features = "{}"
    free = "{}"
    property_id = "{}"
    deviation = "[]"
    created_by = SubFactory(UserProfileFactory)
    accept_status = 0
    accepted_by = 0
    custom_code = Faker("text")
    discount_id = 0
    discount_type = Faker("text")
    business_name = Faker("text")
    is_pack_changed = Faker("random_int", min=0, max=1)
    comment_to_customer = Faker("text")
    pdf_url = Faker("text")

    class Meta:
        model = PackageCustoms


class PackageFactory(DjangoModelFactory):
    product = SubFactory(ProductFactory)
    global_package = SubFactory(GlobalPackageFactory)
    package_custom = None
    name = Faker("word")
    package_type = PackageTypeEnum.MAIN.value
    package_category = SubFactory(PackageCategoryFactory)
    rent_per_month = Decimal("200.000")
    renew_cycle = Faker("random_int", min=1, max=10)
    code = Faker("pystr", max_chars=8)
    ocs_flag = Faker("random_element", elements=[0, 1, 2, 3])
    package_for = Faker(
        "random_element",
        elements=[
            PackageForEnum.VIRTUAL_NUMBER.value,
            PackageForEnum.TOLLFREE.value,
            PackageForEnum.MOBILE_TRACKING.value,
        ],
    )
    package_number = Faker("pystr", max_chars=4)
    discount = None

    class Meta:
        model = Packages


class PackageFeatureFactory(DjangoModelFactory):
    package = SubFactory(PackageFactory)
    product_feature = SubFactory(ProductFeatureFactory)
    product_feature_property = SubFactory(ProductFeaturePropertyFactory)
    free_unit = Faker("random_int", min=1, max=100)
    additional = Faker("random_int", min=0, max=50)
    rent_per_month = Faker("pydecimal", left_digits=5, right_digits=3)
    status = 1
    last_disabled_date = None

    class Meta:
        model = PackageFeatures


class PackageFeatureRatesFactory(DjangoModelFactory):
    class Meta:
        model = PackageFeatureRates

    id = Faker("uuid4")
    package_feature = SubFactory(PackageFeatureFactory)
    min = Faker("random_int", min=0, max=100)
    max = Faker("random_int", min=101, max=200)
    rate = Faker("pydecimal", left_digits=7, right_digits=3, positive=True)
