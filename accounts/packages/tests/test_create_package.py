from django.test import TestCase
from accounts.packages.tests.factories import (
    PackageFactory,
    PackageCustomFactory,
    PackageFeatureFactory,
    PackageFeatureRatesFactory,
)
from accounts.global_packages.tests.factories import (
    GlobalPackageFactory,
    GlobalPackageFeatureFactory,
    GlobalPackageFeatureRatesFactory,
)
import unittest.mock as mock
from accounts.packages.utils.create_package import (
    create_package_from_custom_package,
)
import json
from accounts.discounts.tests.factories import DiscountFactory
from accounts.products.tests.factories import ProductDefaultRateSlabsFactory
from django.forms.models import model_to_dict
from accounts.packages.exceptions import PackageException


class TestCreatePackage(TestCase):
    def setUp(self):
        self.global_package = GlobalPackageFactory.create()

        self.global_package_feature = GlobalPackageFeatureFactory.create(
            package=self.global_package
        )
        self.global_package_feature_rate = (
            GlobalPackageFeatureRatesFactory.create(
                package_feature=self.global_package_feature
            )
        )

        self.product_defalut_rate_slabs = (
            ProductDefaultRateSlabsFactory.create()
        )

        self.discount = DiscountFactory.create()

        discount_dict = model_to_dict(self.discount)
        discount_dict["valid_till"] = discount_dict["valid_till"].strftime(
            "%Y-%m-%d %H:%M:%S"
        )
        discount_str = json.dumps(discount_dict)

        self.package_custom_gpf = PackageCustomFactory.create(
            parent_package=self.global_package,
            features=f'{{"{self.global_package_feature.product_feature_id}":"{self.global_package_feature.product_feature_id}"}}',
            free=f'{{"{self.global_package_feature.product_feature_id}":"0"}}',
            property_id="{}",
            discount_data=discount_str,
        )

        self.package_custom_pdrs = PackageCustomFactory.create(
            parent_package=self.global_package,
            features=f'{{"{self.product_defalut_rate_slabs.product_feature_id}":"{self.product_defalut_rate_slabs.product_feature_id}"}}',
            free=f'{{"{self.product_defalut_rate_slabs.product_feature_id}":"0"}}',
            property_id="{}",
            discount_data=discount_str,
        )

        self.package_custom_without_discount = PackageCustomFactory.create(
            parent_package=self.global_package,
            features=f'{{"{self.product_defalut_rate_slabs.product_feature_id}":"{self.product_defalut_rate_slabs.product_feature_id}"}}',
            free=f'{{"{self.product_defalut_rate_slabs.product_feature_id}":"0"}}',
            property_id="{}",
            discount_data="",
        )

        self.package_with_discount_gpf = PackageFactory.create(
            product=self.global_package.product,
            name=self.global_package.name,
            package_type=self.global_package.package_type,
            rent_per_month=float(self.package_custom_gpf.package_rent)
            + float(self.package_custom_gpf.additional_rent),
            renew_cycle=self.package_custom_gpf.payment_cycle,
            package_custom=self.package_custom_gpf,
            is_public=0,
            ocs_flag=0,
            package_for=self.global_package.package_for,
            package_category=self.global_package.package_category,
            discount=self.discount,
            description=None,
        )
        self.package_feature_discount_gpf = PackageFeatureFactory.create(
            package=self.package_with_discount_gpf
        )
        self.package_feature_rate_discount_gpf = (
            PackageFeatureRatesFactory.create(
                package_feature=self.package_feature_discount_gpf
            )
        )

        self.package_wo_discount_gpf = PackageFactory.create(
            product=self.global_package.product,
            name=self.global_package.name,
            package_type=self.global_package.package_type,
            rent_per_month=float(self.package_custom_gpf.package_rent)
            + float(self.package_custom_gpf.additional_rent),
            renew_cycle=self.package_custom_gpf.payment_cycle,
            package_custom=self.package_custom_gpf,
            is_public=0,
            ocs_flag=0,
            package_for=self.global_package.package_for,
            package_category=self.global_package.package_category,
            discount=None,
            description=None,
        )
        self.package_feature_wo_discount_gpf = PackageFeatureFactory.create(
            package=self.package_wo_discount_gpf
        )
        self.package_feature_rate_wo_discount_gpf = (
            PackageFeatureRatesFactory.create(
                package_feature=self.package_feature_wo_discount_gpf
            )
        )

        self.discount2 = DiscountFactory.create()
        self.package_default_rate_slab = PackageFactory.create(
            product=self.global_package.product,
            name=self.global_package.name,
            package_type=self.global_package.package_type,
            rent_per_month=float(self.package_custom_pdrs.package_rent)
            + float(self.package_custom_pdrs.additional_rent),
            renew_cycle=self.package_custom_pdrs.payment_cycle,
            package_custom=self.package_custom_pdrs,
            is_public=0,
            ocs_flag=0,
            package_for=self.global_package.package_for,
            package_category=self.global_package.package_category,
            discount=self.discount2,
            description=None,
        )
        self.package_feature_default_rate_slab = PackageFeatureFactory.create(
            package=self.package_default_rate_slab
        )
        self.package_feature_rate_default_rate_slab = (
            PackageFeatureRatesFactory.create(
                package_feature=self.package_feature_default_rate_slab
            )
        )

    @mock.patch("accounts.discounts.models.Discounts.objects.create")
    @mock.patch("accounts.packages.models.Packages.objects.create")
    @mock.patch("accounts.packages.models.PackageFeatures.objects.create")
    @mock.patch("accounts.packages.models.PackageFeatureRates.objects.create")
    def test_create_package_with_discount_active_global_feature_rate_success(
        self,
        mock_create_package_feature_rate,
        mock_create_package_feature,
        mock_create_package,
        mock_create_discount,
    ):
        mock_create_discount.return_value = self.discount
        mock_create_package.return_value = self.package_with_discount_gpf
        mock_create_package_feature.return_value = (
            self.package_feature_discount_gpf
        )
        mock_create_package_feature_rate.return_value = (
            self.package_feature_rate_discount_gpf
        )
        package = create_package_from_custom_package(self.package_custom_gpf.id)
        assert package.id == self.package_with_discount_gpf.id
        assert package.discount.id == self.discount.id

    @mock.patch("accounts.packages.models.Packages.objects.create")
    @mock.patch("accounts.packages.models.PackageFeatures.objects.create")
    @mock.patch("accounts.packages.models.PackageFeatureRates.objects.create")
    def test_create_package_without_discount_active_global_feature_rate_success(
        self,
        mock_create_package_feature_rate,
        mock_create_package_feature,
        mock_create_package,
    ):
        mock_create_package.return_value = self.package_wo_discount_gpf
        mock_create_package_feature.return_value = (
            self.package_feature_wo_discount_gpf
        )
        mock_create_package_feature_rate.return_value = (
            self.package_feature_rate_wo_discount_gpf
        )
        package = create_package_from_custom_package(
            self.package_custom_without_discount.id
        )
        assert package.id == self.package_wo_discount_gpf.id
        assert package.discount is None

    @mock.patch("accounts.discounts.models.Discounts.objects.create")
    @mock.patch("accounts.packages.models.Packages.objects.create")
    @mock.patch("accounts.packages.models.PackageFeatures.objects.create")
    @mock.patch("accounts.packages.models.PackageFeatureRates.objects.create")
    def test_create_package_with_discount_active_default_rate_slab_success(
        self,
        mock_create_package_feature_rate,
        mock_create_package_feature,
        mock_create_package,
        mock_create_discount,
    ):
        mock_create_discount.return_value = self.discount2
        mock_create_package.return_value = self.package_default_rate_slab
        mock_create_package_feature.return_value = (
            self.package_feature_default_rate_slab
        )
        mock_create_package_feature_rate.return_value = (
            self.package_feature_rate_default_rate_slab
        )
        package = create_package_from_custom_package(
            self.package_custom_pdrs.id
        )
        assert package.id == self.package_default_rate_slab.id
        assert package.discount.id == self.discount2.id

    def test_invalid_custom_package_id(self):
        with self.assertRaises(PackageException) as context:
            create_package_from_custom_package(999)
        self.assertEqual(str(context.exception), "custom package not found")
