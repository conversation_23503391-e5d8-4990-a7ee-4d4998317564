import pytest
from django.utils import timezone
from django.test import TestCase
from accounts.packages.tests.factories import (
    PackageFactory,
)
from accounts.products.tests.factories import (
    ProductFeaturePropertyFactory,
    ProductFeatureFactory,
)
from accounts.packages.tests.factories import PackageFeatureFactory
from accounts.products.enums import ProductFeatureBillingTypeEnum
from accounts.packages.utils.package_features import (
    create_package_feature,
    calculate_leg_a_use,
)
from accounts.packages.models import PackageFeatures


class TestPackageFeature(TestCase):
    def setUp(self):
        self.package = PackageFactory.create()
        self.product_feature = ProductFeatureFactory.create()
        self.product_feature_property = ProductFeaturePropertyFactory.create()

    def test_create_package_feature_success(self):
        data = {
            "package_id": self.package.id,
            "product_feature_id": self.product_feature.id,
            "product_feature_property_id": self.product_feature_property.id,
            "free_unit": 5,
            "additional": 2,
            "rent_per_month": 150.50,
            "status": 1,
            "last_disabled_date": timezone.now(),
        }

        created_feature = create_package_feature(data)
        result = PackageFeatures.objects.get(id=created_feature.id)
        assert result.package_id == data["package_id"]
        assert result.product_feature_id == data["product_feature_id"]
        assert (
            result.product_feature_property_id
            == data["product_feature_property_id"]
        )
        assert result.free_unit == data["free_unit"]
        assert result.additional == data["additional"]
        assert result.rent_per_month == data["rent_per_month"]
        assert result.status == data["status"]

    def test_create_package_feature_with_defaults(self):
        data = {
            "package_id": self.package.id,
            "product_feature_id": self.product_feature.id,
            "product_feature_property_id": self.product_feature_property.id,
            "free_unit": 5,
            "rent_per_month": 150.50,
            "last_disabled_date": timezone.now(),
        }

        created_feature = create_package_feature(data)
        result = PackageFeatures.objects.get(id=created_feature.id)
        assert result.package_id == data["package_id"]
        assert result.product_feature_id == data["product_feature_id"]
        assert (
            result.product_feature_property_id
            == data["product_feature_property_id"]
        )
        assert result.free_unit == data["free_unit"]
        assert result.additional == 0
        assert result.rent_per_month == data["rent_per_month"]
        assert result.status == 1

    def test_create_package_feature_missing_required_field(self):
        data = {}
        with pytest.raises(KeyError):
            create_package_feature(data)


@pytest.mark.django_db
@pytest.mark.parametrize(
    "billing_type, use, current_leg_a_use, expected_result",
    [
        # NORMAL: should return use
        (ProductFeatureBillingTypeEnum.NORMAL.value, 10, 5, 10),
        # FIXED: free_unit + additional
        (ProductFeatureBillingTypeEnum.FIXED.value, 0, 0, 15),
        # FIXED: free_unit + additional
        (ProductFeatureBillingTypeEnum.FIXED.value, 0, 5, 15),
        # LICENSED: max(current_leg_a_use, use)
        (ProductFeatureBillingTypeEnum.LICENSED.value, 10, 5, 10),
        # LICENSED: max(current_leg_a_use, use)
        (ProductFeatureBillingTypeEnum.LICENSED.value, 5, 10, 10),
        # EVENT: current_leg_a_use + use
        (ProductFeatureBillingTypeEnum.EVENT.value, 10, 5, 15),
    ],
)
def test_calculate_leg_a_use(
    billing_type, use, current_leg_a_use, expected_result
):
    package_feature = PackageFeatureFactory(
        product_feature__billing_type=billing_type,
        free_unit=10,
        additional=5,
    )
    result = calculate_leg_a_use(package_feature, use, current_leg_a_use)
    assert result == expected_result


@pytest.mark.django_db
def test_calculate_leg_a_use_not_implemented():
    package_feature = PackageFeatureFactory(
        product_feature__billing_type="UNKNOWN"
    )
    with pytest.raises(
        NotImplementedError, match="billing_type: UNKNOWN is not implemented"
    ):
        calculate_leg_a_use(package_feature, 10)
