# Generated by Django 3.2.18 on 2025-03-19 11:54

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('packages', '0001_initial'),
        ('global_packages', '0001_initial'),
        ('services', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='packagefeaturepromos',
            name='service',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='services.services'),
        ),
        migrations.AddField(
            model_name='packagefeaturehistory',
            name='package_feature',
            field=models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='packages.packagefeatures'),
        ),
        migrations.AddField(
            model_name='packagecustoms',
            name='parent_package',
            field=models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='global_packages.globalpackages'),
        ),
        migrations.AddIndex(
            model_name='packages',
            index=models.Index(fields=['product_id'], name='packages_product_444a2c_idx'),
        ),
        migrations.AddIndex(
            model_name='packages',
            index=models.Index(fields=['name'], name='packages_name_2c850c_idx'),
        ),
        migrations.AddIndex(
            model_name='packages',
            index=models.Index(fields=['package_type'], name='packages_package_54127e_idx'),
        ),
        migrations.AddIndex(
            model_name='packages',
            index=models.Index(fields=['rent_per_month'], name='packages_rent_pe_a7a32e_idx'),
        ),
        migrations.AddIndex(
            model_name='packages',
            index=models.Index(fields=['is_public'], name='packages_is_publ_200ba9_idx'),
        ),
        migrations.AddIndex(
            model_name='packages',
            index=models.Index(fields=['code'], name='packages_code_ea7575_idx'),
        ),
        migrations.AddIndex(
            model_name='packages',
            index=models.Index(fields=['status'], name='packages_status_3149ce_idx'),
        ),
        migrations.AddIndex(
            model_name='packages',
            index=models.Index(fields=['ocs_flag'], name='packages_ocs_fla_6255a7_idx'),
        ),
        migrations.AddIndex(
            model_name='packages',
            index=models.Index(fields=['package_for'], name='packages_package_d2ced7_idx'),
        ),
        migrations.AddIndex(
            model_name='packages',
            index=models.Index(fields=['package_custom_id'], name='packages_package_ade3b8_idx'),
        ),
        migrations.AddIndex(
            model_name='packagefeatures',
            index=models.Index(fields=['package_id'], name='package_fea_package_f7cce2_idx'),
        ),
        migrations.AddIndex(
            model_name='packagefeatures',
            index=models.Index(fields=['product_feature_id'], name='package_fea_product_fcbe89_idx'),
        ),
        migrations.AddIndex(
            model_name='packagefeatures',
            index=models.Index(fields=['product_feature_property_id'], name='package_fea_product_d59a54_idx'),
        ),
        migrations.AddIndex(
            model_name='packagefeatures',
            index=models.Index(fields=['status'], name='package_fea_status_128564_idx'),
        ),
        migrations.AddIndex(
            model_name='packagefeatures',
            index=models.Index(fields=['last_disabled_date'], name='package_fea_last_di_938868_idx'),
        ),
        migrations.AddIndex(
            model_name='packagefeaturerates',
            index=models.Index(fields=['package_feature_id', 'status'], name='package_fea_package_4bff6a_idx'),
        ),
        migrations.AddIndex(
            model_name='packagefeaturepromos',
            index=models.Index(fields=['service_id'], name='package_fea_service_04da21_idx'),
        ),
        migrations.AddIndex(
            model_name='packagefeaturepromos',
            index=models.Index(fields=['package_feature_id'], name='package_fea_package_bc531a_idx'),
        ),
    ]
