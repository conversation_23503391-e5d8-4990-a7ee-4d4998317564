# Generated by Django 3.2.18 on 2025-06-02 06:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('packages', '0002_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='packagefeaturepromos',
            options={'ordering': ['created']},
        ),
        migrations.AlterModelOptions(
            name='packagefeaturerates',
            options={'ordering': ['created']},
        ),
        migrations.AlterModelOptions(
            name='packagefeatures',
            options={'ordering': ['created']},
        ),
        migrations.AlterModelOptions(
            name='packages',
            options={'ordering': ['created']},
        ),
        migrations.AlterField(
            model_name='packagefeaturehistory',
            name='event_type',
            field=models.SmallIntegerField(choices=[(1, 'ACTIVATED'), (2, 'DEACTIVATED'), (3, 'RENEWED'), (4, 'CANCELLED'), (5, 'REACTIVATED')]),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='packages',
            name='is_public',
            field=models.<PERSON><PERSON>anField(default=True),
        ),
        migrations.AlterField(
            model_name='packages',
            name='status',
            field=models.SmallIntegerField(choices=[(0, 'INACTIVE'), (1, 'ACTIVE')], default=1),
        ),
    ]
