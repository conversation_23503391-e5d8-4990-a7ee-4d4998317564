# Generated by Django 3.2.18 on 2025-03-19 11:54

import accounts.utils.common
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('discounts', '0001_initial'),
        ('global_packages', '0001_initial'),
        ('products', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PackageCustoms',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('package_rent', models.DecimalField(decimal_places=2, max_digits=10)),
                ('additional_rent', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('additional_cost', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('additional_cost_description', models.CharField(default='0.00', max_length=250)),
                ('payment_cycle', models.IntegerField()),
                ('features', models.TextField()),
                ('free', models.TextField()),
                ('property_id', models.TextField(blank=True)),
                ('deviation', models.TextField()),
                ('created_by', models.CharField(max_length=36)),
                ('accept_status', models.SmallIntegerField(default=0)),
                ('accepted_by', models.CharField(max_length=36)),
                ('custom_code', models.CharField(max_length=20)),
                ('discount_id', models.IntegerField(default=None, null=True)),
                ('discount_type', models.CharField(default='0', max_length=50)),
                ('discount_data', models.TextField(blank=True, default=None, null=True)),
                ('business_name', models.CharField(max_length=255, null=True)),
                ('is_pack_changed', models.IntegerField(default=0)),
                ('comment_to_customer', models.TextField(blank=True)),
                ('pdf_url', models.CharField(max_length=250, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'package_customs',
            },
        ),
        migrations.CreateModel(
            name='PackageFeatureHistory',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('event_type', models.SmallIntegerField(choices=[(1, 'ACTIVATED'), (2, 'DEACTIVATED'), (3, 'RENEWED'), (4, 'CANCELLED'), (5, 'REACTIVATED')])),
                ('created', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'db_table': 'package_feature_history',
            },
        ),
        migrations.CreateModel(
            name='Packages',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=40)),
                ('package_type', models.CharField(choices=[('add-on', 'add-on'), ('main', 'main')], max_length=6)),
                ('rent_per_month', models.DecimalField(decimal_places=3, max_digits=10)),
                ('renew_cycle', models.PositiveIntegerField()),
                ('is_public', models.BooleanField(default=True)),
                ('code', models.CharField(max_length=8)),
                ('status', models.SmallIntegerField(choices=[(0, 'INACTIVE'), (1, 'ACTIVE')], default=1)),
                ('ocs_flag', models.SmallIntegerField(choices=[(0, 'account only'), (1, 'ocs only'), (2, 'myoperator and account both'), (3, 'non')])),
                ('package_for', models.CharField(choices=[('vn', 'virtual number'), ('tn', 'tollfree number'), ('mt', 'mobile sync'), ('hn', 'Heyo number')], max_length=3)),
                ('package_number', models.CharField(max_length=4)),
                ('description', models.TextField(blank=True, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('discount', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='discounts.discounts')),
                ('global_package', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='global_packages.globalpackages')),
                ('package_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='global_packages.packagecategories')),
                ('package_custom', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='packages.packagecustoms')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='products.products')),
            ],
            options={
                'db_table': 'packages',
            },
        ),
        migrations.CreateModel(
            name='ProposalTestimonials',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('proposal_type', models.CharField(max_length=100)),
                ('name', models.CharField(max_length=250)),
                ('text', models.TextField()),
                ('logo', models.CharField(max_length=250)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('package_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='global_packages.packagecategories')),
            ],
            options={
                'db_table': 'proposal_testimonials',
            },
        ),
        migrations.CreateModel(
            name='PlanShares',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('business_type', models.CharField(max_length=200)),
                ('from_emails', models.TextField()),
                ('to_emails', models.TextField()),
                ('cc_emails', models.TextField()),
                ('bcc_emails', models.TextField()),
                ('email_body', models.TextField()),
                ('attachments', models.CharField(max_length=250)),
                ('shared_by_user', models.CharField(max_length=36)),
                ('proposal', models.CharField(max_length=50)),
                ('shared_by_dept', models.CharField(max_length=36)),
                ('type', models.SmallIntegerField(default=1, help_text='1-Custom package proposal, 2- Readymade proposals')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='packages.packages')),
            ],
            options={
                'db_table': 'plan_shares',
            },
        ),
        migrations.CreateModel(
            name='PackageFeatures',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('free_unit', models.IntegerField(default=0)),
                ('additional', models.IntegerField(default=0)),
                ('rent_per_month', models.DecimalField(decimal_places=3, max_digits=10)),
                ('status', models.SmallIntegerField(choices=[(0, 'disabled'), (1, 'enabled'), (2, 'deleted')], default=1)),
                ('last_disabled_date', models.DateTimeField(default=None, null=True)),
                ('is_customer_cancelled', models.BooleanField(default=False)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='packages.packages')),
                ('product_feature', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.productfeatures')),
                ('product_feature_property', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='products.productfeatureproperties')),
            ],
            options={
                'db_table': 'package_features',
            },
        ),
        migrations.CreateModel(
            name='PackageFeatureRates',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('min', models.IntegerField()),
                ('max', models.IntegerField()),
                ('rate', models.DecimalField(decimal_places=3, max_digits=10)),
                ('status', models.SmallIntegerField(default=1)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('package_feature', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rate_slabs', to='packages.packagefeatures')),
            ],
            options={
                'db_table': 'package_feature_rates',
            },
        ),
        migrations.CreateModel(
            name='PackageFeaturePromos',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('activation_date', models.DateTimeField()),
                ('free_units', models.IntegerField(default=0)),
                ('months', models.SmallIntegerField(default=0)),
                ('status', models.SmallIntegerField(default=1, help_text='1 => promo_active, 0 => promo_expired')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('package_feature', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='packages.packagefeatures')),
            ],
            options={
                'db_table': 'package_feature_promo',
            },
        ),
    ]
