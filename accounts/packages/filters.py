from django_filters import rest_framework as django_filters
from django_filters import rest_framework as filters
from accounts.packages.models import (
    Packages,
    PackageFeatures,
    PackageFeatureHistory,
)
from accounts.packages.constants import (
    PACKAGE_STATUS_ACTIVE,
    PACKAGE_STATUS_INACTIVE,
)
from accounts.packages.enums import (
    PackageFeatureEventTypeEnums,
    PackageFeatureStatusEnum,
    PackageStatusEnum,
)


class PackageFilter(django_filters.FilterSet):
    status = filters.CharFilter(method="filter_status")
    product_short_code = django_filters.CharFilter(
        method="filter_product_short_code"
    )

    def filter_product_short_code(self, queryset, name, value):
        product_short_codes = value.split(",")
        return queryset.filter(product__short_code__in=product_short_codes)

    def filter_status(self, queryset, name, value):
        value = PackageStatusEnum.get_value(value.upper())
        return queryset.filter(status=value)

    class Meta:
        model = Packages
        fields = {
            "code": ["exact"],
            "rent_per_month": ["exact"],
            "package_category_id": ["exact"],
        }


class PackageFeatureFilter(django_filters.FilterSet):
    resource_key = django_filters.CharFilter(method="filter_by_resource_key")
    is_paid = django_filters.BooleanFilter(method="filter_by_is_paid")
    status = django_filters.CharFilter(method="filter_by_status")

    def filter_by_resource_key(self, queryset, name, value):
        resource_keys = value.split(",")
        return queryset.filter(product_feature__resource_key__in=resource_keys)

    def filter_by_is_paid(self, queryset, name, value):
        return queryset.filter(product_feature__is_paid=value)

    def filter_by_status(self, queryset, name, value):
        statuses = [
            PackageFeatureStatusEnum.dict().get(v.strip().upper())
            for v in value.split(",")
        ]
        statuses = [
            status for status in statuses if status is not None
        ]  # Remove any invalid statuses
        return queryset.filter(status__in=statuses)

    class Meta:
        model = PackageFeatures
        fields = []


class PackageFeatureHistoryFilter(django_filters.FilterSet):
    status = django_filters.CharFilter(method="filter_by_status")

    def filter_by_status(self, queryset, name, value):
        status = PackageFeatureEventTypeEnums.dict().get(value.upper())
        return queryset.filter(event_type=status)

    class Meta:
        model = PackageFeatureHistory
        fields = {"created": ["exact"]}
