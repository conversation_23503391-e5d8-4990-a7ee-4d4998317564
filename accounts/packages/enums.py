from accounts.enums import BaseEnum


class PackageFeatureEventTypeEnums(BaseEnum):
    ACTIVATED = 1
    DEACTIVATED = 2
    RENEWED = 3
    CANCELLED = 4
    REACTIVATED = 5


class PackageFeatureStatusEnum(BaseEnum):
    ENABLED = 1
    DISABLED = 0
    DELETED = 2
    SUSPENDED = 3


class PackageFeatureDeactivateModeEnums(BaseEnum):
    END_OF_BILLING_CYCLE = 1
    INSTANT = 2


class PackageStatusEnum(BaseEnum):
    INACTIVE = 0
    ACTIVE = 1
