import logging
from rest_framework.response import Response
from rest_framework import generics
from accounts.packages.serializers import (
    PackageSerializer,
    PackageFeatureSerializer,
    PackageFeatureRateSerializer,
    PackageFeatureDeactivateSerializer,
    PackageFeatureHistorySerializer,
)
from accounts.packages.models import (
    Packages,
    PackageFeatures,
    PackageFeatureRates,
    PackageFeatureHistory,
)
from rest_framework import filters
from django_filters import rest_framework as django_filters
from accounts.packages.filters import (
    PackageFilter,
    PackageFeatureFilter,
    PackageFeatureHistoryFilter,
)
from accounts.exceptions import BaseException
from rest_framework.exceptions import NotFound
from accounts.packages.utils.package_features import (
    cancel_active_package_feature,
)
from rest_framework.serializers import ValidationError
from rest_framework import status
from accounts import error_codes
from rest_framework.views import APIView
from accounts.packages.tasks import package_changed_task
from accounts.generics import SnsHandlerView

logger = logging.getLogger(__name__)


class PackageListAPIView(generics.ListAPIView):
    serializer_class = PackageSerializer
    queryset = Packages.objects.all()
    filter_backends = [
        filters.OrderingFilter,
        django_filters.DjangoFilterBackend,
    ]
    filterset_class = PackageFilter
    ordering_fields = ["created", "rent_per_month"]

    def get(self, request, *args, **kwargs):
        try:
            return super().get(request, *args, **kwargs)
        except BaseException as e:
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class PackageRetrieveAPIView(generics.RetrieveAPIView):
    serializer_class = PackageSerializer

    def get_object(self):
        obj = generics.get_object_or_404(
            Packages.objects.all(), pk=self.kwargs.get("pk")
        )
        return obj

    def get(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return Response(
                {
                    "status": "success",
                    "message": "Package Details",
                    "data": serializer.data,
                }
            )
        except BaseException as e:
            logger.title("Package Detail Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class PackageFeatureRetrieveAPIView(generics.ListAPIView):
    serializer_class = PackageFeatureSerializer
    filter_backends = [
        filters.OrderingFilter,
        django_filters.DjangoFilterBackend,
    ]
    filterset_class = PackageFeatureFilter
    ordering_fields = ["created"]

    pagination_class = None

    def get_queryset(self):
        package_id = self.kwargs.get("pk")
        package = Packages.objects.get(id=package_id)
        query = (
            PackageFeatures.objects.select_related("product_feature")
            .filter(package=package)
            .prefetch_related("rate_slabs")
        )

        return query

    def get(self, request, *args, **kwargs):
        try:
            queryset = self.filter_queryset(self.get_queryset())
            serializer = self.get_serializer(queryset, many=True)
            return Response(
                {
                    "status": "success",
                    "message": "Package Features",
                    "data": serializer.data,
                }
            )
        except Packages.DoesNotExist:
            raise NotFound(detail="Package not found", code=404)
        except BaseException as e:
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class PackageFeatureRateRetrieveAPIView(generics.RetrieveAPIView):
    serializer_class = PackageFeatureRateSerializer

    def get_queryset(self):
        feature_id = self.kwargs.get("feature_id")
        query = PackageFeatureRates.objects.filter(
            package_feature_id=feature_id
        )
        return query

    def get(self, request, *args, **kwargs):
        try:
            try:
                package = Packages.objects.filter(id=self.kwargs.get("pk"))
            except package.DoesNotExist:
                raise NotFound(detail="Package not found", code=404)
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)
            return Response(
                {"status": "success", "message": "", "data": serializer.data}
            )
        except BaseException as e:
            logger.title("Package feature Rate Detail Error").error(
                e, exc_info=True
            )
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class PackageFeatureDeactivateAPIView(APIView):
    def post(self, request, *args, **kwargs):
        try:
            if not Packages.objects.filter(id=self.kwargs.get("pk")).exists():
                raise NotFound(detail="Package not found", code=404)

            serializer = PackageFeatureDeactivateSerializer(
                data=request.data,
                context={
                    "package_id": self.kwargs.get("pk"),
                    "package_feature_id": self.kwargs.get("package_feature_id"),
                },
            )
            serializer.is_valid(raise_exception=True)
            cancel_active_package_feature(
                self.kwargs.get("package_feature_id"),
                serializer.validated_data["did"],
                serializer.validated_data["gsn"],
                serializer.validated_data["to_cancel_date"],
            )
            return Response(
                {
                    "status": "success",
                    "message": "Subscription cancelled successfully",
                }
            )
        except ValidationError as e:
            logger.title("Feature Deactivate Validation Error").error(
                e, exc_info=True
            )
            return Response(
                {
                    "errors": e.detail,
                    "code": error_codes.VALIDATION_ERROR,
                },
                status.HTTP_400_BAD_REQUEST,
            )
        except BaseException as e:
            logger.title("Feature Deactivate Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class PackageChangedEventProcessView(SnsHandlerView):
    def notification_handler(self, message):
        try:
            gsn = message["gsn"]
            old_package_id = message["old_package_id"]
            task = (
                package_changed_task.apply_async(
                    kwargs={"gsn": gsn, "old_package_id": old_package_id},
                ),
            )
            logger.title("Celery Task Scheduled").info(f"ID: {task[0]}")
            return Response(
                {"message": "Task Scheduled", "data": {"task_id": str(task[0])}}
            )
        except (TypeError, KeyError):
            raise ValidationError(detail="params are missing")
        except BaseException as e:
            logger.error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class PackageFeatureHistoryRetrieveAPIView(generics.RetrieveAPIView):
    serializer_class = PackageFeatureHistorySerializer
    filter_backends = [
        filters.OrderingFilter,
        django_filters.DjangoFilterBackend,
    ]
    filterset_class = PackageFeatureHistoryFilter
    ordering_fields = ["created"]

    pagination_class = None

    def get_queryset(self):
        package_id = self.kwargs.get("pk")
        package_feature_id = self.kwargs.get("package_feature_id")
        package = Packages.objects.get(id=package_id)
        PackageFeatures.objects.get(package=package, id=package_feature_id)
        query = PackageFeatureHistory.objects.filter(
            package_feature_id=package_feature_id
        )

        return query

    def get(self, request, *args, **kwargs):
        try:
            queryset = self.filter_queryset(self.get_queryset())
            serializer = self.get_serializer(queryset, many=True)
            return Response(
                {"status": "success", "message": "", "data": serializer.data}
            )
        except Packages.DoesNotExist:
            raise NotFound(detail="Package not found", code=404)
        except PackageFeatures.DoesNotExist:
            raise NotFound(detail="Package Feature not found", code=404)
        except BaseException as e:
            logger.title("Package feature history Detail Error").error(
                e, exc_info=True
            )
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )
