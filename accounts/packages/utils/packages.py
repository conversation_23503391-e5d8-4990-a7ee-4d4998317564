import logging
from accounts.packages.models import Packages
import secrets
import string

logger = logging.getLogger(__name__)


def generate_code(length=6):
    characters = string.ascii_letters + string.digits
    code = "".join(secrets.choice(characters) for _ in range(length))
    return code


def get_package_details_by_code(code: str) -> Packages:
    return Packages.objects.filter(code=code).first()


def create_package(data: dict) -> Packages:
    package_code = generate_code(6)
    package_number = generate_code(4)
    return Packages.objects.create(
        global_package_id=data.get("global_package_id", None),
        product_id=data.get("product_id", None),
        name=data["name"],
        package_type=data["package_type"],
        rent_per_month=data["rent_per_month"],
        renew_cycle=data["renew_cycle"],
        package_custom_id=data.get("package_custom_id", None),
        is_public=data["is_public"],
        code=package_code,
        ocs_flag=data["ocs_flag"],
        package_for=data["package_for"],
        package_category_id=data.get("package_category_id", None),
        package_number=package_number,
        discount_id=data.get("discount_id", None),
        description=data["description"],
    )
