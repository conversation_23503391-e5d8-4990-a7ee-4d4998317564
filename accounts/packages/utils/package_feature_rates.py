import logging
from accounts.packages.models import (
    PackageFeatureRates,
)
from django.db.models.query import QuerySet
from accounts.packages import constants

logger = logging.getLogger(__name__)


def create_package_feature_rate(data: dict) -> PackageFeatureRates:
    return PackageFeatureRates.objects.create(
        package_feature_id=data["package_feature_id"],
        min=data["min"],
        max=data["max"],
        rate=data["rate"],
        status=data.get("status", constants.PACKAGE_FEATURE_RATE_ACTIVE),
    )


def get_rates_by_package_feature_id(package_feature_id: str) -> QuerySet:
    return PackageFeatureRates.objects.filter(
        package_feature_id=package_feature_id
    )
