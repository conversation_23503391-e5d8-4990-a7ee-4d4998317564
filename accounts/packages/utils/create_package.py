import logging
import json
from accounts.packages.models import PackageCustoms, PackageFeatures, Packages
from accounts.global_packages.models import GlobalPackages
from accounts.packages.utils.packages import (
    create_package,
)
from accounts.packages.utils.package_features import (
    create_package_feature,
)
from accounts.global_packages.utils.global_package_features import (
    get_active_global_package_feature,
)
from accounts.packages.utils.package_feature_rates import (
    create_package_feature_rate,
)
from accounts.global_packages.utils.global_package_feature_rates import (
    get_active_global_package_feature_rate,
)
from accounts.discounts.utils import create_discount
from accounts.products.utils import get_default_product_rate_slab
from django.forms.models import model_to_dict
from accounts.packages.exceptions import PackageException
from accounts.global_packages.models import (
    GlobalPackageFeatures,
    GlobalPackageFeatureRates,
)
from accounts.global_packages.enums import (
    GlobalPackageFeatureStatusEnum,
    PackageFeatureRateSlabStatusEnum,
)


logger = logging.getLogger(__name__)


def create_package_from_custom_package(custom_package_id):

    try:
        custom_package = PackageCustoms.objects.get(id=custom_package_id)
    except PackageCustoms.DoesNotExist:
        raise PackageException("custom package not found")

    logger.title("create_package_from_custom_package").debug(
        "custom package %s", model_to_dict(custom_package)
    )

    try:
        global_package = GlobalPackages.objects.get(
            id=custom_package.parent_package_id
        )
    except GlobalPackages.DoesNotExist:
        raise PackageException("global package not found")

    logger.title("create_package_from_custom_package").debug(
        "global package %s", model_to_dict(global_package)
    )

    discount_id = None
    if custom_package.discount_data:
        discount_data = json.loads(custom_package.discount_data)
        discount = create_discount(discount_data)
        logger.title("create_package_from_custom_package").debug(
            "discount created: %s", model_to_dict(discount)
        )
        discount_id = discount.id

    package_data = {
        "global_package_id": global_package.id,
        "product_id": global_package.product_id,
        "name": global_package.name,
        "package_type": global_package.package_type,
        "rent_per_month": float(custom_package.package_rent)
        + float(custom_package.additional_rent),
        "renew_cycle": custom_package.payment_cycle,
        "package_custom_id": custom_package.id,
        "is_public": 0,
        "ocs_flag": 0,
        "package_for": global_package.package_for,
        "package_category_id": global_package.package_category_id,
        "discount_id": discount_id,
        "description": None,
    }

    package = create_package(package_data)
    logger.title("create_package_from_custom_package").debug(
        "package created: %s", model_to_dict(package)
    )

    features = json.loads(custom_package.features)

    logger.title("create_package_from_custom_package").debug(
        "features: %s", features
    )

    free = json.loads(custom_package.free)

    logger.title("create_package_from_custom_package").debug("free: %s", free)

    property_id = json.loads(custom_package.property_id)

    logger.title("create_package_from_custom_package").debug(
        "property_id: %s", property_id
    )

    for key, value in features.items():

        package_feature_data = {
            "package_id": package.id,
            "product_feature_id": value,
            "product_feature_property_id": property_id.get(value, ""),
            "free_unit": int(float(free.get(value, 0))),
            "rent_per_month": 0,
            "last_disabled_date": None,
        }

        package_feature = create_package_feature(package_feature_data)
        logger.title("create_package_by_code").debug(
            "package_feature created: %s", model_to_dict(package_feature)
        )

        product_feature_property_id = property_id.get(value, None)

        active_global_package_feature = get_active_global_package_feature(
            global_package.id, value, product_feature_property_id
        )

        if not active_global_package_feature:
            active_global_package_feature_rate = get_default_product_rate_slab(
                value
            )
        else:
            active_global_package_feature_rate = (
                get_active_global_package_feature_rate(
                    active_global_package_feature.id
                )
            )
            if not active_global_package_feature_rate:
                active_global_package_feature_rate = (
                    get_default_product_rate_slab(value)
                )

        active_global_package_feature_rate = model_to_dict(
            active_global_package_feature_rate
        )

        package_feature_rate_data = {
            "package_feature_id": package_feature.id,
            "min": active_global_package_feature_rate.get("min", 0),
            "max": active_global_package_feature_rate.get("max", 10000),
            "rate": active_global_package_feature_rate.get("rate", 1),
        }

        package_feature_rate = create_package_feature_rate(
            package_feature_rate_data
        )
        logger.title("create_package_from_custom_package").debug(
            "package_feature_rate created: %s",
            model_to_dict(package_feature_rate),
        )

    return package


def create_package_from_global_package(global_package_id: str) -> Packages:
    global_package = GlobalPackages.objects.get(id=global_package_id)

    package_data = {
        "global_package_id": global_package.id,
        "product_id": global_package.product_id,
        "name": global_package.name,
        "package_type": global_package.package_type,
        "rent_per_month": global_package.rent_per_month,
        "renew_cycle": global_package.renew_cycle,
        "package_custom_id": None,
        "is_public": 0,
        "ocs_flag": 0,
        "package_for": global_package.package_for,
        "package_category_id": global_package.package_category_id,
        "discount_id": None,
        "description": global_package.description,
    }

    package = create_package(package_data)
    global_package_features = GlobalPackageFeatures.objects.filter(
        package=global_package,
        status=GlobalPackageFeatureStatusEnum.ENABLED.value,
    )
    for gpf in global_package_features:
        package_feature = PackageFeatures.objects.create(
            package=package,
            product_feature=gpf.product_feature,
            product_feature_property_id=gpf.product_feature_property_id,
            free_unit=gpf.free_unit,
            rent_per_month=gpf.rent_per_month,
        )
        global_package_feature_rates = GlobalPackageFeatureRates.objects.filter(
            package_feature_id=gpf.id,
            status=PackageFeatureRateSlabStatusEnum.ACTIVE.value,
        )
        if not global_package_feature_rates:
            for gpfr in global_package_feature_rates:
                GlobalPackageFeatureRates.objects.create(
                    package_feature=package_feature,
                    min=gpfr.min,
                    max=gpfr.max,
                    rate=gpfr.rate,
                )
        else:
            pfdr = get_default_product_rate_slab(
                gpf.product_feature_id, gpf.product_feature_property_id
            )
            if pfdr:
                GlobalPackageFeatureRates.objects.create(
                    package_feature=package_feature,
                    min=pfdr.min,
                    max=pfdr.max,
                    rate=pfdr.rate,
                )
    return package
