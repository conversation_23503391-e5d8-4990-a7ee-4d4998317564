import logging
from accounts.packages.exceptions import PackageChangedProcessFailedException
from accounts.services.utils.service_package import get_current_service_package
from accounts.packages.utils.package_features import (
    get_active_package_features,
    create_package_feature,
)
from accounts.services.utils.service import get_service_by_gsn

logger = logging.getLogger(__name__)


def process_packaged_changed(gsn: str, old_package_id: str) -> None:
    try:
        service = get_service_by_gsn(gsn)
        current_package = get_current_service_package(service.id)
        if current_package is None:
            raise PackageChangedProcessFailedException(
                "No active package associated with the GSN"
            )
        new_package_id = current_package.package.id
        if old_package_id == new_package_id:
            raise PackageChangedProcessFailedException(
                "Old package id and current package id cannot be same"
            )
        # Fetch active paid features from the old package
        old_package_features = get_active_package_features(
            old_package_id, paid_feature=True
        )
        if old_package_features is None:
            logger.title("process_packaged_changed").info(
                "Old package doesnt have paid features"
            )
            return
        # Fetch all active features of the new package
        new_package_features = get_active_package_features(new_package_id)

        # Find active features that are in the old package but not in the new package
        features_to_copy = [
            feature
            for feature in old_package_features
            if feature.product_feature.id
            not in {f.product_feature.id for f in new_package_features}
        ]

        if not features_to_copy:
            logger.title("process_packaged_changed").info("No features to copy")
            return

        # Copy missing paid features to the new package
        for feature in features_to_copy:
            create_package_feature(
                {
                    "package_id": current_package.package.id,
                    "product_feature_id": feature.product_feature.id,
                    "product_feature_property_id": feature.product_feature_property_id,
                    "rent_per_month": feature.rent_per_month,
                    "last_disabled_date": feature.last_disabled_date,
                    "is_customer_cancelled": feature.is_customer_cancelled,
                    "free_unit": feature.free_unit,
                    "additional": feature.additional,
                }
            )
    except PackageChangedProcessFailedException:
        raise
