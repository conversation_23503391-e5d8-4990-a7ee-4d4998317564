from accounts.exceptions import BaseException


class PackageException(BaseException):
    message = "Package creation error"


class InvalidPackageException(BaseException):
    message = "Invalid package"


class CustomPackageException(BaseException):
    message = "Custom package creation error"


class PackageChangeException(BaseException):
    message = "Change package error"


class PackageChangedProcessFailedException(BaseException):
    message = "Package changed processing failed"
