from django.urls import path

from accounts.packages.views import (
    PackageListAPIView,
    PackageRetrieveAPIView,
    PackageFeatureRetrieveAPIView,
    PackageFeatureRateRetrieveAPIView,
    PackageFeatureDeactivateAPIView,
    PackageChangedEventProcessView,
    PackageFeatureHistoryRetrieveAPIView,
)

app_name = "packages"

urlpatterns = [
    path(
        "",
        view=PackageListAPIView.as_view(),
        name="listing",
    ),
    path(
        "<str:pk>",
        view=PackageRetrieveAPIView.as_view(),
        name="detail",
    ),
    path(
        "<str:pk>/features",
        view=PackageFeatureRetrieveAPIView.as_view(),
        name="features",
    ),
    path(
        "<str:pk>/feature/<str:feature_id>/rates",
        view=PackageFeatureRateRetrieveAPIView.as_view(),
        name="rate_slabs",
    ),
    path(
        "<str:pk>/features/<str:package_feature_id>/deactivate",
        view=PackageFeatureDeactivateAPIView.as_view(),
        name="package_features_deactivate",
    ),
    path(
        "<str:pk>/features/<str:package_feature_id>/history",
        view=PackageFeatureHistoryRetrieveAPIView.as_view(),
        name="package_features_deactivate",
    ),
    path(
        "event/changed/process",
        view=PackageChangedEventProcessView.as_view(),
        name="process",
    ),
]
