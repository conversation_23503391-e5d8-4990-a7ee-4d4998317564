from rest_framework import serializers
from rest_flex_fields import FlexFieldsModelSerializer
from accounts.packages.models import (
    PackageCustoms,
    Packages,
    PackageFeatures,
    PackageFeatureRates,
    PackageFeatureHistory,
)
import json
from accounts.packages.constants import (
    PACKAGE_STATUS_ACTIVE,
    PACKAGE_STATUS_INACTIVE,
    PAC<PERSON>GE_FEATURE_ENABLED,
    PACKAGE_FEATURE_DISABLED,
    PACKAGE_FEATURE_DELETED,
    PACKAGE_FEATURE_RATE_ACTIVE,
    PACKAGE_FEATURE_RATE_INACTIVE,
)
from accounts.products.serializers import ProductFeatureSerializer
from accounts.packages.enums import (
    PackageFeatureDeactivateModeEnums,
    PackageFeatureEventTypeEnums,
    PackageFeatureStatusEnum,
    PackageStatusEnum,
)
from accounts.services.models import Services
from accounts.billing_accounts.utils.billing_account import (
    calculate_next_billing_date,
)
from datetime import datetime
from accounts.products.models import ProductFeatures
from accounts.global_packages.enums import (
    PackageForEnum,
    PackageTypeEnum,
    OcsFlagEnum,
    PackageFeatureRateSlabStatusEnum,
)


class CustomPackageDiscountSerializer(serializers.Serializer):
    name = serializers.CharField()
    code = serializers.CharField()
    term = serializers.IntegerField()
    model = serializers.IntegerField()
    apply_on = serializers.IntegerField()
    period = serializers.IntegerField()
    value = serializers.DecimalField(max_digits=10, decimal_places=3)


class CustomPackageSerializer(serializers.ModelSerializer):
    discount = CustomPackageDiscountSerializer(read_only=True)

    class Meta:
        model = PackageCustoms
        fields = "__all__"

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data["discount"] = {}
        if instance.discount_data:
            try:
                discount_data = json.loads(instance.discount_data)
                data["discount"] = CustomPackageDiscountSerializer(
                    discount_data
                ).data
            except json.JSONDecodeError:
                pass
        return data


class PackageSerializer(FlexFieldsModelSerializer):
    code = serializers.CharField(read_only=True)
    package_type = serializers.SerializerMethodField()
    package_for = serializers.SerializerMethodField()
    ocs_flag = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()

    def get_package_type(self, obj):
        return PackageTypeEnum.get_name(obj.package_type).lower()

    def get_package_for(self, obj):
        return PackageForEnum.get_name(obj.package_for).lower()

    def get_ocs_flag(self, obj):
        return OcsFlagEnum.get_name(obj.ocs_flag).lower()

    def get_status(self, obj):
        return PackageStatusEnum.get_name(obj.status).lower()

    class Meta:
        model = Packages
        fields = (
            "id",
            "product_id",
            "package_custom_id",
            "global_package_id",
            "package_category_id",
            "name",
            "code",
            "description",
            "package_type",
            "rent_per_month",
            "renew_cycle",
            "is_public",
            "ocs_flag",
            "package_for",
            "package_number",
            "discount_id",
            "status",
            "created",
            "modified",
        )


class PackageFeatureRateSerializer(serializers.ModelSerializer):
    status = serializers.SerializerMethodField()

    def get_status(self, obj):
        return PackageFeatureRateSlabStatusEnum.get_name(obj.status).lower()

    class Meta:
        model = PackageFeatureRates
        fields = (
            "id",
            "package_feature_id",
            "min",
            "max",
            "rate",
            "status",
            "created",
            "modified",
        )


class PackageFeatureSerializer(FlexFieldsModelSerializer):
    product_feature_id = serializers.PrimaryKeyRelatedField(
        source="product_feature", read_only=True
    )
    product_feature_property_id = serializers.PrimaryKeyRelatedField(
        source="product_feature_property", read_only=True
    )
    status = serializers.SerializerMethodField()
    resource_key = serializers.SerializerMethodField()
    memcache_key = serializers.SerializerMethodField()

    def get_status(self, obj):
        return PackageFeatureStatusEnum.get_name(obj.status).lower()

    def get_resource_key(self, obj):
        return obj.get_resource_key()

    def get_memcache_key(self, obj):
        return obj.get_memcache_key()

    class Meta:
        model = PackageFeatures
        expandable_fields = {
            "product_feature": ProductFeatureSerializer,
            "rate_slabs": (PackageFeatureRateSerializer, {"many": True}),
        }
        fields = (
            "id",
            "product_feature_id",
            "product_feature_property_id",
            "free_unit",
            "additional",
            "rent_per_month",
            "last_disabled_date",
            "resource_key",
            "memcache_key",
            "status",
            "created",
            "modified",
        )


class PackageFeatureDeactivateSerializer(serializers.Serializer):
    mode = serializers.ChoiceField(
        choices=PackageFeatureDeactivateModeEnums.values(),
        default=PackageFeatureDeactivateModeEnums.END_OF_BILLING_CYCLE.value,
    )
    gsn = serializers.CharField()
    to_cancel_date = serializers.DateTimeField(read_only=True)
    did = serializers.CharField(max_length=15)

    def validate(self, data):
        gsn = data["gsn"]
        package_id = self.context.get("package_id")
        package_feature_id = self.context.get("package_feature_id")

        # Check if package_feature is active
        if not PackageFeatures.objects.filter(
            id=package_feature_id,
            package_id=package_id,
            status=PackageFeatureStatusEnum.ENABLED.value,
        ).exists():
            raise serializers.ValidationError("Invalid package_feature_id")

        # validate if feature is already disabled by user
        package_feature = PackageFeatures.objects.get(id=package_feature_id)
        if package_feature.is_customer_cancelled:
            raise serializers.ValidationError(
                "The feature subscription has already been cancelled"
            )

        if data["mode"] == PackageFeatureDeactivateModeEnums.INSTANT.value:
            data["to_cancel_date"] = datetime.now()
        elif (
            data["mode"]
            == PackageFeatureDeactivateModeEnums.END_OF_BILLING_CYCLE.value
        ):
            service = Services.objects.get(gsn=gsn)
            next_billing_start_date = calculate_next_billing_date(
                service.billing_account.billing_day,
                service.activation_date,
                service.timezone,
            )
            data["to_cancel_date"] = next_billing_start_date
        return data


class PackageFeatureHistorySerializer(serializers.ModelSerializer):
    event = serializers.CharField(read_only=True)

    class Meta:
        model = PackageFeatureHistory
        fields = ("id", "event", "created")

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["status"] = PackageFeatureEventTypeEnums.get_name(
            instance.event_type
        ).lower()
        return representation
