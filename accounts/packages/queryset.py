import typing as t
from django.db.models import QuerySet
from accounts.packages.enums import PackageStatusEnum
from django.db.models import Q


class PackageQuerySet(QuerySet):
    def active(self) -> QuerySet:
        """
        Filter packages that are active.
        """
        return self.filter(status=PackageStatusEnum.ACTIVE.value)

    def inactive(self) -> QuerySet:
        """
        Filter packages that are inactive.
        """
        return self.filter(status=PackageStatusEnum.INACTIVE.value)

    def must_be_custom_package(self) -> QuerySet:
        """
        Filter packages that must be custom packages.
        """
        return self.filter(
            Q(package_custom__isnull=False) & ~Q(package_custom="0")
        )
