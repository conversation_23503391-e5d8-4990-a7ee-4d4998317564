import logging
import celery
import celery.states
from django.conf import settings
from accounts.packages.utils.process_package_changed import (
    process_packaged_changed,
)
from accounts.services.exceptions import InvalidServiceException
from accounts.packages.exceptions import PackageChangedProcessFailedException

logger = logging.getLogger(__name__)


@celery.shared_task(
    bind=True,
    max_retries=settings.CELERY_TASK_MAX_RETRIES,
    queue=settings.CELERY_TASK_DEFAULT_QUEUE,
)
def package_changed_task(task: celery.Task, gsn: str, old_package_id) -> None:

    task.update_state(state=celery.states.STARTED)
    try:
        try:
            process_packaged_changed(gsn, old_package_id)
        except InvalidServiceException as e:
            logger.title("Changed package Task Error").error(e, exc_info=True)
            task.update_state(state=celery.states.RETRY)
            raise task.retry(countdown=10)
        logger.info(f"Changed packaged processed for gsn: {gsn}")
        task.update_state(state=celery.states.SUCCESS)
    except celery.exceptions.Retry as e:
        logger.title("Changed packaged Task Retry").info(str(e))
    except celery.exceptions.MaxRetriesExceededError as e:
        task.update_state(state=celery.states.FAILURE)
        logger.title("Changed packaged Task Failed").critical(e, exc_info=True)
        raise
    except (PackageChangedProcessFailedException,) as e:
        logger.title("Changed packaged Task Failed").error(e)
        task.update_state(state=celery.states.FAILURE)
        raise
    except Exception as e:
        logger.title("Changed packaged Task Failed").critical(e, exc_info=True)
        task.update_state(state=celery.states.FAILURE)
        raise
