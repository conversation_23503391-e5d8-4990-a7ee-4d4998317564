from django.db.models import Manager
from .queryset import PackageQuerySet


class PackageManager(Manager):
    def get_queryset(self):
        return PackageQuerySet(self.model, using=self._db)

    def active(self) -> "PackageQuerySet":
        return self.get_queryset().active()

    def inactive(self) -> "PackageQuerySet":
        return self.get_queryset().inactive()

    def must_be_custom_package(self) -> "PackageQuerySet":
        return self.get_queryset().must_be_custom_package()
