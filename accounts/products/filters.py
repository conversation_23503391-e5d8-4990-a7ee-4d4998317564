from django_filters import rest_framework as django_filters
from django_filters import rest_framework as filters
from accounts.products.models import Products, ProductFeatures
from accounts.products.enums import (
    ProductStatusEnum,
)


class ProductFilter(django_filters.FilterSet):
    status = filters.CharFilter(method="filter_status")
    country_short_code = django_filters.CharFilter(
        method="filter_country_short_code"
    )

    def filter_country_short_code(self, queryset, name, value):
        return queryset.filter(country__short_code=value)

    def filter_status(self, queryset, name, value):
        final_status = []
        possible_statuses = ProductStatusEnum.keys()
        for status in value.strip().split(","):
            if status.upper() not in possible_statuses:
                continue
            internal_status = ProductStatusEnum.dict().get(status.upper())
            if isinstance(internal_status, int):
                final_status.append(internal_status)
        return queryset.filter(**{f"{name}__in": final_status})

    class Meta:
        model = Products
        fields = {
            "product_code": ["exact"],
            "brand_id": ["exact"],
            "short_code": ["exact"],
        }


class ProductFeatureFilter(django_filters.FilterSet):
    status = filters.CharFilter(method="filter_by_status")
    resource_key = django_filters.CharFilter(method="filter_by_resource_key")

    def filter_by_status(self, queryset, name, value):
        if value.lower() == "active":
            return queryset.filter(status=ProductStatusEnum.ACTIVE.value)
        elif value.lower() == "inactive":
            return queryset.filter(status=ProductStatusEnum.INACTIVE.value)
        return queryset

    def filter_by_resource_key(self, queryset, name, value):
        return queryset.filter(resource_key=value)

    class Meta:
        model = ProductFeatures
        fields = {"product_id": ["exact"], "is_paid": ["exact"]}
