from django.db import models
from accounts.enums import BaseEnum


class ProductStatusEnum(BaseEnum):
    INACTIVE = 0
    ACTIVE = 1


class ProductFeatureBillingTypeEnum(BaseEnum):
    NORMAL = "N"
    LICENSED = "L"
    FIXED = "F"
    EVENT = "E"


class ProductFeatureTypeEnum(BaseEnum):
    BILLABLE = 1
    NON_BILLABLE = 2


class ProductFeatureStatusEnum(BaseEnum):
    INACTIVE = 0
    ACTIVE = 1
