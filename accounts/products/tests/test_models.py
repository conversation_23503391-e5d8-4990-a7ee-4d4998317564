from django.test import TestCase
from faker import Faker
from accounts.core.tests.factories import CountryFactory
from accounts.products.models import (
    ProductDefaultRateSlabs,
    ProductDefaultSettings,
    ProductFeatureProperties,
    ProductFeatures,
    Products,
    ProductSettings,
)
from accounts.products.tests.factories import (
    ProductFactory,
    ProductFeatureFactory,
    ProductFeaturePropertyFactory,
    ProductDefaultRateSlabsFactory,
    ProductSettingFactory,
    ProductDefaultSettingFactory,
)


class TestModels(TestCase):
    def setUp(self):
        self.fake = Faker()
        country = CountryFactory.create()

        # create products
        ProductFactory.create_batch(2, country=country)

        # create product feature
        self.product = ProductFactory.create(
            country=country,
        )

        self.product_feature = ProductFeatureFactory.create(
            product=self.product,
        )

        # inactive product feature
        self.inactive_product = ProductFactory.create(
            country=country,
            status=0,
        )

        self.product_feature_property = ProductFeaturePropertyFactory.create(
            product_feature=self.product_feature,
        )

        # create product default rate slab
        self.product_default_rate_slab = ProductDefaultRateSlabsFactory.create(
            product_feature=self.product_feature,
            product_feature_property=self.product_feature_property,
            min=0,
            max=100,
        )

        # create product setting
        self.setting_key = "demo_key"
        self.product_setting = ProductSettingFactory.create(
            product=self.product,
            setting_key=self.setting_key,
            setting_value="dummy text",
        )

        # create inactive product setting
        ProductSettingFactory.create(
            product=self.inactive_product,
            setting_key=self.setting_key,
            setting_value="setting_value",
            status=0,
        )

        # create product default setting
        self.product_default_setting = ProductDefaultSettingFactory.create(
            setting_key=self.setting_key, setting_value="dummy text"
        )

    def test_default_manager(self):
        """
        Count all (active/inactive) products
        """
        count = Products.objects.count()
        assert count == 4

    def test_feature_manager(self):
        # test case with data found
        result = ProductFeatures.objects.product(self.product.id).first()
        assert result == self.product_feature
        assert result.product.id == self.product_feature.product_id

        # test case with data not found
        result = ProductFeatures.objects.product("12345678").first()
        assert result is None

    def test_active_product_feature_manager(self):
        # test case with data found
        result = ProductFeatures.active.product(self.product.id).first()
        assert result == self.product_feature
        assert result.product.id == self.product_feature.product_id

        # test case with data not found
        result = ProductFeatures.active.product(
            self.inactive_product.id
        ).first()
        assert result is None

        count = ProductFeatures.active.product(self.product.id).count()
        assert count == 1

    def test_feature_property_manager(self):
        # test case with data found
        result = ProductFeatureProperties.objects.product_feature(
            self.product_feature.id
        ).first()
        assert result == self.product_feature_property
        assert (
            result.product_feature.id
            == self.product_feature_property.product_feature.id
        )

        # test case with data not found
        result = ProductFeatureProperties.objects.product_feature(
            "12345678"
        ).first()
        assert result is None

    def test_default_rate_slab_manager(self):
        # test case with data found
        result = ProductDefaultRateSlabs.objects.product_feature(
            self.product_feature.id
        ).first()
        assert result == self.product_default_rate_slab
        assert (
            result.product_feature.id
            == self.product_default_rate_slab.product_feature.id
        )

        # test case with data not found
        result = ProductDefaultRateSlabs.objects.product_feature(
            "12345678"
        ).first()
        assert result is None

    def test_active_product_default_rate_slab_manager(self):
        # test case with data found
        result = (
            ProductDefaultRateSlabs.objects.product_feature(
                self.product_feature.id
            )
            .min(0)
            .max(100)
            .first()
        )
        assert result == self.product_default_rate_slab
        assert (
            result.product_feature.id
            == self.product_default_rate_slab.product_feature.id
        )

        # test case with data not found
        result = (
            ProductDefaultRateSlabs.objects.product_feature(
                self.product_feature.id
            )
            .min(2)
            .max(110)
            .first()
        )
        assert result is None

        count = (
            ProductDefaultRateSlabs.objects.product_feature(
                self.product_feature.id
            )
            .min(0)
            .max(100)
            .count()
        )
        assert count == 1

    def test_setting_manager(self):
        # test case with data found
        result = (
            ProductSettings.objects.product(self.product.id)
            .key(self.setting_key)
            .first()
        )
        assert result.product.id == self.product_setting.product.id
        assert result.setting_key == self.product_setting.setting_key

        # test case with data not found
        result = (
            ProductSettings.objects.product("12345678")
            .key(self.setting_key)
            .first()
        )
        assert result is None

    def test_active_product_setting_manager(self):
        # test case with data found
        result = (
            ProductSettings.active.product(self.product.id)
            .key(self.setting_key)
            .first()
        )
        assert result.product.id == self.product_setting.product.id
        assert result.setting_key == self.product_setting.setting_key

        # test case with data not found
        result = (
            ProductSettings.active.product(self.inactive_product.id)
            .key(self.setting_key)
            .first()
        )
        assert result is None

    def test_default_setting_manager(self):
        # test case with data found
        result = ProductDefaultSettings.objects.key(self.setting_key).first()
        assert result.setting_key == self.product_setting.setting_key

        # test case with data not found
        result = ProductDefaultSettings.objects.key("abc").first()
        assert result is None
