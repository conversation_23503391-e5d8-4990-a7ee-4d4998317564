import pytest
from django.test import TestCase
from faker import Faker
from accounts.core.tests.factories import CountryFactory
from accounts.products.models import (
    Products,
)
from accounts.products.tests.factories import (
    ProductFactory,
    ProductSettingFactory,
    ProductDefaultSettingFactory,
)
from accounts.products.utils import (
    get_country_code,
    get_product_from_short_code,
    get_product_setting,
    get_product_default_setting,
)


class TestProductUtils(TestCase):
    def setUp(self):

        self.fake = Faker()

        self.country_IN = CountryFactory.create(
            country="India", short_code="IN"
        )

        self.country_US = CountryFactory.create(
            country="United States", short_code="US"
        )

        self.product_IN = ProductFactory.create(
            country=self.country_IN, short_code="myopin"
        )

        self.product_US = ProductFactory.create(
            country=self.country_US, short_code="myopus"
        )

        # create product setting
        self.setting_key = "demo_key"
        self.product_setting = ProductSettingFactory.create(
            product=self.product_IN,
            setting_key=self.setting_key,
            setting_value="dummy text",
        )

        # create product default setting
        self.product_default_setting = ProductDefaultSettingFactory.create(
            setting_key=self.setting_key, setting_value="dummy text"
        )

    def test_get_country_code_success(self):
        country_code_IN = get_country_code(self.product_IN.id)
        self.assertEqual(country_code_IN, self.country_IN.short_code)

        country_code_US = get_country_code(self.product_US.id)
        self.assertEqual(country_code_US, self.country_US.short_code)

    def test_get_country_code_failure(self):
        country_code = get_country_code(1)
        self.assertFalse(country_code)

    def test_get_product_from_short_code(self):
        product_IN = get_product_from_short_code("myopin")
        assert product_IN.id == self.product_IN.id

        product_US = get_product_from_short_code("myopus")
        assert product_US.id == self.product_US.id

    def test_get_product_from_short_code_exception(self):
        with pytest.raises(Products.DoesNotExist):
            product_IN = get_product_from_short_code("invalid_short_code")
            assert product_IN.id == self.product_IN.id

    def test_get_product_default_setting_success(self):
        result = get_product_default_setting(self.setting_key)
        assert result.setting_key == self.product_default_setting.setting_key

    def test_get_product_default_setting_failure(self):
        setting_key = "non_existent_setting"
        result = get_product_default_setting(setting_key)
        assert result is None

    def test_get_product_setting_success(self):
        result = get_product_setting(self.product_IN.id, self.setting_key)
        assert result.product.id == self.product_setting.product.id
        assert result.setting_key == self.product_setting.setting_key

    def test_get_product_setting_failure(self):
        setting_key = "non_existent_setting"
        result = get_product_setting(1, setting_key)
        assert result is None
