from factory import Faker, Sequence, SubFactory
from factory.django import DjangoModelFactory
from accounts.core.tests.factories import CountryFactory
from accounts.products.models import (
    OrgTypes,
    ProductFeatureProperties,
    ProductFeatures,
    Products,
    ProductDefaultRateSlabs,
    ProductSettings,
    ProductDefaultSettings,
)


class ProductFactory(DjangoModelFactory):
    name = Faker("name")
    product_code = Sequence(lambda n: n)
    short_code = Sequence(lambda n: n)
    brand_id = 1
    country = SubFactory(CountryFactory)
    logo = Faker("image_url")
    billing_mod = Faker("text", max_nb_chars=200)
    memcache_mod = Faker("text", max_nb_chars=200)
    credit_limit_mod = Faker("text", max_nb_chars=200)
    currency = "INR"
    currency_symbol = Faker("hex_color")
    company_name = Faker("company")
    address = Faker("address")
    support_contact = Faker("phone_number")
    email_sender_id_billing = Faker("email")
    sms_sender_id_billing = Faker("text", max_nb_chars=10)
    email_sender_id_cr = Faker("email")
    sms_sender_id_cr = Faker("text", max_nb_chars=10)
    kyc_required = Faker("random_int", min=0, max=1)
    recurring_setup = Faker("random_element", elements=["off", "on"])

    class Meta:
        model = Products


class ProductFeatureFactory(DjangoModelFactory):

    product = SubFactory(ProductFactory)
    name = Faker("word")
    unit = Faker("word")
    billing_type = "N"
    resource_key = Faker("word")
    memcache_key = Faker("word")
    is_highlighted = Faker("boolean")
    es_dependent = True
    api_call = False

    class Meta:
        model = ProductFeatures


class ProductFeaturePropertyFactory(DjangoModelFactory):

    product_feature = SubFactory(ProductFeatureFactory)
    name = Faker("word")
    property = Faker("word")
    post_fix_resource = Faker("word")
    post_fix_memcache = Faker("word")

    class Meta:
        model = ProductFeatureProperties


class OrgTypesFactory(DjangoModelFactory):
    product = SubFactory(ProductFactory)
    name = Faker("name")
    category = Faker("random_int", min=1, max=2)
    status = Faker("random_int", min=0, max=1)

    class Meta:
        model = OrgTypes


class ProductDefaultRateSlabsFactory(DjangoModelFactory):
    product_feature = SubFactory(ProductFeatureFactory)
    product_feature_property = SubFactory(ProductFeaturePropertyFactory)
    country_code = "IN"
    min = Faker("pyint")
    max = Faker("pyint", min_value=0, max_value=1000000)
    rate = Faker("pyfloat", right_digits=3)

    class Meta:
        model = ProductDefaultRateSlabs


class ProductSettingFactory(DjangoModelFactory):
    product = SubFactory(ProductFactory)
    setting_key = Faker("uuid4")
    setting_value = Faker("text")
    status = 1

    class Meta:
        model = ProductSettings


class ProductDefaultSettingFactory(DjangoModelFactory):
    setting_key = Faker("uuid4")
    setting_value = Faker("text")

    class Meta:
        model = ProductDefaultSettings
