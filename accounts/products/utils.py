import logging
from django.db import models
from django.db.models import Q
from accounts.products.models import (
    Products,
    ProductSettings,
    ProductDefaultSettings,
    ProductDefaultRateSlabs,
)
from django.db.models import F
from typing import Optional

logger = logging.getLogger(__name__)


def get_country_code(id):
    country_short_code = (
        Products.objects.filter(Q(id=id))
        .annotate(cnty_short_code=models.F("country_id__short_code"))
        .values("cnty_short_code")
    )

    logger.title("query #get_country_code").info(str(country_short_code.query))

    if not country_short_code.exists():
        return False

    return country_short_code[0]["cnty_short_code"]


def get_product_from_short_code(short_code):
    return Products.objects.get(short_code=short_code)


def get_product_details(product_id):
    return Products.objects.filter(id=product_id).first()


def get_product_default_setting(setting_key):
    return ProductDefaultSettings.objects.key(setting_key).first()


def get_product_setting(product_id, setting_key):
    return ProductSettings.active.product(product_id).key(setting_key).first()


def get_country_from_product_id(product_id):
    queryset = (
        Products.objects.annotate(
            product_country_id=F("country__id"),
        )
        .filter(id=product_id)
        .first()
    )

    return queryset


def get_default_product_rate_slab(
    product_feature_id: str, product_feature_property_id: Optional[str] = None
) -> ProductDefaultRateSlabs:

    filter_args = {
        "product_feature_id": product_feature_id,
    }
    if product_feature_property_id is not None:
        filter_args["product_feature_property_id"] = product_feature_property_id

    query_set = ProductDefaultRateSlabs.objects.filter(**filter_args)

    logger.title("Query #get_default_product_rate_slab").info(query_set.query)

    return query_set.first()
