from django.db import models

from accounts.core.models import Countries, DocTypes
from accounts.utils.common import uuid
from .enums import ProductFeatureTypeEnum, ProductFeatureStatusEnum

from . import managers


class Products(models.Model):
    id = models.CharField(max_length=36, primary_key=True, default=uuid)
    name = models.CharField(max_length=250)
    product_code = models.IntegerField(
        help_text="1: New myoperator India, 2: Old myoperator india, 3: New myoperator US"
    )
    short_code = models.CharField(max_length=20)
    brand_id = models.IntegerField(default=1)
    country = models.ForeignKey(
        Countries,
        on_delete=models.CASCADE,
    )
    logo = models.CharField(max_length=250)
    billing_mod = models.Char<PERSON>ield(max_length=200)
    memcache_mod = models.CharField(max_length=200)
    credit_limit_mod = models.CharField(max_length=200)
    currency = models.CharField(max_length=3, default="INR")
    currency_symbol = models.Char<PERSON><PERSON>(max_length=10)
    company_name = models.CharField(max_length=250, null=True, blank=True)
    address = models.CharField(max_length=250)
    support_contact = models.CharField(max_length=50)
    email_sender_id_billing = models.CharField(max_length=250)
    sms_sender_id_billing = models.CharField(max_length=10)
    email_sender_id_cr = models.CharField(max_length=250)
    sms_sender_id_cr = models.CharField(max_length=10)
    kyc_required = models.SmallIntegerField(default=1)
    recurring_setup = models.CharField(
        max_length=3,
        choices=(("off", "off"), ("on", "on")),
        default="off",
        help_text="Recurring setup 0=> off, 1=> on",
    )
    status = models.SmallIntegerField(default=1)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = managers.ProductManager()

    class Meta:
        db_table = "product"
        indexes = [
            models.Index(fields=["country_id"]),
            models.Index(fields=["status"]),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=["product_code"], name="product_code"
            ),
        ]

    def __eq__(self, other):
        if isinstance(other, str):
            return self.id == other
        elif isinstance(other, Products):
            return self.id == other.product.id
        else:
            return False


class OrgTypes(models.Model):
    id = models.AutoField(primary_key=True)
    product = models.ForeignKey(Products, on_delete=models.CASCADE)
    name = models.CharField(max_length=250)
    category = models.IntegerField(
        default=1, help_text="1=> individual, 2=> organization"
    )
    status = models.SmallIntegerField(default=1)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "org_types"
        indexes = [
            models.Index(fields=["product_id"]),
            models.Index(fields=["name"]),
            models.Index(fields=["status"]),
        ]


class OrgDocMaps(models.Model):
    id = models.AutoField(primary_key=True)
    org_type = models.ForeignKey(OrgTypes, on_delete=models.CASCADE)
    doc_type = models.ForeignKey(DocTypes, on_delete=models.CASCADE)
    proof_type = models.IntegerField(
        help_text="1=> ID Proof, 2 => address Proof"
    )
    status = models.SmallIntegerField()
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "org_doc_maps"
        indexes = [
            models.Index(fields=["org_type_id"]),
            models.Index(fields=["doc_type_id"]),
            models.Index(fields=["proof_type"]),
            models.Index(fields=["status"]),
        ]


class ProductFeatures(models.Model):
    id = models.CharField(max_length=36, primary_key=True, default=uuid)
    product = models.ForeignKey(Products, on_delete=models.CASCADE)
    name = models.CharField(max_length=200)
    unit = models.CharField(max_length=30)
    billing_type = models.CharField(max_length=3, default="N")
    resource_key = models.CharField(max_length=200)
    memcache_key = models.CharField(max_length=200)
    is_highlighted = models.SmallIntegerField(default=0)
    type = models.IntegerField(default=ProductFeatureTypeEnum.BILLABLE.value)
    es_dependent = models.SmallIntegerField(default=0)
    api_call = models.SmallIntegerField(default=0)
    status = models.SmallIntegerField(default=ProductFeatureStatusEnum.ACTIVE.value)
    is_paid = models.BooleanField(default=False)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = managers.ProductFeatureManager()
    active = managers.ActiveProductFeatureManager()

    class Meta:
        db_table = "product_features"
        indexes = [
            models.Index(fields=["product_id", "status"]),
            models.Index(fields=["status"]),
            models.Index(fields=["is_highlighted"]),
            models.Index(fields=["memcache_key"]),
        ]


class ProductFeatureProperties(models.Model):
    id = models.CharField(max_length=36, primary_key=True, default=uuid)
    product_feature = models.ForeignKey(
        ProductFeatures, on_delete=models.CASCADE
    )
    name = models.CharField(max_length=100)
    property = models.CharField(max_length=10)
    post_fix_resource = models.CharField(max_length=10)
    post_fix_memcache = models.CharField(max_length=10)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = managers.ProductFeaturePropertyManager()

    class Meta:
        db_table = "product_feature_properties"
        indexes = [
            models.Index(fields=["product_feature_id"]),
        ]


class ProductDefaultRateSlabs(models.Model):
    id = models.CharField(max_length=36, primary_key=True)
    product_feature = models.ForeignKey(
        ProductFeatures, on_delete=models.CASCADE, null=True, blank=True
    )
    product_feature_property = models.ForeignKey(
        ProductFeatureProperties,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    country_code = models.CharField(max_length=10, default="IN")
    min = models.IntegerField(default=0)
    max = models.IntegerField(default=1000000)
    rate = models.FloatField(default=0.000)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = managers.ProductDefaultRateSlabManager()

    class Meta:
        db_table = "product_default_rate_slabs"
        indexes = [
            models.Index(
                fields=["product_feature_id", "product_feature_property_id"]
            ),
        ]


class ProductSettings(models.Model):
    id = models.AutoField(primary_key=True)
    product = models.ForeignKey(Products, on_delete=models.CASCADE)
    setting_key = models.CharField(max_length=36)
    setting_value = models.TextField()
    status = models.SmallIntegerField(default=1)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = managers.ProductSettingManager()
    active = managers.ActiveProductSettingManager()

    class Meta:
        db_table = "product_settings"


class ProductDefaultSettings(models.Model):
    id = models.AutoField(primary_key=True)
    setting_key = models.CharField(max_length=36)
    setting_value = models.TextField()
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = managers.ProductDefaultSettingManager()

    class Meta:
        db_table = "product_default_settings"
