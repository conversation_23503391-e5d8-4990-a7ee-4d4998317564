from rest_framework import serializers
from accounts.products.models import (
    Products,
    ProductFeatures,
    ProductDefaultRateSlabs,
)
from accounts.products.enums import ProductStatusEnum


class ProductDefaultRateSlabSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductDefaultRateSlabs
        fields = ["id", "rate"]


class ProductFeatureSerializer(serializers.ModelSerializer):
    product_id = serializers.PrimaryKeyRelatedField(
        source="product", read_only=True
    )
    status = serializers.SerializerMethodField()
    created = serializers.DateTimeField(read_only=True)

    def get_status(self, obj: ProductFeatures):
        return ProductStatusEnum.get_name(obj.status).lower()

    class Meta:
        model = ProductFeatures
        fields = (
            "id",
            "product_id",
            "name",
            "unit",
            "billing_type",
            "resource_key",
            "memcache_key",
            "type",
            "is_highlighted",
            "status",
            "created",
        )


class ProductSerializer(serializers.ModelSerializer):
    created = serializers.DateTimeField(read_only=True)
    modified = serializers.DateTimeField(read_only=True)

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["status"] = ProductStatusEnum.get_name(
            instance.status
        ).lower()
        return representation

    class Meta:
        model = Products
        fields = (
            "id",
            "name",
            "product_code",
            "short_code",
            "country_id",
            "status",
            "created",
            "modified",
        )

    # Other field would be ondemand, eg: /products?include=logo,currency,currency_symbol,...
