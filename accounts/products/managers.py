from django.db import models

from .queryset import (
    ProductQuerySet,
    ProductFeatureQuerySet,
    ProductFeaturePropertyQuerySet,
    ProductDefaultRateSlabQuerySet,
    ProductSettingQuerySet,
    ProductDefaultSettingQuerySet,
)


class ProductManager(models.Manager):
    def get_queryset(self):
        return ProductQuerySet(self.model, using=self._db)


class ProductFeatureManager(models.Manager):
    def get_queryset(self):
        return ProductFeatureQuerySet(self.model, using=self._db)

    def product(self, product_id):
        return self.get_queryset().product(product_id)


class ActiveProductFeatureManager(ProductFeatureManager):
    def product(self, product_id):
        return super().get_queryset().active_product(product_id)


class ProductFeaturePropertyManager(models.Manager):
    def get_queryset(self):
        return ProductFeaturePropertyQuerySet(self.model, using=self._db)

    def product_feature(self, product_feature_id):
        return self.get_queryset().product_feature(product_feature_id)


class ProductDefaultRateSlabManager(models.Manager):
    def get_queryset(self):
        return ProductDefaultRateSlabQuerySet(self.model, using=self._db)

    def product_feature(self, product_feature_id):
        return self.get_queryset().product_feature(product_feature_id)

    def min(self, min_value):
        return self.get_queryset().min(min_value)

    def max(self, max_value):
        return self.get_queryset().max(max_value)


class ProductSettingManager(models.Manager):
    def get_queryset(self):
        return ProductSettingQuerySet(self.model, using=self._db)

    def product(self, product_id):
        return self.get_queryset().product(product_id)

    def key(self, key):
        return self.get_queryset().key(key)


class ActiveProductSettingManager(ProductSettingManager):
    def product(self, product_id):
        return super().get_queryset().active_product(product_id)

    def key(self, key):
        return self.get_queryset().key(key)


class ProductDefaultSettingManager(models.Manager):
    def get_queryset(self):
        return ProductDefaultSettingQuerySet(self.model, using=self._db)

    def key(self, key):
        return self.get_queryset().key(key)
