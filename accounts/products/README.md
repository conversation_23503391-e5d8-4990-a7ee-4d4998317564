### Model Managers

Query to fetch features of a product
```
ProductFeatures.objects.product('58ee66511dba5458').all()
select * from product_features where product_id='58ee66511dba5458';
```

Query to fetch active features of a product
```
ProductFeatures.active.product('58ee66511dba5458').all()
select * from product_features where product_id='58ee66511dba5458' and status=1;
```

Query to fetch feature properties of product
```
ProductFeatureProperties.objects.product_feature('5ea94821e6957268').all()
select * from product_feature_properties where product_feature_id='5ea94821e6957268';
```

Query to fetch default rate slabs of a product feature
```
ProductFeatureDefaultRateSlabs.objects.product_feature('5ea94821e6957268').all()
select * from product_default_rate_slabs where product_feature_id='5ea94821e6957268';
```

Query to fetch default rate slabs of a product feature with min and max value range
```
ProductFeatureDefaultRateSlabs.objects.product_feature('5ea94821e6957268').min(0).max(100).all()
select * from product_default_rate_slabs where product_feature_id='5ea94821e6957268' and min >= 0 and max <= 100;
```

Query to fetch key settings of a product
```
ProductSettings.objects.product('58ee66511dba5458').key('ABC_123')
select * from product_settings where product_id='58ee66511dba5458' and setting_key='ABC_123' LIMIT 1;
```

Query to fetch active key settings of a product
```
ProductSettings.active.product('58ee66511dba5458').key('ABC_123')
select * from product_settings where product_id='58ee66511dba5458' and setting_key='ABC_123' and status=1 LIMIT 1;
```

Query to fetch default key settings
```
ProductSettings.objects.key('ABC_123')
select * from product_default_settings where setting_key='ABC_123';
```
