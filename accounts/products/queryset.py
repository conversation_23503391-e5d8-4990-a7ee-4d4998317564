from django.db import models


class ProductQuerySet(models.QuerySet):
    pass


class ProductFeatureQuerySet(models.QuerySet):
    def product(self, product_id):
        return self.filter(product_id=product_id)

    def active_product(self, product_id):
        return self.filter(product_id=product_id, status=1)


class ProductFeaturePropertyQuerySet(models.QuerySet):
    def product_feature(self, product_feature_id):
        return self.filter(product_feature_id=product_feature_id)


class ProductDefaultRateSlabQuerySet(models.QuerySet):
    def product_feature(self, product_feature_id):
        return self.filter(product_feature_id=product_feature_id)

    def min(self, min_value):
        return self.filter(min__gte=min_value)

    def max(self, max_value):
        return self.filter(max__lte=max_value)


class ProductSettingQuerySet(models.QuerySet):
    def product(self, product_id):
        return self.filter(product_id=product_id)

    def key(self, key):
        return self.filter(setting_key=key)

    def active_product(self, product_id):
        return self.filter(product_id=product_id, status=1)


class ProductDefaultSettingQuerySet(models.QuerySet):
    def key(self, key):
        return self.filter(setting_key=key)
