# Generated by Django 3.2.18 on 2025-03-19 11:54

import accounts.utils.common
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductDefaultSettings',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('setting_key', models.CharField(max_length=36)),
                ('setting_value', models.TextField()),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'product_default_settings',
            },
        ),
        migrations.CreateModel(
            name='Products',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, max_length=36, primary_key=True, serialize=False)),
                ('name', models.Char<PERSON>ield(max_length=250)),
                ('product_code', models.IntegerField(help_text='1: New myoperator India, 2: Old myoperator india, 3: New myoperator US')),
                ('short_code', models.CharField(max_length=20)),
                ('brand_id', models.IntegerField(default=1)),
                ('logo', models.CharField(max_length=250)),
                ('billing_mod', models.CharField(max_length=200)),
                ('memcache_mod', models.CharField(max_length=200)),
                ('credit_limit_mod', models.CharField(max_length=200)),
                ('currency', models.CharField(default='INR', max_length=3)),
                ('currency_symbol', models.CharField(max_length=10)),
                ('company_name', models.CharField(blank=True, max_length=250, null=True)),
                ('address', models.CharField(max_length=250)),
                ('support_contact', models.CharField(max_length=50)),
                ('email_sender_id_billing', models.CharField(max_length=250)),
                ('sms_sender_id_billing', models.CharField(max_length=10)),
                ('email_sender_id_cr', models.CharField(max_length=250)),
                ('sms_sender_id_cr', models.CharField(max_length=10)),
                ('kyc_required', models.SmallIntegerField(default=1)),
                ('recurring_setup', models.CharField(choices=[('off', 'off'), ('on', 'on')], default='off', help_text='Recurring setup 0=> off, 1=> on', max_length=3)),
                ('status', models.SmallIntegerField(default=1)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('country', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.countries')),
            ],
            options={
                'db_table': 'product',
            },
        ),
        migrations.CreateModel(
            name='ProductSettings',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('setting_key', models.CharField(max_length=36)),
                ('setting_value', models.TextField()),
                ('status', models.SmallIntegerField(default=1)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.products')),
            ],
            options={
                'db_table': 'product_settings',
            },
        ),
        migrations.CreateModel(
            name='ProductFeatures',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, max_length=36, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('unit', models.CharField(max_length=30)),
                ('billing_type', models.CharField(default='N', max_length=3)),
                ('resource_key', models.CharField(max_length=200)),
                ('memcache_key', models.CharField(max_length=200)),
                ('is_highlighted', models.SmallIntegerField(default=0)),
                ('type', models.IntegerField(default=1)),
                ('es_dependent', models.SmallIntegerField(default=0)),
                ('api_call', models.SmallIntegerField(default=0)),
                ('status', models.SmallIntegerField(default=1)),
                ('is_paid', models.BooleanField(default=False)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.products')),
            ],
            options={
                'db_table': 'product_features',
            },
        ),
        migrations.CreateModel(
            name='ProductFeatureProperties',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, max_length=36, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('property', models.CharField(max_length=10)),
                ('post_fix_resource', models.CharField(max_length=10)),
                ('post_fix_memcache', models.CharField(max_length=10)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('product_feature', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.productfeatures')),
            ],
            options={
                'db_table': 'product_feature_properties',
            },
        ),
        migrations.CreateModel(
            name='ProductDefaultRateSlabs',
            fields=[
                ('id', models.CharField(max_length=36, primary_key=True, serialize=False)),
                ('country_code', models.CharField(default='IN', max_length=10)),
                ('min', models.IntegerField(default=0)),
                ('max', models.IntegerField(default=1000000)),
                ('rate', models.FloatField(default=0.0)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('product_feature', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='products.productfeatures')),
                ('product_feature_property', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='products.productfeatureproperties')),
            ],
            options={
                'db_table': 'product_default_rate_slabs',
            },
        ),
        migrations.CreateModel(
            name='OrgTypes',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=250)),
                ('category', models.IntegerField(default=1, help_text='1=> individual, 2=> organization')),
                ('status', models.SmallIntegerField(default=1)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.products')),
            ],
            options={
                'db_table': 'org_types',
            },
        ),
        migrations.CreateModel(
            name='OrgDocMaps',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('proof_type', models.IntegerField(help_text='1=> ID Proof, 2 => address Proof')),
                ('status', models.SmallIntegerField()),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('doc_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.doctypes')),
                ('org_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.orgtypes')),
            ],
            options={
                'db_table': 'org_doc_maps',
            },
        ),
        migrations.AddIndex(
            model_name='products',
            index=models.Index(fields=['country_id'], name='product_country_336af1_idx'),
        ),
        migrations.AddIndex(
            model_name='products',
            index=models.Index(fields=['status'], name='product_status_407ffb_idx'),
        ),
        migrations.AddConstraint(
            model_name='products',
            constraint=models.UniqueConstraint(fields=('product_code',), name='product_code'),
        ),
        migrations.AddIndex(
            model_name='productfeatures',
            index=models.Index(fields=['product_id', 'status'], name='product_fea_product_a3016b_idx'),
        ),
        migrations.AddIndex(
            model_name='productfeatures',
            index=models.Index(fields=['status'], name='product_fea_status_c6d265_idx'),
        ),
        migrations.AddIndex(
            model_name='productfeatures',
            index=models.Index(fields=['is_highlighted'], name='product_fea_is_high_c745df_idx'),
        ),
        migrations.AddIndex(
            model_name='productfeatures',
            index=models.Index(fields=['memcache_key'], name='product_fea_memcach_2e9b31_idx'),
        ),
        migrations.AddIndex(
            model_name='productfeatureproperties',
            index=models.Index(fields=['product_feature_id'], name='product_fea_product_3df7d8_idx'),
        ),
        migrations.AddIndex(
            model_name='productdefaultrateslabs',
            index=models.Index(fields=['product_feature_id', 'product_feature_property_id'], name='product_def_product_e8eb76_idx'),
        ),
        migrations.AddIndex(
            model_name='orgtypes',
            index=models.Index(fields=['product_id'], name='org_types_product_3396e3_idx'),
        ),
        migrations.AddIndex(
            model_name='orgtypes',
            index=models.Index(fields=['name'], name='org_types_name_2df4c4_idx'),
        ),
        migrations.AddIndex(
            model_name='orgtypes',
            index=models.Index(fields=['status'], name='org_types_status_09c8a3_idx'),
        ),
        migrations.AddIndex(
            model_name='orgdocmaps',
            index=models.Index(fields=['org_type_id'], name='org_doc_map_org_typ_a6df90_idx'),
        ),
        migrations.AddIndex(
            model_name='orgdocmaps',
            index=models.Index(fields=['doc_type_id'], name='org_doc_map_doc_typ_e967b3_idx'),
        ),
        migrations.AddIndex(
            model_name='orgdocmaps',
            index=models.Index(fields=['proof_type'], name='org_doc_map_proof_t_301366_idx'),
        ),
        migrations.AddIndex(
            model_name='orgdocmaps',
            index=models.Index(fields=['status'], name='org_doc_map_status_55bc38_idx'),
        ),
    ]
