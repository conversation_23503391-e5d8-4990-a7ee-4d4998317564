from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from accounts import error_codes
from accounts.products.serializers import (
    ProductSerializer,
    ProductFeatureSerializer,
)
from accounts.products.models import ProductFeatures
from rest_framework.exceptions import NotFound
import logging
from rest_framework import generics
from accounts.products.models import Products
from rest_framework import filters
from django_filters import rest_framework as django_filters
from accounts.products.filters import ProductFilter, ProductFeatureFilter
from accounts.pagination import CustomPagination
from accounts.exceptions import BaseException

logger = logging.getLogger(__name__)


class ProductListAPIView(generics.ListAPIView):
    serializer_class = ProductSerializer
    queryset = Products.objects.all()
    filter_backends = [
        filters.OrderingFilter,
        django_filters.DjangoFilterBackend,
    ]
    filterset_class = ProductFilter
    ordering_fields = ["created"]
    pagination_class = CustomPagination

    def get(self, request, *args, **kwargs):
        try:
            return super().get(request, *args, **kwargs)
        except BaseException as e:
            logger.title("Product List API Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class ProductFeatureRetrieveAPIView(generics.ListAPIView):
    serializer_class = ProductFeatureSerializer
    filter_backends = [
        filters.OrderingFilter,
        django_filters.DjangoFilterBackend,
    ]
    filterset_class = ProductFeatureFilter
    ordering_fields = ["created"]
    pagination_class = None

    def get_queryset(self):
        product = Products.objects.get(id=self.kwargs.get("pk"))
        return ProductFeatures.objects.filter(product=product)

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["request"] = self.request
        return context

    def get(self, request, *args, **kwargs):
        try:
            queryset = self.filter_queryset(self.get_queryset())
            serializer = self.get_serializer(queryset, many=True)
            return Response(
                {"status": "success", "message": "", "data": serializer.data}
            )
        except Products.DoesNotExist:
            raise NotFound(detail="Product not found", code=404)
        except BaseException as e:
            logger.title("Product feature Detail Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )
