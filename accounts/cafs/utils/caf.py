import logging

from accounts.cafs.models import Cafs
from typing import Optional

logger = logging.getLogger(__name__)


def is_caf_approved(billing_account_id: str) -> bool:
    try:
        cafs = Cafs.objects.get(billing_account_id=billing_account_id)
        return cafs.status == 1
    except Cafs.DoesNotExist:
        return False


def get_caf_data_kyc(billing_account_id: str) -> Optional[Cafs]:
    return Cafs.objects.filter(billing_account_id=billing_account_id).first()
