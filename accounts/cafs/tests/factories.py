from factory import Faker, SubFactory
from factory.django import DjangoModelFactory
from accounts.cafs.models import Cafs
from accounts.billing_accounts.tests.factories import BillingAccountFactory
from django.utils import timezone


class CafFactory(DjangoModelFactory):

    caf_number = Faker("numerify", text="##########")
    billing_account = SubFactory(BillingAccountFactory)
    deal_id = Faker("pystr", max_chars=30)
    support_agent = Faker("uuid4")
    sales_agent = Faker("uuid4")
    partner_id = Faker("uuid4")
    created_by = Faker("uuid4")
    resource = Faker("word", ext_word_list=None)
    status = 1
    tagged_agent_error = Faker("sentence")
    last_agent_sync_time = Faker(
        "date_time", tzinfo=timezone.get_current_timezone()
    )
    ocs_by_agent = Faker("random_element", elements=[0, 1])
    caf_doc_url = Faker("url")
    mode = Faker("random_element", elements=[0, 1, 2, 3])
    ekyc_verifier = Faker("word")

    class Meta:
        model = Cafs
