from django.test import TestCase


from accounts.billing_accounts.tests.factories import (
    BillingAccountFactory,
)
from accounts.cafs.utils.caf import is_caf_approved
from accounts.cafs.tests.factories import CafFactory


class TestCaf(TestCase):
    def test_is_caf_approved_true(self):
        billing_account = BillingAccountFactory.create()
        CafFactory.create(billing_account_id=billing_account.id)
        assert is_caf_approved(billing_account.id) is True

    def test_is_caf_approved_false(self):
        billing_account = BillingAccountFactory.create(
            status=0, verification_state=5
        )
        CafFactory.create(billing_account_id=billing_account.id, status=0)
        assert is_caf_approved(billing_account.id) is False

    def test_is_caf_approved_caf_404(self):
        assert is_caf_approved("123") is False
