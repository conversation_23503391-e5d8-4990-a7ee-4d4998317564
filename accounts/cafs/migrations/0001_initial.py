# Generated by Django 3.2.18 on 2025-03-19 11:54

import accounts.utils.common
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('billing_accounts', '0001_initial'),
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Cafs',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('caf_number', models.Char<PERSON>ield(max_length=250)),
                ('deal_id', models.Char<PERSON>ield(max_length=30)),
                ('support_agent', models.Char<PERSON>ield(help_text='support agent user profile id', max_length=36, null=True)),
                ('sales_agent', models.Char<PERSON><PERSON>(help_text='sales agent user profile id', max_length=36, null=True)),
                ('partner_id', models.Char<PERSON><PERSON>(max_length=36, null=True)),
                ('created_by', models.Char<PERSON><PERSON>(max_length=36, null=True)),
                ('resource', models.Char<PERSON>ield(max_length=30)),
                ('status', models.Integer<PERSON>ield()),
                ('tagged_agent_error', models.CharField(max_length=250, null=True)),
                ('last_agent_sync_time', models.DateTimeField(null=True)),
                ('ocs_by_agent', models.Integer<PERSON>ield(default=0, help_text='0 - OCSNEW  by customer\n1 -  OCSNEW created by agent')),
                ('caf_doc_url', models.CharField(max_length=250, null=True)),
                ('mode', models.IntegerField(default=0, help_text='0 - Other, 1 - Aadhar OTP, 2 - Kissflow, 3 - Auto-verify')),
                ('ekyc_verifier', models.CharField(max_length=100, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('billing_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing_accounts.billingaccounts')),
            ],
            options={
                'db_table': 'cafs',
            },
        ),
        migrations.CreateModel(
            name='CafNotes',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('message', models.TextField()),
                ('action', models.CharField(default='1', help_text='accept,reject,pending', max_length=64)),
                ('active_flag', models.IntegerField(default=1, help_text='0-not, 1-active')),
                ('created', models.DateTimeField(null=True)),
                ('modified', models.DateTimeField(null=True)),
                ('caf', models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, to='cafs.cafs')),
                ('user_profile', models.ForeignKey(help_text='id of logged in user', null=True, on_delete=django.db.models.deletion.CASCADE, to='users.userprofiles')),
            ],
            options={
                'db_table': 'caf_notes',
            },
        ),
        migrations.CreateModel(
            name='CafLogs',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('deal_id', models.BigIntegerField()),
                ('ban_id', models.CharField(max_length=50)),
                ('caf_no', models.CharField(max_length=250)),
                ('old_sales_agent', models.CharField(max_length=250, null=True)),
                ('new_sales_agent', models.CharField(max_length=250)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('caf', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='cafs.cafs')),
            ],
            options={
                'db_table': 'caf_logs',
            },
        ),
        migrations.AddIndex(
            model_name='cafs',
            index=models.Index(fields=['sales_agent'], name='cafs_sales_a_d8d90f_idx'),
        ),
        migrations.AddIndex(
            model_name='cafs',
            index=models.Index(fields=['billing_account_id', 'status'], name='cafs_billing_c83cf4_idx'),
        ),
        migrations.AddIndex(
            model_name='cafs',
            index=models.Index(fields=['status'], name='cafs_status_e882a0_idx'),
        ),
        migrations.AddIndex(
            model_name='cafnotes',
            index=models.Index(fields=['caf_id'], name='caf_notes_caf_id_2ae8b9_idx'),
        ),
    ]
