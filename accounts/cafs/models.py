from django.db import models

from accounts.billing_accounts.models import BillingAccounts
from accounts.users.models import UserProfiles
from accounts.utils.common import uuid


class Cafs(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    caf_number = models.CharField(max_length=250)
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE
    )
    deal_id = models.CharField(max_length=30)
    support_agent = models.CharField(
        max_length=36, null=True, help_text="support agent user profile id"
    )
    sales_agent = models.CharField(
        max_length=36, null=True, help_text="sales agent user profile id"
    )
    partner_id = models.Char<PERSON>ield(max_length=36, null=True)
    created_by = models.CharField(max_length=36, null=True)
    resource = models.Char<PERSON>ield(max_length=30)
    status = models.IntegerField()
    tagged_agent_error = models.Char<PERSON><PERSON>(max_length=250, null=True)
    last_agent_sync_time = models.DateTimeField(null=True)
    ocs_by_agent = models.IntegerField(
        default=0,
        help_text="0 - OCSNEW  by customer\n1 -  OCSNEW created by agent",
    )
    caf_doc_url = models.CharField(max_length=250, null=True)
    mode = models.IntegerField(
        default=0,
        help_text="0 - Other, 1 - Aadhar OTP, 2 - Kissflow, 3 - Auto-verify",
    )
    ekyc_verifier = models.CharField(max_length=100, null=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "cafs"
        indexes = [
            models.Index(fields=["sales_agent"]),
            models.Index(fields=["billing_account_id", "status"]),
            models.Index(fields=["status"]),
        ]


class CafLogs(models.Model):
    id = models.AutoField(primary_key=True)
    deal_id = models.BigIntegerField()
    ban_id = models.CharField(max_length=50)
    caf = models.ForeignKey(Cafs, on_delete=models.CASCADE)
    caf_no = models.CharField(max_length=250)
    old_sales_agent = models.CharField(max_length=250, null=True)
    new_sales_agent = models.CharField(max_length=250)
    created = models.DateTimeField(auto_now_add=True)

    objects = models.Manager()

    class Meta:
        db_table = "caf_logs"


class CafNotes(models.Model):
    id = models.AutoField(primary_key=True)
    caf = models.ForeignKey(
        Cafs, on_delete=models.CASCADE, null=True, default=None
    )
    message = models.TextField()
    action = models.CharField(
        max_length=64, default="1", help_text="accept,reject,pending"
    )
    user_profile = models.ForeignKey(
        UserProfiles,
        on_delete=models.CASCADE,
        null=True,
        help_text="id of logged in user",
    )
    active_flag = models.IntegerField(default=1, help_text="0-not, 1-active")
    created = models.DateTimeField(null=True)
    modified = models.DateTimeField(null=True)

    objects = models.Manager()

    class Meta:
        db_table = "caf_notes"
        indexes = [models.Index(fields=["caf_id"])]
