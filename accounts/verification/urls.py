from django.urls import path

from accounts.verification.views.aadhaar import <PERSON><PERSON><PERSON><PERSON><PERSON>erficationView
from accounts.verification.views.gst import GstVerficationView
from accounts.verification.views.uan import UanVerficationView
from accounts.verification.views.verification_list import (
    VerificationRulesListView,
)
from accounts.verification.views.verification_list_create import (
    RulesListCreateView,
)
from accounts.verification.views.verification_retrieve_update_destroy import (
    RuleRetrieveUpdateDestroyAPIView,
)

app_name = "verification_rules"

urlpatterns = [
    path(
        "<str:product>/<str:resource>/rules",
        view=VerificationRulesListView.as_view(),
        name="list_rule",
    ),
    path(
        "rules",
        view=RulesListCreateView.as_view(),
        name="create_list_rule",
    ),
    path(
        "rules/<str:rule_id>",
        view=RuleRetrieveUpdateDestroyAPIView.as_view(),
        name="edit_delete_rule",
    ),
    path(
        "aadhaar/<str:product>/verify",
        view=AadhaarVerficationView.as_view(),
        name="verify_limit_exceeded",
    ),
    path(
        "gst/<str:product>/verify",
        view=GstVerficationView.as_view(),
        name="gst_verify_limit_exceeded",
    ),
    path(
        "uan/<str:product>/verify",
        view=UanVerficationView.as_view(),
        name="uan_verify_limit_exceeded",
    ),
]
