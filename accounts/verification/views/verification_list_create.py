import logging

from django.db.models import Q

from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.serializers import ValidationError

from accounts import error_codes
from accounts.exceptions import BaseException
from accounts.products.models import Products
from accounts.verification.models import VerificationRules
from accounts.verification.serializers import (
    RuleCreateUpdateSerializer,
    RuleListSerializer,
    RuleSerializer,
)
from accounts.verification.utils.verification_rule import (
    create_rule,
    is_rule_exists,
)

logger = logging.getLogger(__name__)


class RulesListCreateView(generics.ListCreateAPIView):
    serializer_class = RuleSerializer

    def perform_create(self, serializer):
        return create_rule(
            rule=serializer.data["rule"],
            product_id=serializer.data["product_id"],
            resource=serializer.data["resource"],
            resource_id=serializer.data["resource_id"],
            allowed=serializer.data["allowed"],
            time=serializer.data["time"],
        )

    def get_queryset(self, params):
        filter_args = Q()

        if params["product_id"] is not None:
            filter_args &= Q(product_id=params["product_id"])
        if params["resource"] is not None:
            filter_args &= Q(resource=params["resource"])
        if params["resource_id"] is not None:
            filter_args &= Q(resource_id=params["resource_id"])
        if params["rule"] is not None:
            filter_args &= Q(rule=params["rule"])
        if params["status"] is not None:
            filter_args &= Q(status=params["status"])

        queryset = (
            VerificationRules.objects.filter(filter_args)
            .all()
            .order_by("-" + params["sort"])
        )
        if params["order"] == "asc":
            queryset = (
                VerificationRules.objects.filter(filter_args)
                .all()
                .order_by(params["sort"])
            )

        return queryset.all()

    def post(self, request, *args, **kwargs):
        serializer = RuleCreateUpdateSerializer(data=request.data)
        try:
            if serializer.is_valid(raise_exception=True):
                # check if rule exists
                if is_rule_exists(
                    product_id=serializer.data["product_id"],
                    rule=serializer.data["rule"],
                    resource=serializer.data["resource"],
                    allowed=serializer.data["allowed"],
                    seconds=serializer.data["time"],
                    resource_id=serializer.data["resource_id"],
                ):
                    raise ValidationError("Rule already exists")

                rule = self.perform_create(serializer)
                data = {"id": rule.id}
                return Response(
                    {
                        "message": "Rule created",
                        "data": data,
                    },
                    status.HTTP_201_CREATED,
                )
        except ValidationError as e:
            return Response(
                {
                    "code": error_codes.VALIDATION_ERROR,
                    "errors": e.detail,
                },
                status.HTTP_400_BAD_REQUEST,
            )
        except BaseException as e:
            logger.title("Rule Creation Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )

    def list(self, request):
        serializer = RuleListSerializer(data=request.query_params)
        try:
            if serializer.is_valid(raise_exception=True):
                query_set = self.filter_queryset(
                    self.get_queryset(serializer.data)
                )
                data = self.paginate_queryset(query_set)
                serializer = self.get_serializer(data, many=True)
                return self.get_paginated_response(serializer.data)

        except ValidationError:
            return Response(
                {
                    "errors": serializer.errors,
                    "code": error_codes.VALIDATION_ERROR,
                },
                status.HTTP_400_BAD_REQUEST,
            )
        except BaseException as e:
            logger.title("Rule List Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )
