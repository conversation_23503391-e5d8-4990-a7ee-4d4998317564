import logging
from rest_framework.exceptions import NotFound
from rest_framework.response import Response
from rest_framework.views import APIView
from accounts.exceptions import BaseException
from accounts.products.models import Products
from accounts.products.utils import get_product_from_short_code
from accounts.verification.utils.uan_verification import UanVerification

logger = logging.getLogger(__name__)


class UanVerficationView(APIView):
    def get(self, request, *args, **kwargs):
        try:
            uan = request.query_params.get("uan")
            if not uan:
                raise NotFound(detail="missing param uan", code=404)

            product = get_product_from_short_code(kwargs["product"])
            is_valid, rule = UanVerification(product.id).validate(uan)
            return Response(
                {
                    "status": "success",
                    "message": "UAN Verification",
                    "data": {
                        "uan": uan,
                        "limit_exceeded": is_valid,
                        "reason": rule,
                    },
                }
            )
        except Products.DoesNotExist:
            raise NotFound(detail="invalid product code", code=404)
        except BaseException as e:
            logger.title("Verify Rule exceeded Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )
