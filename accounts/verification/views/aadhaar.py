import logging

from rest_framework.exceptions import NotFound
from rest_framework.response import Response
from rest_framework.views import APIView

from accounts.exceptions import BaseException
from accounts.products.models import Products
from accounts.products.utils import get_product_from_short_code
from accounts.utils.common import get_aadhaar_hash
from accounts.verification.utils.aadhaar_verification import AadhaarValidation

logger = logging.getLogger(__name__)


class AadhaarVerficationView(APIView):
    def get(self, request, *args, **kwargs):
        try:
            aadhaar_no = request.query_params.get("aadhaar_no")
            if not aadhaar_no:
                raise NotFound(detail="missing param aadhaar_no", code=404)

            product = get_product_from_short_code(kwargs["product"])
            is_valid, rule = AadhaarValidation(product.id).validate(aadhaar_no)
            return Response(
                {
                    "status": "success",
                    "message": "Aadhaar Verification",
                    "data": {
                        "aadhaar_hash": get_aadhaar_hash(aadhaar_no),
                        "limit_exceeded": is_valid,
                        "reason": rule,
                    },
                }
            )
        except Products.DoesNotExist:
            raise NotFound(detail="invalid product code", code=404)
        except BaseException as e:
            logger.title("Verify Rule exceeded Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )
