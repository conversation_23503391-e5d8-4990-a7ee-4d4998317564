import logging

from rest_framework import generics, status
from rest_framework.exceptions import NotFound
from rest_framework.response import Response
from rest_framework.serializers import ValidationError

from accounts import error_codes
from accounts.exceptions import BaseException
from accounts.verification.models import VerificationRules
from accounts.verification.serializers import (
    RuleCreateUpdateSerializer,
    RuleSerializer,
)
from accounts.verification.utils.verification_rule import edit_rule

logger = logging.getLogger(__name__)


class RuleRetrieveUpdateDestroyAPIView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = RuleSerializer

    def get_object(self):
        return VerificationRules.objects.get(id=self.kwargs["rule_id"])

    def perform_destroy(self, instance):
        return instance.inactive()

    def perform_update(self, serializer):
        return edit_rule(self.kwargs["rule_id"], serializer.data)

    def get(self, request, *args, **kwargs):
        try:
            data = self.get_object()
            serializer = self.get_serializer(data)
            return Response({"message": "Rule Data", "data": serializer.data})
        except VerificationRules.DoesNotExist:
            raise NotFound(
                detail="rule not found", code=status.HTTP_404_NOT_FOUND
            )
        except BaseException as e:
            logger.title("Rule Detail Error").error(e, exc_info=True)
            return Response(
                {
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )

    def put(self, request, *args, **kwargs):
        serializer = RuleCreateUpdateSerializer(data=request.data)
        try:
            if serializer.is_valid(raise_exception=True):
                self.perform_update(serializer)
                serializer = self.get_serializer(self.get_object())
                return Response(
                    {
                        "status": "success",
                        "message": "Rule Updated.",
                        "data": serializer.data,
                    }
                )
        except VerificationRules.DoesNotExist:
            raise NotFound(
                detail="rule not found", code=status.HTTP_404_NOT_FOUND
            )
        except ValidationError:
            return Response(
                {
                    "errors": serializer.errors,
                    "code": error_codes.VALIDATION_ERROR,
                },
                status.HTTP_400_BAD_REQUEST,
            )
        except BaseException as e:
            logger.title("Rule edit Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )

    def delete(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            self.perform_destroy(instance)
            return Response(
                {
                    "status": "success",
                    "code": status.HTTP_200_OK,
                    "message": "Rule deleted successfully.",
                },
                status.HTTP_200_OK,
            )
        except VerificationRules.DoesNotExist:
            raise NotFound(
                detail="rule not found", code=status.HTTP_404_NOT_FOUND
            )
        except BaseException as e:
            logger.title("Rule deletion Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )

    def patch(self, request, *args, **kwargs):
        serializer = RuleCreateUpdateSerializer(data=request.data, partial=True)
        try:
            if serializer.is_valid(raise_exception=True):
                instance = self.get_object()
                attributes = {
                    "product_id": instance.product.id,
                    "rule": instance.rule,
                    "resource": instance.resource,
                    "resource_id": instance.resource_id,
                    "allowed": instance.allowed,
                    "time": instance.seconds,
                    "status": instance.status,
                }
                validated_data = dict(serializer.validated_data)

                # merge both dictionaries
                updated_attributes = {**attributes, **validated_data}
                logger.title("Diff of attributes and validated_data").info(
                    updated_attributes
                )
                edit_rule(self.kwargs["rule_id"], updated_attributes)
                serializer = self.get_serializer(self.get_object())
                return Response(
                    {
                        "status": "success",
                        "message": "Rule updated successfully.",
                        "data": serializer.data,
                    }
                )
        except VerificationRules.DoesNotExist:
            raise NotFound(
                detail="rule not found", code=status.HTTP_404_NOT_FOUND
            )
        except ValidationError:
            return Response(
                {
                    "errors": serializer.errors,
                    "code": error_codes.VALIDATION_ERROR,
                },
                status.HTTP_400_BAD_REQUEST,
            )
        except ValueError as e:
            error_detail = str(e)
            return Response(
                {
                    "errors": {"message": error_detail},
                    "code": error_codes.VALIDATION_ERROR,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        except BaseException as e:
            logger.title("Rule active/inactive Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )
