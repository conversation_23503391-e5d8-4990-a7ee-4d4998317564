import logging

from rest_framework import status
from rest_framework.exceptions import NotFound
from rest_framework.response import Response
from rest_framework.views import APIView

from accounts import error_codes
from accounts.exceptions import BaseException
from accounts.products.models import Products
from accounts.products.utils import get_product_from_short_code
from accounts.verification.utils.verification_rule import get_verification_rules

logger = logging.getLogger(__name__)


class VerificationRulesListView(APIView):
    def get(self, request, *args, **kwargs):
        try:
            resource = kwargs["resource"]
            resource_id = request.query_params.get("resource_id", None)

            product = get_product_from_short_code(kwargs["product"])
            data = get_verification_rules(product.id, resource, resource_id)
            return Response(
                {
                    "message": "Rules",
                    "data": data,
                },
                status.HTTP_200_OK,
            )
        except Products.DoesNotExist:
            raise NotFound(detail="invalid product code", code=404)
        except BaseException as e:
            logger.title("Rule List Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )
