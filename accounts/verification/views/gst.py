import logging
from rest_framework.exceptions import NotFound
from rest_framework.response import Response
from rest_framework.views import APIView
from accounts.exceptions import BaseException
from accounts.products.models import Products
from accounts.products.utils import get_product_from_short_code
from accounts.verification.utils.gst_verification import GstVerification

logger = logging.getLogger(__name__)


class GstVerficationView(APIView):
    def get(self, request, *args, **kwargs):
        try:
            gst_no = request.query_params.get("gst_no")
            if not gst_no:
                raise NotFound(detail="missing param gst_no", code=404)

            product = get_product_from_short_code(kwargs["product"])
            is_valid, rule = GstVerification(product.id).validate(gst_no)
            return Response(
                {
                    "status": "success",
                    "message": "Gst Verification",
                    "data": {
                        "gst_no": gst_no,
                        "limit_exceeded": is_valid,
                        "reason": rule,
                    },
                }
            )
        except Products.DoesNotExist:
            raise NotFound(detail="invalid product code", code=404)
        except BaseException as e:
            logger.title("Verify Rule exceeded Error").error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )
