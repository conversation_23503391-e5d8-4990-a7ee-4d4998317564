import logging
from accounts.billing_accounts.utils.billing_account import (
    count_billing_accounts_by_uan,
    count_billing_accounts_by_uan_in_duration,
    count_fraud_account_by_uan,
    count_fraud_account_by_uan_in_duration,
)
from accounts.verification import constants
from accounts.verification.utils.verification_rule import (
    get_rules_queryset,
    get_date_range,
)

from .rules import MaxAllowed, MaxBlacklisted

logger = logging.getLogger(__name__)


class UanVerification:
    product_id = None
    resource = "uan"
    rules = (MaxAllowed, MaxBlacklisted)

    def __init__(self, product_id) -> None:
        self.product_id = product_id

    def validate(self, uan):
        errors = []
        if MaxAllowed in self.rules:
            if not self.check_max_allowed_rules(uan):
                errors.append(MaxAllowed.rule)

        if MaxBlacklisted in self.rules:
            if not self.check_max_blacklisted_rules(uan):
                errors.append(MaxBlacklisted.rule)

        logger.title("UAN Verify").info(f"Failed Rules: {errors}")
        return bool(errors), errors

    def _fetch_rules(self, rule, uan):
        queryset = get_rules_queryset(self.product_id, rule, self.resource, uan)
        return queryset.all()

    def check_max_allowed_rules(self, uan):
        for rule in self._fetch_rules(MaxAllowed.rule, uan):
            logger.title("UAN Verify").info(
                f"Rule: {MaxAllowed.rule}, Allowed: {rule.allowed}, Duration: {rule.seconds}"
            )
            if rule.allowed == constants.REJECT_ALL:
                logger.title("UAN Verify").info("Marked as Blocked")
                return False
            elif (
                rule.allowed == constants.ALLOW_UNLIMITED
                and rule.seconds is None
            ):
                logger.title("UAN Verify").info("Marked as Whitelisted")
                return True
            elif rule.seconds is None:
                cnt = count_billing_accounts_by_uan(uan)
                logger.title("UAN Verify").info(f"UAN Count: {cnt}")
                if cnt >= rule.allowed:
                    return False
            else:
                from_date, to_date = get_date_range(rule.seconds)
                cnt = count_billing_accounts_by_uan_in_duration(
                    uan, from_date, to_date
                )
                logger.title("UAN Verify").info(
                    f"UAN Count: {cnt}, Between: {from_date}, {to_date}"
                )
                if cnt >= rule.allowed:
                    return False
        return True

    def check_max_blacklisted_rules(self, uan):
        for rule in self._fetch_rules(MaxBlacklisted.rule, uan):
            logger.title("UAN Verify").info(
                f"Rule: {MaxBlacklisted.rule}, Allowed: {rule.allowed}, Duration: {rule.seconds}"
            )
            if rule.allowed == constants.REJECT_ALL:
                logger.title("UAN Verify").info("Marked as Blocked")
                return False
            elif (
                rule.allowed == constants.ALLOW_UNLIMITED
                and rule.seconds is None
            ):
                logger.title("UAN Verify").info("Marked as Whitelisted")
                return True
            elif rule.seconds is None:
                cnt = count_fraud_account_by_uan(uan)
                logger.title("UAN Verify").info(f"UAN Count: {cnt}")
                if cnt >= rule.allowed:
                    return False
            else:
                from_date, to_date = get_date_range(rule.seconds)
                cnt = count_fraud_account_by_uan_in_duration(
                    uan, from_date, to_date
                )
                logger.title("UAN Verify").info(
                    f"UAN Count: {cnt}, Between: {from_date}, {to_date}"
                )
                if cnt >= rule.allowed:
                    return False
        return True
