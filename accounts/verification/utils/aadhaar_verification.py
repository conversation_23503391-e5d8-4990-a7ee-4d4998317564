import logging
from datetime import <PERSON><PERSON><PERSON>

from django.utils import timezone

from accounts.billing_accounts.utils.service_contact import (
    count_fraud_account_by_aadhar,
    count_fraud_account_by_aadhar_in_duration,
    count_service_contacts_by_aadhar,
    count_service_contacts_by_aadhar_in_duration,
)
from accounts.verification import constants
from accounts.verification.models import VerificationRules
from accounts.verification.utils.verification_rule import get_rules_queryset

from .rules import MaxAllowed, MaxBlacklisted

logger = logging.getLogger(__name__)


class AadhaarValidation:
    product_id = None
    resource = "aadhaar"
    rules = (MaxAllowed, MaxBlacklisted)

    def __init__(self, product_id) -> None:
        self.product_id = product_id

    def validate(self, aadhaar_no):
        errors = []
        if MaxAllowed in self.rules:
            if not self.check_max_allowed_rules(aadhaar_no):
                errors.append(MaxAllowed.rule)

        if MaxBlacklisted in self.rules:
            if not self.check_max_blacklisted_rules(aadhaar_no):
                errors.append(MaxBlacklisted.rule)

        logger.title("Aadhaar Verify").info(f"Failed Rules: {errors}")
        return bool(errors), errors

    def _fetch_rules(self, rule, aadhaar_no):
        queryset = get_rules_queryset(
            self.product_id, rule, self.resource, aadhaar_no
        )
        return queryset.all()

    def check_max_allowed_rules(self, aadhaar_no):
        for rule in self._fetch_rules(MaxAllowed.rule, aadhaar_no):
            logger.title("Aadhaar Verify").info(
                f"Rule: {MaxAllowed.rule}, Allowed: {rule.allowed}, Duration: {rule.seconds}"
            )
            if rule.allowed == constants.REJECT_ALL:
                logger.title("Aadhaar Verify").info("Marked as Blocked")
                return False
            elif (
                rule.allowed == constants.ALLOW_UNLIMITED
                and rule.seconds is None
            ):
                logger.title("Aadhaar Verify").info("Marked as Whitelisted")
                return True
            elif rule.seconds is None:
                cnt = count_service_contacts_by_aadhar(aadhaar_no)
                logger.title("Aadhaar Verify").info(f"Aadhaar Count: {cnt}")
                if cnt >= rule.allowed:
                    return False
            else:
                from_date, to_date = self._date_range(rule.seconds)
                cnt = count_service_contacts_by_aadhar_in_duration(
                    aadhaar_no, from_date, to_date
                )
                logger.title("Aadhaar Verify").info(
                    f"Aadhaar Count: {cnt}, Between: {from_date}, {to_date}"
                )
                if cnt >= rule.allowed:
                    return False
        return True

    def check_max_blacklisted_rules(self, aadhaar_no):
        for rule in self._fetch_rules(MaxBlacklisted.rule, aadhaar_no):
            logger.title("Aadhaar Verify").info(
                f"Rule: {MaxBlacklisted.rule}, Allowed: {rule.allowed}, Duration: {rule.seconds}"
            )
            if rule.allowed == constants.REJECT_ALL:
                logger.title("Aadhaar Verify").info("Marked as Blocked")
                return False
            elif (
                rule.allowed == constants.ALLOW_UNLIMITED
                and rule.seconds is None
            ):
                logger.title("Aadhaar Verify").info("Marked as Whitelisted")
                return True
            elif rule.seconds is None:
                cnt = count_fraud_account_by_aadhar(aadhaar_no)
                logger.title("Aadhaar Verify").info(f"Aadhaar Count: {cnt}")
                if cnt >= rule.allowed:
                    return False
            else:
                from_date, to_date = self._date_range(rule.seconds)
                cnt = count_fraud_account_by_aadhar_in_duration(
                    aadhaar_no, from_date, to_date
                )
                logger.title("Aadhaar Verify").info(
                    f"Aadhaar Count: {cnt}, Between: {from_date}, {to_date}"
                )
                if cnt >= rule.allowed:
                    return False
        return True

    def _date_range(self, seconds):
        delta = timedelta(seconds=seconds)
        to_date = timezone.now()
        from_date = to_date - delta
        return from_date, to_date
