from accounts.verification.models import VerificationRules
from accounts.verification.constants import VERIFICATION_RULE_STATUS_ACTIVE


class BaseRule(object):
    def get_queryset(self):
        return VerificationRules.objects.filter(
            rule=self.rule, status=VERIFICATION_RULE_STATUS_ACTIVE
        )


class MaxAllowed(BaseRule):
    rule = "max_allowed"


class MaxBlacklisted(BaseRule):
    rule = "max_blacklisted"
