import logging
from django.utils import timezone
from datetime import timedelta
from accounts.products.models import Products
from accounts.utils.common import seconds_to_duration
from accounts.verification import constants
from accounts.verification.exceptions import RuleConflictException
from accounts.verification.models import VerificationRules

logger = logging.getLogger(__name__)


def concat_allowed_and_seconds(allowed, seconds):
    if seconds is not None and seconds > 0:
        return f"{allowed}/{seconds_to_duration(seconds)}"
    else:
        return f"{allowed}"


def create_rule(rule, product_id, resource, **kwrgs):
    allowed = kwrgs["allowed"]
    time = kwrgs["time"]

    # Validate allowed value and time
    validate_time(allowed, time)

    conflicting_rule_id = get_conflicting_rule(
        product_id=product_id,
        resource=resource,
        resource_id=kwrgs["resource_id"],
        rule=rule,
        allowed=allowed,
    )
    if conflicting_rule_id:
        logger.title("Create Rule Error").error(
            f"Conflicting Rule ID: {conflicting_rule_id}"
        )
        raise RuleConflictException()

    return VerificationRules.objects.create(
        rule=rule,
        product=Products.objects.get(id=product_id),
        resource=resource,
        resource_id=kwrgs.get("resource_id", None),
        value=concat_allowed_and_seconds(allowed, time),
    )


def edit_rule(id, data):
    obj = VerificationRules.objects.get(id=id)

    if not data:
        raise ValueError()

    allowed = data["allowed"]
    time = data["time"]

    # Validate allowed value and time
    validate_time(allowed, time)

    conflicting_rule_id = get_conflicting_rule(
        product_id=data["product_id"],
        resource=data["resource"],
        resource_id=data["resource_id"],
        allowed=allowed,
        rule=data["rule"],
    )
    if conflicting_rule_id and conflicting_rule_id != id:
        logger.title("Edit Rule Error").error(
            f"Conflicting Rule ID: {conflicting_rule_id}"
        )
        raise RuleConflictException()

    if obj.allowed != allowed or obj.seconds != time:
        obj.value = concat_allowed_and_seconds(allowed, time)

    if obj.rule != data["rule"]:
        obj.rule = data["rule"]

    if obj.product.id != data["product_id"]:
        obj.product = Products.objects.get(id=data["product_id"])

    if obj.resource != data["resource"]:
        obj.resource = data["resource"]

    if obj.resource_id != data["resource_id"]:
        obj.resource_id = data["resource_id"]

    if "status" in data and obj.status != data["status"]:
        obj.status = data["status"]

    obj.save()
    return True


def validate_time(allowed, time):
    if (
        allowed in [constants.ALLOW_UNLIMITED, constants.REJECT_ALL]
        and time is not None
    ):
        raise ValueError(
            f"Duration can't be defined for allowed({allowed}), duration should be null"
        )
    return None


def get_conflicting_rule(product_id, resource, resource_id, **kwrgs):
    # check if rule exists when allowed is 0 or -1
    query = VerificationRules.objects.filter(
        product__id=product_id,
        resource=resource,
        resource_id=resource_id,
        rule=kwrgs["rule"],
        status=constants.VERIFICATION_RULE_STATUS_ACTIVE,
    )
    if kwrgs["allowed"] not in [
        constants.ALLOW_UNLIMITED,
        constants.REJECT_ALL,
    ]:
        # check if 0 or -1 rule is present then for new rule raise error
        query = query.filter(
            value__in=[constants.ALLOW_UNLIMITED, constants.REJECT_ALL]
        )
    conflicting_rule = query.first()
    if conflicting_rule:
        return conflicting_rule.id
    return None


def is_rule_exists(
    product_id, rule, resource, allowed, seconds, resource_id=None
):
    queryset = VerificationRules.objects.filter(
        product__id=product_id,
        resource=resource,
        rule=rule,
        value=f"{allowed}/{seconds_to_duration(seconds)}",
    )
    if resource_id:
        queryset = queryset.filter(resource_id=resource_id)

    return queryset.exists()


def get_rules_queryset(product_id, rule, resource, resource_id):
    queryset = VerificationRules.objects.filter(
        product__id=product_id,
        rule=rule,
        resource=resource,
        status=constants.VERIFICATION_RULE_STATUS_ACTIVE,
    )
    if resource_id is not None:
        resource_queryset = queryset.filter(resource_id=resource_id)
        logger.title(f"Rules Query for Resource ID: {resource_id}").info(
            resource_queryset.query
        )
        # if data does not exists with given resource_id
        # then fetch data without resource_id filter
        if resource_queryset.exists():
            return resource_queryset
    queryset = queryset.filter(resource_id__isnull=True)
    logger.title("Rules Query without Resource ID").info(queryset.query)
    return queryset


def get_default_rule_keys(product_id, resource):
    rule_keys = (
        VerificationRules.objects.filter(
            product__id=product_id,
            resource=resource,
            resource_id=None,
            status=constants.VERIFICATION_RULE_STATUS_ACTIVE,
        )
        .values_list("rule", flat=True)
        .distinct()
    )
    return rule_keys


def get_verification_rules(product_id, resource, resource_id):
    rules_dict = {}
    rule_keys = get_default_rule_keys(product_id, resource)
    for key in rule_keys:
        if key not in rules_dict:
            rules_dict[key] = []

        queryset = get_rules_queryset(product_id, key, resource, resource_id)
        for rule in queryset:
            rule_data = {"allowed": rule.allowed, "time": rule.seconds}
            rules_dict[key].append(rule_data)
    return rules_dict


def get_date_range(seconds):
    delta = timedelta(seconds=seconds)
    to_date = timezone.now()
    from_date = to_date - delta
    return from_date, to_date
