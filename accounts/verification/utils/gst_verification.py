import logging
from accounts.billing_accounts.utils.billing_account import (
    count_billing_accounts_by_gst,
    count_billing_accounts_by_gst_in_duration,
    count_fraud_account_by_gst,
    count_fraud_account_by_gst_in_duration,
)
from accounts.verification import constants
from accounts.verification.utils.verification_rule import (
    get_rules_queryset,
    get_date_range,
)

from .rules import MaxAllowed, MaxBlacklisted

logger = logging.getLogger(__name__)


class GstVerification:
    product_id = None
    resource = "gst"
    rules = (MaxAllowed, MaxBlacklisted)

    def __init__(self, product_id) -> None:
        self.product_id = product_id

    def validate(self, gst_no):
        errors = []
        if MaxAllowed in self.rules:
            if not self.check_max_allowed_rules(gst_no):
                errors.append(MaxAllowed.rule)

        if MaxBlacklisted in self.rules:
            if not self.check_max_blacklisted_rules(gst_no):
                errors.append(MaxBlacklisted.rule)

        logger.title("Gst Verify").info(f"Failed Rules: {errors}")
        return bool(errors), errors

    def _fetch_rules(self, rule, gst_no):
        queryset = get_rules_queryset(
            self.product_id, rule, self.resource, gst_no
        )
        return queryset.all()

    def check_max_allowed_rules(self, gst_no):
        for rule in self._fetch_rules(MaxAllowed.rule, gst_no):
            logger.title("Gst Verify").info(
                f"Rule: {MaxAllowed.rule}, Allowed: {rule.allowed}, Duration: {rule.seconds}"
            )
            if rule.allowed == constants.REJECT_ALL:
                logger.title("Gst Verify").info("Marked as Blocked")
                return False
            elif (
                rule.allowed == constants.ALLOW_UNLIMITED
                and rule.seconds is None
            ):
                logger.title("Gst Verify").info("Marked as Whitelisted")
                return True
            elif rule.seconds is None:
                cnt = count_billing_accounts_by_gst(gst_no)
                logger.title("Gst Verify").info(f"Gst Count: {cnt}")
                if cnt >= rule.allowed:
                    return False
            else:
                from_date, to_date = get_date_range(rule.seconds)
                cnt = count_billing_accounts_by_gst_in_duration(
                    gst_no, from_date, to_date
                )
                logger.title("Gst Verify").info(
                    f"Gst Count: {cnt}, Between: {from_date}, {to_date}"
                )
                if cnt >= rule.allowed:
                    return False
        return True

    def check_max_blacklisted_rules(self, gst_no):
        for rule in self._fetch_rules(MaxBlacklisted.rule, gst_no):
            logger.title("Gst Verify").info(
                f"Rule: {MaxBlacklisted.rule}, Allowed: {rule.allowed}, Duration: {rule.seconds}"
            )
            if rule.allowed == constants.REJECT_ALL:
                logger.title("Gst Verify").info("Marked as Blocked")
                return False
            elif (
                rule.allowed == constants.ALLOW_UNLIMITED
                and rule.seconds is None
            ):
                logger.title("Gst Verify").info("Marked as Whitelisted")
                return True
            elif rule.seconds is None:
                cnt = count_fraud_account_by_gst(gst_no)
                logger.title("Gst Verify").info(f"Gst Count: {cnt}")
                if cnt >= rule.allowed:
                    return False
            else:
                from_date, to_date = get_date_range(rule.seconds)
                cnt = count_fraud_account_by_gst_in_duration(
                    gst_no, from_date, to_date
                )
                logger.title("Gst Verify").info(
                    f"Gst Count: {cnt}, Between: {from_date}, {to_date}"
                )
                if cnt >= rule.allowed:
                    return False
        return True
