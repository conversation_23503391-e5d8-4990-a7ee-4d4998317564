from unittest.mock import Mock
from django.test import TestCase
from accounts.billing_accounts.tests.factories import BillingAccountFactory
from accounts.core.tests.factories import CountryFactory
from accounts.products.tests.factories import ProductFactory
from accounts.verification.models import VerificationRules
from accounts.verification.utils.gst_verification import GstVerification
from accounts.billing_accounts import constants


class TestGstVerification(TestCase):
    def setUp(self):
        self.country = CountryFactory.create()
        self.product = ProductFactory.create(
            country=self.country,
        )

    def test_max_allowed(self):
        # test with max_allowed = zero
        billing_account_one = BillingAccountFactory.create(
            ac_number="AC001",
            business_name="Test Business 1",
            status=1,
            gst_no="***************",
        )
        VerificationRules.objects.create(
            rule="max_allowed",
            product=self.product,
            resource="gst",
            resource_id=billing_account_one.gst_no,
            value="0",
        )
        assert (
            GstVerification(self.product.id).check_max_allowed_rules(
                billing_account_one.gst_no
            )
            is False
        )

        # test with max_allowed set as unlimited
        billing_account_two = BillingAccountFactory.create(
            ac_number="AC002",
            business_name="Test Business 2",
            status=1,
            gst_no="***************",
        )
        VerificationRules.objects.create(
            rule="max_allowed",
            product=self.product,
            resource="gst",
            resource_id=billing_account_two.gst_no,
            value="-1",
        )
        assert (
            GstVerification(self.product.id).check_max_allowed_rules(
                billing_account_two.gst_no
            )
            is True
        )

    def test_max_allowed_with_duration_none(self):
        # with count withtin max limit
        billing_account_one = BillingAccountFactory.create(
            ac_number="AC001",
            business_name="Test Business 1",
            status=1,
            gst_no="***************",
        )
        VerificationRules.objects.create(
            rule="max_allowed",
            product=self.product,
            resource="gst",
            resource_id=billing_account_one.gst_no,
            value="2",
        )
        assert (
            GstVerification(self.product.id).check_max_allowed_rules(
                billing_account_one.gst_no
            )
            is True
        )

        # with count exceeded
        billing_account_two = BillingAccountFactory.create(
            ac_number="AC002",
            business_name="Test Business 2",
            status=1,
            gst_no="***************",
        )
        VerificationRules.objects.create(
            rule="max_allowed",
            product=self.product,
            resource="gst",
            resource_id=billing_account_two.gst_no,
            value="0",
        )
        assert (
            GstVerification(self.product.id).check_max_allowed_rules(
                billing_account_two.gst_no
            )
            is False
        )

    def test_max_allowed_with_duration(self):
        # with count withtin limit
        billing_account_one = BillingAccountFactory.create(
            ac_number="AC001",
            business_name="Test Business 1",
            status=1,
            gst_no="***************",
        )
        VerificationRules.objects.create(
            rule="max_allowed",
            product=self.product,
            resource="gst",
            resource_id=billing_account_one.gst_no,
            value="2/1d",
        )
        assert (
            GstVerification(self.product.id).check_max_allowed_rules(
                billing_account_one.gst_no
            )
            is True
        )

        # with count exceeded
        billing_account_two = BillingAccountFactory.create(
            ac_number="AC002",
            business_name="Test Business 2",
            status=1,
            gst_no="***************",
        )
        assert (
            GstVerification(self.product.id).check_max_allowed_rules(
                billing_account_two.gst_no
            )
            is False
        )

    def test_check_max_blacklisted_rules(self):
        # test with max_allowed = zero
        billing_account_one = BillingAccountFactory.create(
            ac_number="AC001",
            business_name="Test Business 1",
            status=1,
            gst_no="***************",
            verification_state=constants.BILLING_VERIFICATION_STATE_FRAUD,
        )
        VerificationRules.objects.create(
            rule="max_blacklisted",
            product=self.product,
            resource="gst",
            resource_id=billing_account_one.gst_no,
            value="0",
        )
        assert (
            GstVerification(self.product.id).check_max_blacklisted_rules(
                billing_account_one.gst_no
            )
            is False
        )

        # test with max_allowed set as unlimited zero
        billing_account_two = BillingAccountFactory.create(
            ac_number="AC002",
            business_name="Test Business 2",
            status=1,
            gst_no="***************",
            verification_state=constants.BILLING_VERIFICATION_STATE_FRAUD,
        )
        VerificationRules.objects.create(
            rule="max_blacklisted",
            product=self.product,
            resource="gst",
            resource_id=billing_account_two.gst_no,
            value="-1",
        )
        assert (
            GstVerification(self.product.id).check_max_blacklisted_rules(
                billing_account_two.gst_no
            )
            is True
        )

    def test_check_max_blacklisted_rules_duration_none(self):
        # with count withtin max limit
        billing_account_one = BillingAccountFactory.create(
            ac_number="AC001",
            business_name="Test Business 1",
            status=1,
            gst_no="***************",
            verification_state=constants.BILLING_VERIFICATION_STATE_FRAUD,
        )
        VerificationRules.objects.create(
            rule="max_blacklisted",
            product=self.product,
            resource="gst",
            resource_id=billing_account_one.gst_no,
            value="2",
        )
        assert (
            GstVerification(self.product.id).check_max_blacklisted_rules(
                billing_account_one.gst_no
            )
            is True
        )

        # with count exceeded max limit
        billing_account_two = BillingAccountFactory.create(
            ac_number="AC002",
            business_name="Test Business 2",
            status=1,
            gst_no="***************",
            verification_state=constants.BILLING_VERIFICATION_STATE_FRAUD,
        )
        VerificationRules.objects.create(
            rule="max_blacklisted",
            product=self.product,
            resource="gst",
            resource_id=billing_account_two.gst_no,
            value="1",
        )
        assert (
            GstVerification(self.product.id).check_max_blacklisted_rules(
                billing_account_two.gst_no
            )
            is False
        )

    def test_check_max_blacklisted_rules_duration(self):
        # with count withtin max limit
        billing_account_one = BillingAccountFactory.create(
            ac_number="AC001",
            business_name="Test Business 1",
            status=1,
            gst_no="***************",
            verification_state=constants.BILLING_VERIFICATION_STATE_FRAUD,
        )
        VerificationRules.objects.create(
            rule="max_blacklisted",
            product=self.product,
            resource="gst",
            resource_id=billing_account_one.gst_no,
            value="2/1d",
        )
        assert (
            GstVerification(self.product.id).check_max_blacklisted_rules(
                billing_account_one.gst_no
            )
            is True
        )

        # with count exceeded max limit
        billing_account_two = BillingAccountFactory.create(
            ac_number="AC002",
            business_name="Test Business 2",
            status=1,
            gst_no="***************",
            verification_state=constants.BILLING_VERIFICATION_STATE_FRAUD,
        )
        assert (
            GstVerification(self.product.id).check_max_blacklisted_rules(
                billing_account_two.gst_no
            )
            is False
        )

    def test_validate_max_allowed(self):
        validator = GstVerification(self.product.id)
        validator.check_max_allowed_rules = Mock(return_value=True)
        result, rule = validator.validate("***************")
        assert result is False
        assert rule == []

    def test_validate_max_allowed_failure(self):
        validator = GstVerification(self.product.id)
        validator.check_max_allowed_rules = Mock(return_value=False)
        result, rule = validator.validate("***************")
        assert result is True
        assert rule == ["max_allowed"]

    def test_validate_max_blacklisted(self):
        validator = GstVerification(self.product.id)
        validator.check_max_allowed_rules = Mock(return_value=True)
        validator.check_max_blacklisted_rules = Mock(return_value=True)
        result, rule = validator.validate("***************")
        assert result is False
        assert rule == []

    def test_validate_max_blacklisted_failure(self):
        validator = GstVerification(self.product.id)
        validator.check_max_allowed_rules = Mock(return_value=True)
        validator.check_max_blacklisted_rules = Mock(return_value=False)
        result, rule = validator.validate("123456789012963")
        assert result is True
        assert rule == ["max_blacklisted"]
