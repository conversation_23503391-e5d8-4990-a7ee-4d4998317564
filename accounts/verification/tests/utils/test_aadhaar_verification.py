from datetime import timed<PERSON><PERSON>
from unittest.mock import Mock

from django.test import TestCase

from accounts.billing_accounts.models import ServiceContacts
from accounts.billing_accounts.tests.factories import BillingAccountFactory
from accounts.core.tests.factories import CountryFactory
from accounts.products.tests.factories import ProductFactory
from accounts.services.tests.factories import ServiceFactory
from accounts.verification.models import VerificationRules
from accounts.verification.utils.aadhaar_verification import AadhaarValidation


class TestAadhaarVerification(TestCase):
    def setUp(self):
        self.country = CountryFactory.create()
        self.product = ProductFactory.create(
            country=self.country,
        )
        self.billing_account = BillingAccountFactory.create(
            discount=None,
        )
        self.account = BillingAccountFactory.create(
            ac_number="KJHFF6",
            business_type="1",
            billing_property="3",
            discount=None,
        )
        self.account_2 = BillingAccountFactory.create(
            ac_number="BHDYEG",
            business_type="1",
            billing_property="3",
            discount=None,
        )

    def test_max_allowed_with_blocked_aadhaar(self):
        # test with max_allowed = zero
        VerificationRules.objects.create(
            rule="max_allowed",
            product=self.product,
            resource="aadhaar",
            resource_id="123",
            value="0",
        )
        assert (
            AadhaarValidation(self.product.id).check_max_allowed_rules("123")
            is False
        )

    def test_max_allowed_with_whitelisted_aadhaar(self):
        # test with max_allowed set as unlimited
        VerificationRules.objects.create(
            rule="max_allowed",
            product=self.product,
            resource="aadhaar",
            resource_id="456",
            value="-1",
        )
        assert (
            AadhaarValidation(self.product.id).check_max_allowed_rules("456")
            is True
        )

    def test_max_allowed_with_duration_none(self):
        # with count withtin max limit
        ServiceContacts.objects.create(
            billing_account=self.account,
            contact_type=1,
            mobile="**********",
            aadhar_card_number="111",
            status=1,
        )
        ServiceFactory.create(
            product=self.product,
            billing_account=self.account,
            user_profile=None,
            live_status=1,
            status=1,
        )
        VerificationRules.objects.create(
            rule="max_allowed",
            product=self.product,
            resource="aadhaar",
            resource_id="111",
            value="2",
        )
        result = AadhaarValidation(self.product.id).check_max_allowed_rules(
            "111"
        )
        assert result is True

        # with count exceeded
        ServiceContacts.objects.create(
            billing_account=self.account_2,
            contact_type=1,
            mobile="*********",
            aadhar_card_number="111",
            status=1,
        )
        ServiceFactory.create(
            product=self.product,
            billing_account=self.account_2,
            user_profile=None,
            live_status=1,
            status=0,
        )

        result2 = AadhaarValidation(self.product.id).check_max_allowed_rules(
            "111"
        )
        assert result2 is False

    def test_max_allowed_with_duration(self):
        # with count withtin limit
        ServiceContacts.objects.create(
            billing_account=self.account,
            contact_type=1,
            mobile="**********",
            aadhar_card_number="321",
            status=1,
        )
        ServiceFactory.create(
            product=self.product,
            billing_account=self.account,
            user_profile=None,
            live_status=1,
            status=0,
        )
        VerificationRules.objects.create(
            rule="max_allowed",
            product=self.product,
            resource="aadhaar",
            resource_id="321",
            value="2/1d",
        )
        assert (
            AadhaarValidation(self.product.id).check_max_allowed_rules("321")
            is True
        )

        # with count exceeded
        ServiceContacts.objects.create(
            billing_account=self.account_2,
            contact_type=1,
            mobile="*********",
            aadhar_card_number="321",
            status=1,
        )
        ServiceFactory.create(
            product=self.product,
            billing_account=self.account_2,
            user_profile=None,
            live_status=1,
            status=0,
        )
        assert (
            AadhaarValidation(self.product.id).check_max_allowed_rules("321")
            is False
        )

    def test_check_max_blacklisted_rules(self):
        # test with max_allowed = zero
        VerificationRules.objects.create(
            rule="max_blacklisted",
            product=self.product,
            resource="aadhaar",
            resource_id="999",
            value="0",
        )
        assert (
            AadhaarValidation(self.product.id).check_max_blacklisted_rules(
                "999"
            )
            is False
        )

        # test with max_allowed set as unlimited zero
        VerificationRules.objects.create(
            rule="max_blacklisted",
            product=self.product,
            resource="aadhaar",
            resource_id="888",
            value="-1",
        )
        assert (
            AadhaarValidation(self.product.id).check_max_blacklisted_rules(
                "888"
            )
            is True
        )

    def test_check_max_blacklisted_rules_duration_none(self):
        # with count withtin max limit
        billing_account = BillingAccountFactory.create(
            status=0, verification_state=5
        )
        ServiceFactory.create(
            product=self.product,
            billing_account=billing_account,
            user_profile=None,
            live_status=1,
            status=0,
        )

        ServiceContacts.objects.create(
            aadhar_card_number="111333",
            billing_account=billing_account,
            contact_type=1,
            mobile="**********",
            status=0,
        )

        VerificationRules.objects.create(
            rule="max_blacklisted",
            product=self.product,
            resource="aadhaar",
            resource_id="111333",
            value="2",
        )
        result = AadhaarValidation(self.product.id).check_max_blacklisted_rules(
            "111333"
        )
        assert result is True

        # with count exceeded max limit

        ServiceContacts.objects.create(
            aadhar_card_number="111333",
            billing_account=billing_account,
            contact_type=1,
            mobile="**********",
            status=0,
        )

        VerificationRules.objects.create(
            rule="max_blacklisted",
            product=self.product,
            resource="aadhaar",
            resource_id="111333",
            value="1",
        )
        result = AadhaarValidation(self.product.id).check_max_blacklisted_rules(
            "111333"
        )
        assert result is False

    def test_check_max_blacklisted_rules_duration(self):
        # with count withtin max limit
        billing_account = BillingAccountFactory.create(
            status=0, verification_state=5
        )
        ServiceFactory.create(
            product=self.product,
            billing_account=billing_account,
            user_profile=None,
            live_status=1,
            status=0,
        )

        ServiceContacts.objects.create(
            aadhar_card_number="111333",
            billing_account=billing_account,
            contact_type=1,
            mobile="**********",
            status=0,
        )

        VerificationRules.objects.create(
            rule="max_blacklisted",
            product=self.product,
            resource="aadhaar",
            resource_id="111333",
            value="2/1d",
        )
        result = AadhaarValidation(self.product.id).check_max_blacklisted_rules(
            "111333"
        )
        assert result is True

        # with count exceeded max limit
        ServiceContacts.objects.create(
            aadhar_card_number="111333",
            billing_account=billing_account,
            contact_type=1,
            mobile="**********",
            status=0,
        )

        VerificationRules.objects.create(
            rule="max_blacklisted",
            product=self.product,
            resource="aadhaar",
            resource_id="111333",
            value="1/1d",
        )
        result = AadhaarValidation(self.product.id).check_max_blacklisted_rules(
            "111333"
        )
        assert result is False

    # @pytest.fixture
    # def aadhaar_validator(self):
    #     validator = AadhaarValidation(product_id="test_product_id")
    #     validator.rules = [MaxAllowed, MaxBlacklisted]
    #     return validator

    def test_validate_max_allowed(self):
        validator = AadhaarValidation(self.product.id)
        validator.check_max_allowed_rules = Mock(return_value=True)
        result, rule = validator.validate("12345")
        assert result is False
        assert rule == []

    def test_validate_max_allowed_failure(self):
        validator = AadhaarValidation(self.product.id)
        validator.check_max_allowed_rules = Mock(return_value=False)
        result, rule = validator.validate("12345")
        assert result is True
        assert rule == ["max_allowed"]

    def test_validate_max_blacklisted(self):
        validator = AadhaarValidation(self.product.id)
        validator.check_max_allowed_rules = Mock(return_value=True)
        validator.check_max_blacklisted_rules = Mock(return_value=True)
        result, rule = validator.validate("123456789012")
        assert result is False
        assert rule == []

    def test_validate_max_blacklisted_failure(self):
        validator = AadhaarValidation(self.product.id)
        validator.check_max_allowed_rules = Mock(return_value=True)
        validator.check_max_blacklisted_rules = Mock(return_value=False)
        result, rule = validator.validate("123456789012")
        assert result is True
        assert rule == ["max_blacklisted"]

    def test_date_range(self):
        # Test for a range of 10 seconds
        seconds = 10
        from_date, to_date = AadhaarValidation(self.product.id)._date_range(
            seconds
        )
        assert to_date > from_date
        assert to_date - from_date == timedelta(seconds=seconds)
