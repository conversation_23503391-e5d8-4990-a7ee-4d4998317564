from unittest.mock import Mock
from django.test import TestCase
from accounts.billing_accounts.tests.factories import BillingAccountFactory
from accounts.core.tests.factories import CountryFactory
from accounts.products.tests.factories import ProductFactory
from accounts.verification.models import VerificationRules
from accounts.verification.utils.uan_verification import UanVerification
from accounts.billing_accounts import constants


class TestUanVerification(TestCase):
    def setUp(self):
        self.country = CountryFactory.create()
        self.product = ProductFactory.create(
            country=self.country,
        )

    def test_max_allowed(self):
        # test with max_allowed = zero
        billing_account_one = BillingAccountFactory.create(
            ac_number="AC001",
            business_name="Test Business 1",
            status=1,
            uan="***************",
        )
        VerificationRules.objects.create(
            rule="max_allowed",
            product=self.product,
            resource="uan",
            resource_id=billing_account_one.uan,
            value="0",
        )
        assert (
            UanVerification(self.product.id).check_max_allowed_rules(
                billing_account_one.uan
            )
            is False
        )

        # test with max_allowed set as unlimited
        billing_account_two = BillingAccountFactory.create(
            ac_number="AC002",
            business_name="Test Business 2",
            status=1,
            uan="***************",
        )
        VerificationRules.objects.create(
            rule="max_allowed",
            product=self.product,
            resource="uan",
            resource_id=billing_account_two.uan,
            value="-1",
        )
        assert (
            UanVerification(self.product.id).check_max_allowed_rules(
                billing_account_two.uan
            )
            is True
        )

    def test_max_allowed_with_duration_none(self):
        # with count withtin max limit
        billing_account_one = BillingAccountFactory.create(
            ac_number="AC001",
            business_name="Test Business 1",
            status=1,
            uan="***************",
        )
        VerificationRules.objects.create(
            rule="max_allowed",
            product=self.product,
            resource="uan",
            resource_id=billing_account_one.uan,
            value="2",
        )
        assert (
            UanVerification(self.product.id).check_max_allowed_rules(
                billing_account_one.uan
            )
            is True
        )

        # with count exceeded
        billing_account_two = BillingAccountFactory.create(
            ac_number="AC002",
            business_name="Test Business 2",
            status=1,
            uan="***************",
        )
        VerificationRules.objects.create(
            rule="max_allowed",
            product=self.product,
            resource="uan",
            resource_id=billing_account_two.uan,
            value="0",
        )
        assert (
            UanVerification(self.product.id).check_max_allowed_rules(
                billing_account_two.uan
            )
            is False
        )

    def test_max_allowed_with_duration(self):
        # with count withtin limit
        billing_account_one = BillingAccountFactory.create(
            ac_number="AC001",
            business_name="Test Business 1",
            status=1,
            uan="***************",
        )
        VerificationRules.objects.create(
            rule="max_allowed",
            product=self.product,
            resource="uan",
            resource_id=billing_account_one.uan,
            value="2/1d",
        )
        assert (
            UanVerification(self.product.id).check_max_allowed_rules(
                billing_account_one.uan
            )
            is True
        )

        # with count exceeded
        billing_account_two = BillingAccountFactory.create(
            ac_number="AC002",
            business_name="Test Business 2",
            status=1,
            uan="***************",
        )
        assert (
            UanVerification(self.product.id).check_max_allowed_rules(
                billing_account_two.uan
            )
            is False
        )

    def test_check_max_blacklisted_rules(self):
        # test with max_allowed = zero
        billing_account_one = BillingAccountFactory.create(
            ac_number="AC001",
            business_name="Test Business 1",
            status=1,
            uan="***************",
            verification_state=constants.BILLING_VERIFICATION_STATE_FRAUD,
        )
        VerificationRules.objects.create(
            rule="max_blacklisted",
            product=self.product,
            resource="uan",
            resource_id=billing_account_one.uan,
            value="0",
        )
        assert (
            UanVerification(self.product.id).check_max_blacklisted_rules(
                billing_account_one.uan
            )
            is False
        )

        # test with max_allowed set as unlimited zero
        billing_account_two = BillingAccountFactory.create(
            ac_number="AC002",
            business_name="Test Business 2",
            status=1,
            uan="***************",
            verification_state=constants.BILLING_VERIFICATION_STATE_FRAUD,
        )
        VerificationRules.objects.create(
            rule="max_blacklisted",
            product=self.product,
            resource="uan",
            resource_id=billing_account_two.uan,
            value="-1",
        )
        assert (
            UanVerification(self.product.id).check_max_blacklisted_rules(
                billing_account_two.uan
            )
            is True
        )

    def test_check_max_blacklisted_rules_duration_none(self):
        # with count withtin max limit
        billing_account_one = BillingAccountFactory.create(
            ac_number="AC001",
            business_name="Test Business 1",
            status=1,
            uan="***************",
            verification_state=constants.BILLING_VERIFICATION_STATE_FRAUD,
        )
        VerificationRules.objects.create(
            rule="max_blacklisted",
            product=self.product,
            resource="uan",
            resource_id=billing_account_one.uan,
            value="2",
        )
        assert (
            UanVerification(self.product.id).check_max_blacklisted_rules(
                billing_account_one.uan
            )
            is True
        )

        # with count exceeded max limit
        billing_account_two = BillingAccountFactory.create(
            ac_number="AC002",
            business_name="Test Business 2",
            status=1,
            uan="***************",
            verification_state=constants.BILLING_VERIFICATION_STATE_FRAUD,
        )
        VerificationRules.objects.create(
            rule="max_blacklisted",
            product=self.product,
            resource="uan",
            resource_id=billing_account_two.uan,
            value="1",
        )
        assert (
            UanVerification(self.product.id).check_max_blacklisted_rules(
                billing_account_two.uan
            )
            is False
        )

    def test_check_max_blacklisted_rules_duration(self):
        # with count withtin max limit
        billing_account_one = BillingAccountFactory.create(
            ac_number="AC001",
            business_name="Test Business 1",
            status=1,
            uan="***************",
            verification_state=constants.BILLING_VERIFICATION_STATE_FRAUD,
        )
        VerificationRules.objects.create(
            rule="max_blacklisted",
            product=self.product,
            resource="uan",
            resource_id=billing_account_one.uan,
            value="2/1d",
        )
        assert (
            UanVerification(self.product.id).check_max_blacklisted_rules(
                billing_account_one.uan
            )
            is True
        )

        # with count exceeded max limit
        billing_account_two = BillingAccountFactory.create(
            ac_number="AC002",
            business_name="Test Business 2",
            status=1,
            uan="***************",
            verification_state=constants.BILLING_VERIFICATION_STATE_FRAUD,
        )
        assert (
            UanVerification(self.product.id).check_max_blacklisted_rules(
                billing_account_two.uan
            )
            is False
        )

    def test_validate_max_allowed(self):
        validator = UanVerification(self.product.id)
        validator.check_max_allowed_rules = Mock(return_value=True)
        result, rule = validator.validate("***************")
        assert result is False
        assert rule == []

    def test_validate_max_allowed_failure(self):
        validator = UanVerification(self.product.id)
        validator.check_max_allowed_rules = Mock(return_value=False)
        result, rule = validator.validate("***************")
        assert result is True
        assert rule == ["max_allowed"]

    def test_validate_max_blacklisted(self):
        validator = UanVerification(self.product.id)
        validator.check_max_allowed_rules = Mock(return_value=True)
        validator.check_max_blacklisted_rules = Mock(return_value=True)
        result, rule = validator.validate("***************")
        assert result is False
        assert rule == []

    def test_validate_max_blacklisted_failure(self):
        validator = UanVerification(self.product.id)
        validator.check_max_allowed_rules = Mock(return_value=True)
        validator.check_max_blacklisted_rules = Mock(return_value=False)
        result, rule = validator.validate("123456789012963")
        assert result is True
        assert rule == ["max_blacklisted"]
