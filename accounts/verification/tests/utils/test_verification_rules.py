import pytest
from django.test import TestCase

from accounts.products.models import Products
from accounts.products.tests.factories import ProductFactory
from accounts.verification.exceptions import RuleConflictException
from accounts.verification.models import VerificationRules
from accounts.verification.utils.verification_rule import (
    create_rule,
    get_conflicting_rule,
    get_default_rule_keys,
    get_rules_queryset,
    get_verification_rules,
    is_rule_exists,
    validate_time,
    edit_rule,
    concat_allowed_and_seconds,
)


class TestVerificationRules(TestCase):
    def setUp(self):
        self.product = ProductFactory.create()
        self.rule = VerificationRules.objects.create(
            rule="Test product",
            product=self.product,
            resource="aadhaar",
            resource_id="abc123",
            value="2/90d",
        )

    def test_create_rule(self):
        # with time not none
        rule_name = "test_rule"
        resource = "test_resource"
        rule = create_rule(
            rule_name,
            self.product.id,
            resource,
            allowed=1,
            time=3600,
            resource_id=None,
        )
        obj = VerificationRules.objects.get(id=rule.id)

        assert obj.rule == rule_name
        assert obj.product.id == self.product.id
        assert obj.resource == resource
        assert obj.resource_id is None
        assert obj.value == "1/1h"

    def test_create_rule_with_product_404(self):
        # with time not none
        rule_name = "test_rule"
        resource = "test_resource"
        with pytest.raises(Products.DoesNotExist):
            create_rule(
                rule_name,
                "123",
                resource,
                allowed=1,
                time=3600,
                resource_id=None,
            )

    def test_create_conflicting_rules(self):
        create_rule(
            "max_allowed",
            self.product.id,
            "android",
            allowed=-1,
            time=None,
            resource_id="123_abc",
        )
        with pytest.raises(RuleConflictException):
            create_rule(
                "max_allowed",
                self.product.id,
                "android",
                allowed=1,
                time=3600,
                resource_id="123_abc",
            )

    def test_create_non_conflicting_rules(self):
        rule_1 = create_rule(
            "max_allowed",
            self.product.id,
            "android",
            allowed=10,
            time=None,
            resource_id="123_abc",
        )
        rule_2 = create_rule(
            "max_allowed",
            self.product.id,
            "android",
            allowed=5,
            time=3600,
            resource_id="123_abc",
        )
        assert VerificationRules.objects.filter(id=rule_1.id).exists() is True
        assert VerificationRules.objects.filter(id=rule_2.id).exists() is True

    def test_is_rule_exists_true(self):
        VerificationRules.objects.create(
            rule="test_rule",
            product=self.product,
            resource="test_resource",
            resource_id="123",
            value="5/1h",
        )
        # test with resource_id
        assert (
            is_rule_exists(
                product_id=self.product.id,
                rule="test_rule",
                resource="test_resource",
                resource_id=123,
                allowed=5,
                seconds=3600,
            )
            is True
        )
        # test without resource_id
        assert (
            is_rule_exists(
                product_id=self.product.id,
                rule="test_rule",
                resource="test_resource",
                resource_id=None,
                allowed=5,
                seconds=3600,
            )
            is True
        )

    def test_is_rule_exists_false(self):
        assert (
            is_rule_exists(
                product_id=self.product.id,
                rule="test_rule",
                resource="test_resource_new",
                resource_id=456,
                allowed=3,
                seconds=3600,
            )
            is False
        )

    def test_get_default_rule_keys(self):
        VerificationRules.objects.create(
            product=self.product,
            rule="test_rule_1",
            resource="test_resource",
            status=1,
        )

        VerificationRules.objects.create(
            product=self.product,
            rule="test_rule_2",
            resource="test_resource",
            status=1,
        )
        VerificationRules.objects.create(
            product=self.product,
            rule="test_rule_2",
            resource="test_resource",
            status=1,
        )
        assert set(
            get_default_rule_keys(
                product_id=self.product.id, resource="test_resource"
            )
        ) == set(["test_rule_1", "test_rule_2"])

    def test_get_verification_rules(self):
        VerificationRules.objects.create(
            product=self.product,
            rule="test_rule_abc",
            resource="resource_one",
            status=1,
        )
        VerificationRules.objects.create(
            product=self.product,
            rule="test_rule_xyz",
            resource="resource_two",
            status=1,
        )
        VerificationRules.objects.create(
            product=self.product,
            rule="test_rule_abc",
            resource="resource_one",
            resource_id=1,
            status=1,
            value="5/30d",
        )
        VerificationRules.objects.create(
            product=self.product,
            rule="test_rule_xyz",
            resource="resource_two",
            resource_id=1,
            status=1,
            value="2/30d",
        )
        result = get_verification_rules(
            product_id=self.product.id, resource="resource_one", resource_id=1
        )
        assert result == {
            "test_rule_abc": [{"allowed": 5, "time": 2592000}],
        }

    def test_get_rules_queryset(self):
        rule = "new_rule"
        resource = "test_resource"
        resource_id = "123"
        created_rule = VerificationRules.objects.create(
            product=self.product,
            rule=rule,
            resource=resource,
            resource_id=resource_id,
        )
        fetch_rule = get_rules_queryset(
            self.product.id, rule, resource, resource_id
        )

        assert created_rule in fetch_rule

        # test rule  without resource_id
        created_rule = VerificationRules.objects.create(
            product=self.product,
            rule=rule,
            resource=resource,
        )

        fetch_rule = get_rules_queryset(
            self.product.id, rule, resource, resource_id=None
        )
        assert created_rule in fetch_rule

    def test_get_rules_queryset_for_resource_id_as_null(self):
        rule_1 = VerificationRules.objects.create(
            product=self.product,
            rule="max_allowed",
            resource="android",
            value="3/90d",
        )
        rule_2 = VerificationRules.objects.create(
            product=self.product,
            rule="max_allowed",
            resource="android",
            value="1/30d",
        )
        rule_3 = VerificationRules.objects.create(
            product=self.product,
            rule="max_allowed",
            resource="android",
            resource_id="123_abc",
            value="-1",
        )

        queryset = get_rules_queryset(
            self.product.id, "max_allowed", "android", None
        )
        assert queryset.count() == 2
        assert queryset.filter(id=rule_1.id).exists() is True
        assert queryset.filter(id=rule_2.id).exists() is True
        assert queryset.filter(id=rule_3.id).exists() is False

    def test_get_rules_queryset_for_resource_id_not_null(self):
        rule_1 = VerificationRules.objects.create(
            product=self.product,
            rule="max_allowed",
            resource="android",
            value="3/90h",
        )
        rule_2 = VerificationRules.objects.create(
            product=self.product,
            rule="max_allowed",
            resource="android",
            resource_id="123_abc",
            value="-1",
        )

        queryset = get_rules_queryset(
            self.product.id, "max_allowed", "android", "123_abc"
        )
        assert queryset.count() == 1
        assert queryset.filter(id=rule_1.id).exists() is False
        assert queryset.filter(id=rule_2.id).exists() is True

    def test_validate_time_allowed_unlimited(self):
        allowed = -1
        time = 3600
        with pytest.raises(ValueError):
            validate_time(allowed, time)

    def test_validate_time_allowed_reject_all(self):
        allowed = 0
        time = 3600
        with pytest.raises(ValueError):
            validate_time(allowed, time)

    def test_validate_time_allowed_other(self):
        allowed = 1
        time = 3600
        assert validate_time(allowed, time) is None

    def test_validate_time_unlimited_null_time(self):
        allowed = -1
        time = None
        assert validate_time(allowed, time) is None

    def test_validate_time_reject_null_time(self):
        allowed = 0
        time = None
        assert validate_time(allowed, time) is None

    def test_validate_time_allowed_other_null_time(self):
        allowed = 1
        time = None
        assert validate_time(allowed, time) is None

    def test_get_conflicting_rule_false(self):
        # test without any rule 0,-1 or any other
        resource = "test_resource"
        resource_id = 1
        rule = "max_allowed"
        kwargs = {"rule": "max_allowed", "allowed": 10}
        assert (
            get_conflicting_rule(
                self.product.id, resource, resource_id, **kwargs
            )
            is None
        )

        # test with some rule present
        VerificationRules.objects.create(
            rule=rule,
            product=self.product,
            resource=resource,
            resource_id=resource_id,
            value="5/90d",
        )
        resource = "resource1"
        resource_id = 1

        assert (
            get_conflicting_rule(
                self.product.id, resource, resource_id, **kwargs
            )
            is None
        )

    def test_get_conflicting_rule_true(self):
        #  test with  any rule exists other than 0,-1
        resource = "test_resource"
        resource_id = 1
        rule = "max_allowed"
        rule_id = VerificationRules.objects.create(
            rule=rule,
            product=self.product,
            resource=resource,
            resource_id=resource_id,
            value="5/90d",
        )
        assert (
            get_conflicting_rule(
                product_id=self.product.id,
                resource=resource,
                resource_id=resource_id,
                rule=rule,
                allowed=0,
            )
            == rule_id.id
        )

        #  test with rule exists 0 or -1
        rule_id.status = 0
        rule_id.save()
        resource = "test_resource"
        resource_id = 1
        rule = "max_allowed"
        rule_id = VerificationRules.objects.create(
            rule=rule,
            product=self.product,
            resource=resource,
            resource_id=resource_id,
            value="0",
        )
        assert (
            get_conflicting_rule(
                product_id=self.product.id,
                resource=resource,
                resource_id=resource_id,
                rule=rule,
                allowed=5,
            )
            == rule_id.id
        )

    def test_multiple_rules_on_same_resource_id(self):
        VerificationRules.objects.create(
            rule="max_allowed",
            product=self.product,
            resource="aadhaar",
            resource_id="123",
            value="5/90d",
        )

        VerificationRules.objects.create(
            rule="max_allowed",
            product=self.product,
            resource="aadhaar",
            resource_id="123",
            value="6/120d",
        )

        assert (
            get_conflicting_rule(
                product_id=self.product.id,
                resource="aadhaar",
                resource_id="123",
                rule="max_allowed",
                allowed=10,
            )
            is None
        )

    # Valid input data with no conflicting rule
    def test_edit_rule_valid_input_no_conflict(self):
        data = {
            "allowed": 1,
            "time": 3600,
            "product_id": self.product.id,
            "resource": "example",
            "resource_id": 456,
            "rule": "example_rule",
            "status": 1,
        }

        result = edit_rule(self.rule.id, data)

        self.assertTrue(result)

    # Valid input data with conflicting rule
    def test_edit_rule_valid_input_with_conflict(self):
        data = {
            "allowed": 0,
            "time": None,
            "product_id": self.product.id,
            "resource": "example",
            "resource_id": 456,
            "rule": "conflicting_rule",
            "status": 1,
        }

        VerificationRules.objects.create(
            product=self.product,
            resource="example",
            resource_id=456,
            rule="conflicting_rule",
            status=1,
        )

        with self.assertRaises(RuleConflictException):
            edit_rule(self.rule.id, data)

    # Valid input data with no changes to the existing rule
    def test_edit_rule_valid_input_no_changes(self):
        data = {
            "allowed": 1,
            "time": 3600,
            "product_id": self.product.id,
            "resource": "example",
            "resource_id": 456,
            "rule": "example_rule",
            "status": 1,
        }

        rule = VerificationRules.objects.create(
            value=concat_allowed_and_seconds(1, 3600),
            product=self.product,
            resource="example",
            resource_id=456,
            rule="example_rule",
            status=1,
        )

        result = edit_rule(rule.id, data)

        self.assertTrue(result)

    # Invalid rule ID
    def test_edit_rule_invalid_id(self):
        rule_id = 111
        data = {
            "allowed": 1,
            "time": 3600,
            "product_id": self.product.id,
            "resource": "example",
            "resource_id": 456,
            "rule": "example_rule",
            "status": 1,
        }

        with self.assertRaises(VerificationRules.DoesNotExist):
            edit_rule(rule_id, data)

    # Invalid input data format
    def test_edit_rule_invalid_input_format(self):

        data = {}

        with self.assertRaises(ValueError):
            edit_rule(self.rule.id, data)

    # Duration defined for disallowed rules
    def test_edit_rule_duration_for_disallowed_rules(self):
        data = {
            "allowed": 0,
            "time": 3600,
            "product_id": self.product.id,
            "resource": "example",
            "resource_id": 456,
            "rule": "example_rule",
            "status": 1,
        }

        with self.assertRaises(ValueError):
            edit_rule(self.rule.id, data)

    # Error while saving the edited rule
    def test_edit_rule_save_error(self):
        data = {
            "allowed": 1,
            "time": 3600,
            "product_id": self.product.id,
            "resource": "example",
            "resource_id": 456,
            "rule": "example_rule",
            "status": 1,
        }

        rule = VerificationRules.objects.create(
            value=concat_allowed_and_seconds(1, 3600),
            product=self.product,
            resource="example",
            resource_id=456,
            rule="example_rule",
            status=1,
        )

        data["product_id"] = 9999

        with self.assertRaises(Exception):
            edit_rule(rule.id, data)
