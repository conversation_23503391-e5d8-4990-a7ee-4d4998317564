from factory import Faker, SubFactory, LazyAttribute
from factory.django import DjangoModelFactory

from accounts.products.tests.factories import ProductFactory
from accounts.verification.models import VerificationRules
from accounts.verification.constants import VERIFICATION_RULE_STATUS_ACTIVE
from accounts.verification.utils.verification_rule import (
    concat_allowed_and_seconds,
)


class VerificationRuleFactory(DjangoModelFactory):

    product = SubFactory(ProductFactory)
    rule = Faker("pystr", max_chars=30)
    resource = Faker("pystr", max_chars=10)
    resource_id = Faker("pystr", max_chars=15)
    value = Faker("random_int", min=1, max=100)

    allowed = Faker("random_int", min=1, max=100)
    time = Faker("random_int", min=1, max=3600)

    value = LazyAttribute(
        lambda p: concat_allowed_and_seconds(p.allowed, p.time)
    )
    status = VERIFICATION_RULE_STATUS_ACTIVE

    class Meta:
        model = VerificationRules
        exclude = ("allowed", "time")
