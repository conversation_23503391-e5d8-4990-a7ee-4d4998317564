from django.urls import reverse

from rest_framework import status
from rest_framework.test import APITestCase

from accounts.core.tests.factories import CountryFactory
from accounts.products.tests.factories import ProductFactory
from accounts.verification.models import VerificationRules
from accounts.verification.views.verification_list import (
    VerificationRulesListView,
)


class VerificationListViewTestCase(APITestCase):
    def setUp(self):
        self.url = "/verification/resource/"
        self.country = CountryFactory.create()
        self.product = ProductFactory.create(
            country=self.country,
            short_code="test_short_code",
        )
        self.resource = "email"
        self.resource_id = "123"
        self.view = VerificationRulesListView.as_view()

    def test_rule_listing_success(self):
        # test with filters and sorting
        VerificationRules.objects.create(
            rule="max_allowed",
            product=self.product,
            resource="aadhaar",
            value="8/1h",
        )

        response = self.client.get(
            reverse(
                "verification_rules:list_rule",
                args=["test_short_code", "aadhaar"],
            ),
            data={},
            format="json",
        )

        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"
        assert response.json()["data"] == {
            "max_allowed": [{"allowed": 8, "time": 3600}]
        }

    def test_rule_listing_404(self):
        # test with filters and sorting

        response = self.client.get(
            reverse(
                "verification_rules:list_rule", args=["test_new", "aadhaar"]
            ),
            data={},
            format="json",
        )

        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.json()["status"] == "error"
        assert response.json()["message"] == "Not found."
        assert response.json()["errors"]["detail"] == "invalid product code"
