from unittest.mock import patch

from django.urls import reverse

from rest_framework import status
from rest_framework.test import APITestCase

from accounts.core.tests.factories import CountryFactory
from accounts.products.tests.factories import ProductFactory


class TestVerificationRuleLimitRetrieveAPIView(APITestCase):
    def setUp(self):
        self.country = CountryFactory.create()
        self.product = ProductFactory.create(
            country=self.country,
            short_code="ABC",
        )

    @patch(
        "accounts.verification.utils.aadhaar_verification.AadhaarValidation.validate"
    )
    def test_verify_rule_limit_exceeded_true(self, aadhaar_validate):
        aadhaar_validate.return_value = True, ["max_allowed"]

        response = self.client.get(
            reverse(
                "verification_rules:verify_limit_exceeded",
                kwargs={"product": "ABC"},
            ),
            {"aadhaar_no": "123"},
            format="json",
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"
        assert response.json()["data"]["limit_exceeded"] is True
        assert response.json()["data"]["reason"] == ["max_allowed"]

    def test_verify_rule_limit_exceeded_invalid_product(self):
        response = self.client.get(
            reverse(
                "verification_rules:verify_limit_exceeded",
                kwargs={"product": "XYZ"},
            ),
            {"aadhaar_no": "123"},
            format="json",
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.json()["status"] == "error"
        assert response.json()["message"] == "Not found."
