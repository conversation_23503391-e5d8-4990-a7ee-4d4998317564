from django.urls import reverse

from rest_framework import status
from rest_framework.test import APITestCase

from accounts.products.tests.factories import ProductFactory
from accounts.verification.models import VerificationRules
from accounts.verification.tests.factories import VerificationRuleFactory


class TestRuleRetrieveUpdateDestroyAPIView(APITestCase):
    def setUp(self):
        self.product = ProductFactory.create()
        self.rule = VerificationRuleFactory.create(
            rule="Test product",
            product=self.product,
            resource="aadhaar",
            resource_id="abc123",
            value="2/90d",
        )

    def test_delete_rule_success(self):
        response = self.client.delete(
            reverse(
                "verification_rules:edit_delete_rule",
                kwargs={"rule_id": self.rule.id},
            ),
            {},
            format="json",
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"

    def test_delete_rule_404(self):
        """
        Delete rule test case when the rule does not exist.

        This test case verifies that when a delete request is made for a rule that does not exist,
        the server returns a 404 Not Found HTTP status code and an error status in the JSON response.

        Parameters:
            self (TestClassName): An instance of the test class.

        Returns:
            None
        """
        response = self.client.delete(
            reverse(
                "verification_rules:edit_delete_rule",
                kwargs={"rule_id": "abc123"},
            ),
            {},
            format="json",
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.json()["status"] == "error"

    def test_retrieve_rule_success(self):
        response = self.client.get(
            reverse(
                "verification_rules:edit_delete_rule",
                kwargs={"rule_id": self.rule.id},
            ),
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["data"]["rule"] == self.rule.rule
        assert response.json()["data"]["product_id"] == self.rule.product_id
        assert response.json()["data"]["resource"] == self.rule.resource
        assert response.json()["data"]["resource_id"] == self.rule.resource_id

    def test_retrieve_rule_404(self):
        response = self.client.get(
            reverse(
                "verification_rules:edit_delete_rule",
                kwargs={"rule_id": 123},
            ),
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_edit_status_rule_success(self):
        rule = VerificationRuleFactory.create(
            rule="Test product",
            product=self.product,
            resource="aadhaar",
            resource_id="abc456",
            value="2/30d",
        )
        url = reverse(
            "verification_rules:edit_delete_rule", kwargs={"rule_id": rule.id}
        )
        response = self.client.patch(
            url,
            {
                "status": "inactive",
            },
            format="json",
        )
        edited_rule = VerificationRules.objects.get(id=rule.id)
        assert response.status_code == status.HTTP_200_OK
        assert edited_rule.status == 0

    def test_edit_status_rule_404(self):
        url = reverse(
            "verification_rules:edit_delete_rule", kwargs={"rule_id": 123}
        )
        response = self.client.patch(
            url,
            {
                "status": "inactive",
            },
            format="json",
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_edit_status_rule_validation_error(self):
        url = reverse(
            "verification_rules:edit_delete_rule",
            kwargs={"rule_id": self.rule.id},
        )
        response = self.client.patch(
            url,
            {
                "status": "test",
            },
            format="json",
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["status"] == "error"
        assert response.json()["errors"]["status"] == [
            '"test" is not a valid choice.'
        ]

    def test_edit_rule_success(self):
        rule = VerificationRuleFactory.create(
            rule="Test product",
            product=self.product,
            resource="aadhaar",
            resource_id="abc456",
            value="2/30d",
        )
        url = reverse(
            "verification_rules:edit_delete_rule", kwargs={"rule_id": rule.id}
        )
        response = self.client.put(
            url,
            {
                "allowed": 2,
                "time": 3600,
                "rule": "max_blacklisted",
                "product_id": self.product.id,
                "resource": "android",
                "status": "active",
            },
            format="json",
        )
        edited_rule = VerificationRules.objects.get(id=rule.id)

        assert response.status_code == status.HTTP_200_OK
        assert edited_rule.value == "2/1h"
        assert edited_rule.resource == "android"

    def test_edit_rule_404(self):
        url = reverse(
            "verification_rules:edit_delete_rule", kwargs={"rule_id": "111"}
        )
        response = self.client.put(
            url,
            {
                "product_id": self.product.id,
                "rule": "max_blacklisted",
                "resource": "aadhaar",
                "allowed": 2,
                "time": 3600,
                "status": "active",
            },
            format="json",
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_edit_rule_validation_error(self):
        url = reverse(
            "verification_rules:edit_delete_rule", kwargs={"rule_id": "111"}
        )
        response = self.client.put(
            url,
            {
                "allowed": 2,
                "time": 3600,
                "rule": "max_blacklisted",
                "resource": "aadhaar",
                "status": "active",
            },
            format="json",
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["status"] == "error"
        assert response.json()["errors"]["product_id"] == [
            "This field is required."
        ]

    # Update the 'allowed' attribute to 1
    def test_patch_single_attribute(self):
        url = reverse(
            "verification_rules:edit_delete_rule",
            kwargs={"rule_id": self.rule.id},
        )
        data = {"allowed": 1}
        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["status"], "success")
        self.assertEqual(response.json()["data"]["allowed"], 1)

    # Update multiple attributes
    def test_patch_multiple_attributes(self):
        url = reverse(
            "verification_rules:edit_delete_rule",
            kwargs={"rule_id": self.rule.id},
        )
        data = {"allowed": 0, "status": "inactive", "time": None}
        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["status"], "success")
        self.assertEqual(response.json()["data"]["allowed"], 0)
        self.assertEqual(response.json()["data"]["status"], "inactive")

    # Update optional fields 'resource_id' and 'time'
    def test_patch_optional_fields(self):
        url = reverse(
            "verification_rules:edit_delete_rule",
            kwargs={"rule_id": self.rule.id},
        )
        data = {"resource_id": "12345", "time": 60}
        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["status"], "success")
        self.assertEqual(response.json()["data"]["resource_id"], "12345")
        self.assertEqual(response.json()["data"]["time"], 60)

    # Update the 'status' field to 'active'
    def test_patch_status_field(self):
        url = reverse(
            "verification_rules:edit_delete_rule",
            kwargs={"rule_id": self.rule.id},
        )
        data = {"status": "active"}
        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["status"], "success")
        self.assertEqual(response.json()["data"]["status"], "active")

    # Update the 'resource' field to 'android'
    def test_patch_resource_field(self):
        url = reverse(
            "verification_rules:edit_delete_rule",
            kwargs={"rule_id": self.rule.id},
        )
        data = {"resource": "android"}
        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["status"], "success")
        self.assertEqual(response.json()["data"]["resource"], "android")

    # invalid choice for the 'resource' field
    def test_patch_invalid_choice_for_resource_field(self):
        url = reverse(
            "verification_rules:edit_delete_rule",
            kwargs={"rule_id": self.rule.id},
        )
        data = {"resource": "passport"}
        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json()["status"], "error")
        self.assertEqual(response.json()["code"], "ACC_0002")
        self.assertIn("resource", response.json()["errors"])

    # invalid value for the 'allowed' field
    def test_patch_invalid_value_for_allowed_field(self):
        url = reverse(
            "verification_rules:edit_delete_rule",
            kwargs={"rule_id": self.rule.id},
        )
        data = {"allowed": "abc"}
        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json()["status"], "error")
        self.assertEqual(response.json()["code"], "ACC_0002")
        self.assertIn("allowed", response.json()["errors"])

    # invalid value for the 'status' field
    def test_patch_invalid_value_for_status_field(self):
        url = reverse(
            "verification_rules:edit_delete_rule",
            kwargs={"rule_id": self.rule.id},
        )
        data = {"status": "cancel"}
        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json()["status"], "error")
        self.assertEqual(response.json()["code"], "ACC_0002")
        self.assertIn("status", response.json()["errors"])

    def test_patch_non_existing_rule(self):
        """
        This test case verifies that the method returns a 404 status code when trying to patch a non-existing rule.

        Parameters:
        - self: The test case object.

        Returns:
        - None
        """
        url = reverse(
            "verification_rules:edit_delete_rule", kwargs={"rule_id": 999}
        )
        data = {"allowed": 1}
        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    # same values for all attributes
    def test_patch_success_no_changes(self):
        url = reverse(
            "verification_rules:edit_delete_rule",
            kwargs={"rule_id": self.rule.id},
        )
        data = {"allowed": self.rule.allowed}
        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()["status"], "success")
        self.assertEqual(response.json()["data"]["allowed"], self.rule.allowed)
