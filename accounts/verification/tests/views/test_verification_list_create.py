from unittest.mock import patch

from django.urls import reverse

from rest_framework import status
from rest_framework.test import APITestCase

from accounts.core.tests.factories import CountryFactory
from accounts.products.tests.factories import ProductFactory
from accounts.verification.models import VerificationRules


class TestRulesListCreateView(APITestCase):
    def setUp(self):
        self.country = CountryFactory.create()
        self.product = ProductFactory.create(
            country=self.country,
        )

    def test_create_rule_success(self):
        data = {
            "rule": "max_blacklisted",
            "allowed": 8,
            "time": 3606,
            "product_id": self.product.id,
            "resource": "aadhaar",
            "resource_id": "************",
            "status": "active",
        }

        response = self.client.post(
            reverse("verification:create_list_rule"),
            data=data,
            format="json",
        )
        created_data = VerificationRules.objects.get(
            id=response.data["data"]["id"]
        )
        assert response.status_code == status.HTTP_201_CREATED
        assert response.json()["status"] == "success"
        assert response.json()["message"] == "Rule created"
        assert created_data.resource == data["resource"]
        assert created_data.resource_id == data["resource_id"]
        assert created_data.rule == data["rule"]
        assert created_data.status == 1

    def test_create_rule_already_exists(self):
        data = {
            "rule": "max_blacklisted",
            "allowed": 8,
            "time": 3600,
            "product_id": self.product.id,
            "resource": "aadhaar",
            "resource_id": "************",
            "status": "active",
        }
        VerificationRules.objects.create(
            rule=data["rule"],
            product=self.product,
            resource=data["resource"],
            resource_id=data.get("resource_id"),
            value="8/1h",
        )

        response = self.client.post(
            reverse("verification:create_list_rule"),
            data=data,
            format="json",
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["status"] == "error"
        assert (
            response.json()["message"]
            == "Validation Failed, Please check errors field for details"
        )
        assert response.json()["errors"] == ["Rule already exists"]

    def test_create_rule_validation_error(self):
        data = {
            "rule": "test_create_rule_validation_error",
            "allowed": None,
            "time": 3600,
            "product_id": self.product.id,
            "resource": "aadhaar",
            "resource_id": "************",
            "status": "active",
        }
        VerificationRules.objects.create(
            rule=data["rule"],
            product=self.product,
            resource=data["resource"],
            resource_id=data.get("resource_id"),
            value="8/1h",
        )

        response = self.client.post(
            reverse("verification:create_list_rule"),
            data=data,
            format="json",
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["status"] == "error"
        assert (
            response.json()["message"]
            == "Validation Failed, Please check errors field for details"
        )

    @patch(
        "accounts.verification.views.verification_list_create.RulesListCreateView.perform_create"
    )
    def test_create_rule_500_error(self, mock_perform_create):
        mock_perform_create.return_value = None

        data = {
            "rule": "max_allowed",
            "allowed": "2",
            "time": 3600,
            "product_id": self.product.id,
            "resource": "aadhaar",
            "resource_id": "************",
            "status": "active",
        }

        response = self.client.post(
            reverse("verification:create_list_rule"),
            data=data,
            format="json",
        )
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert response.json()["status"] == "error"
        assert response.json()["message"] == "Server Error"

    def test_rule_listing_success(self):
        # test with filters and sorting
        data = {
            "status": "active",
            "rule": "max_allowed",
            "product_id": self.product.id,
            "resource": "aadhaar",
            "resource_id": "123",
            "sort": "created",
            "order": "desc",
        }
        rule = VerificationRules.objects.create(
            rule=data["rule"],
            product=self.product,
            resource=data["resource"],
            resource_id=data.get("resource_id"),
            value="8/1h",
        )

        response = self.client.get(
            reverse("verification:create_list_rule"),
            data=data,
            format="json",
        )

        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"
        assert response.json()["data"][0]["id"] == rule.id
        assert response.json()["data"][0]["rule"] == rule.rule
        assert response.json()["data"][0]["product_id"] == rule.product_id
        assert response.json()["data"][0]["resource"] == rule.resource
        assert response.json()["data"][0]["resource_id"] == rule.resource_id
        assert response.json()["data"][0]["status"] == "active"

        # test without filters and sorting
        data = {}

        response = self.client.get(
            reverse("verification:create_list_rule"),
            data=data,
            format="json",
        )

        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"
        assert response.json()["data"][0]["id"] == rule.id
        assert response.json()["data"][0]["rule"] == rule.rule
        assert response.json()["data"][0]["product_id"] == rule.product_id
        assert response.json()["data"][0]["resource"] == rule.resource
        assert response.json()["data"][0]["resource_id"] == rule.resource_id
        assert response.json()["data"][0]["status"] == "active"

    def test_rule_listing_validation_error(self):
        data = {
            "status": "test",
        }

        response = self.client.get(
            reverse("verification:create_list_rule"),
            data=data,
            format="json",
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["status"] == "error"
        assert (
            response.json()["message"]
            == "Validation Failed, Please check errors field for details"
        )
        assert response.json()["errors"]["status"] == [
            '"test" is not a valid choice.'
        ]
