from typing import Union
from rest_framework import serializers
from accounts.products.models import Products
from accounts.verification import constants


class RuleCreateUpdateSerializer(serializers.Serializer):
    RESOURCE_CHOICES = ["aadhaar", "android", "email", "contact", "gst", "uan"]
    product_id = serializers.CharField()
    resource = serializers.ChoiceField(choices=RESOURCE_CHOICES)
    resource_id = serializers.CharField(required=False, default=None)
    allowed = serializers.IntegerField()
    time = serializers.IntegerField(default=None, allow_null=True)
    status = serializers.ChoiceField(choices=["active", "inactive"])

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        resource = self.initial_data.get("resource")
        resource_id = self.initial_data.get("resource_id")

        if resource in ["aadhaar", "android", "contact", "gst", "uan"]:
            self.fields["rule"] = serializers.ChoiceField(
                choices=["max_allowed", "max_blacklisted"],
            )
        elif resource == "email":
            self.fields["rule"] = serializers.ChoiceField(
                choices=["max_allowed"]
            )
        # resource_id must be 12 digits for contact and aadhaar
        if resource in ["contact", "aadhaar"] and (
            resource_id and len(resource_id) != 12
        ):
            raise serializers.ValidationError(
                f"Resource ID must be 12 digits for {resource}"
            )

        # resource_id must be 15 characters for gst and uan
        if resource in ["gst", "uan"] and (
            resource_id and len(resource_id) != 15
        ):
            raise serializers.ValidationError(
                f"Resource ID must be 15 characters for {resource}"
            )

    def validate_allowed(self, value: int) -> int:
        if value < constants.ALLOW_UNLIMITED:
            raise serializers.ValidationError(
                f"allowed value should be equal or greater than {constants.ALLOW_UNLIMITED}"
            )
        return value

    def validate_time(self, value: Union[int, None]) -> Union[int, None]:
        if (
            self.initial_data.get("allowed")
            in [constants.ALLOW_UNLIMITED, constants.REJECT_ALL]
        ) and value is not None:
            raise serializers.ValidationError("Time field should be null")
        return value

    def validate_product_id(self, value: str) -> str:
        # If the poduct slug does not exist, raise a validation error
        exists = Products.objects.filter(id=value).exists()
        if not exists:
            raise serializers.ValidationError("Invalid product id")
        return value

    def validate_status(self, value: str) -> int:
        if value.lower() == "active":
            return constants.VERIFICATION_RULE_STATUS_ACTIVE

        return constants.VERIFICATION_RULE_STATUS_INACTIVE


class RuleSerializer(serializers.Serializer):
    id = serializers.CharField()
    rule = serializers.CharField()
    product_id = serializers.SerializerMethodField()
    resource = serializers.CharField()
    resource_id = serializers.CharField()
    allowed = serializers.SerializerMethodField()
    time = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    created = serializers.DateTimeField()
    modified = serializers.DateTimeField()

    def get_product_id(self, instance):
        return instance.product.id

    def get_allowed(self, instance):
        return instance.allowed

    def get_time(self, instance):
        return instance.seconds

    def get_status(self, obj):
        if obj.status == constants.VERIFICATION_RULE_STATUS_ACTIVE:
            return "active"
        return "inactive"


class RuleListSerializer(serializers.Serializer):
    product_id = serializers.CharField(max_length=50, default=None)
    resource = serializers.CharField(max_length=50, default=None)
    resource_id = serializers.CharField(max_length=100, default=None)
    rule = serializers.CharField(default=None)
    status = serializers.ChoiceField(
        choices=["active", "inactive"], default=None
    )
    sort = serializers.ChoiceField(choices=["created"], default="created")
    order = serializers.ChoiceField(choices=["asc", "desc"], default="asc")

    def validate_status(self, value):
        # manipulate value for status field as 0=> Inactive,1=>active
        if value is not None:
            if value.lower() == "active":
                return 1
            else:
                return 0

        return None
