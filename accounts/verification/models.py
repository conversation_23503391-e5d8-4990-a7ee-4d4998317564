from django.db import models

from accounts.utils.common import uuid, duration_to_seconds

from accounts.products.models import Products
from accounts.verification.constants import (
    VERIFICATION_RULE_STATUS_ACTIVE,
    VERIFICATION_RULE_STATUS_INACTIVE,
)


class VerificationRules(models.Model):
    id = models.CharField(max_length=36, primary_key=True, default=uuid)
    rule = models.CharField(max_length=30)
    product = models.ForeignKey(
        Products,
        db_column="product_id",
        on_delete=models.CASCADE,
    )
    resource = models.CharField(max_length=30)
    resource_id = models.CharField(max_length=100, null=True)
    value = models.CharField(max_length=30)
    status = models.SmallIntegerField(default=VERIFICATION_RULE_STATUS_ACTIVE)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    def inactive(self):
        self.status = VERIFICATION_RULE_STATUS_INACTIVE
        self.save()

    @property
    def allowed(self):
        allowed = self.value.split("/")[0]
        return int(allowed)

    @property
    def seconds(self):
        arr = self.value.split("/")
        if len(arr) > 1:
            return duration_to_seconds(arr[1])
        return None

    class Meta:
        db_table = "verification_rules"
        managed = True
        indexes = [
            models.Index(fields=["product_id", "resource", "resource_id"]),
        ]
