from django_filters import rest_framework as filters


from accounts.services.models import Services
from accounts.services.enums import ServiceStatusEnum, ServiceLiveStatusEnum


class ServiceFilter(filters.FilterSet):
    status = filters.CharFilter(method="filter_by_status")
    live_status = filters.CharFilter(method="filter_by_live_status")
    product_short_code = filters.CharFilter(method="filter_by_product_short_code")

    def filter_by_status(self, queryset, name, value):
        statuses = set(value.strip().split(","))
        statuses = [status.upper().strip() for status in statuses]

        return queryset.filter(
            status__in=ServiceStatusEnum.get_values(statuses)
        )

    def filter_by_live_status(self, queryset, name, value):
        live_statuses = set(value.strip().split(","))
        live_statuses = [
            live_status.upper().strip() for live_status in live_statuses
        ]

        return queryset.filter(
            live_status__in=ServiceLiveStatusEnum.get_values(live_statuses)
        )
    
    def filter_by_product_short_code(self, queryset, name, value):
        short_codes = set(value.strip().split(","))
        return queryset.filter(
            product__short_code__in=short_codes
        )

    class Meta:
        model = Services
        fields = {
            "activation_date": ["gte", "lte", "exact"],
        }
