from django.conf import settings

from accounts.utils.events.sns import SnsEvent
from accounts.utils.events.sns_v2 import SnsEventV2
from accounts.services.models import Services


class ServiceActivationEvent(SnsEvent):

    EVENT_ACTION = "service_activated"
    REQUIRED_PARAMS = ("company_id",)

    def __init__(self):
        self.arn = settings.ACCOUNTS_SNS_ARN
        super().__init__()

    def company_id(self, company_id):
        self.service_type = get_product_code_from_gsn(company_id)
        self.set_data("company_id", company_id)
        return self


class ServiceDeactivationEvent(SnsEvent):

    EVENT_ACTION = "service_deactivated"
    REQUIRED_PARAMS = ("company_id",)

    def __init__(self):
        self.arn = settings.ACCOUNTS_SNS_ARN
        super().__init__()

    def company_id(self, company_id):
        self.service_type = get_product_code_from_gsn(company_id)
        self.set_data("company_id", company_id)
        return self


class ServiceTerminationEvent(SnsEvent):

    EVENT_ACTION = "service_terminated"
    REQUIRED_PARAMS = ("company_id",)

    def __init__(self):
        self.arn = settings.ACCOUNTS_SNS_ARN
        super().__init__()

    def company_id(self, company_id):
        self.service_type = get_product_code_from_gsn(company_id)
        self.set_data("company_id", company_id)
        return self


class ServiceSuspensionEvent(SnsEvent):

    EVENT_ACTION = "service_suspended"
    REQUIRED_PARAMS = ("company_id", "ban")

    def __init__(self):
        self.arn = settings.ACCOUNTS_SNS_ARN
        super().__init__()

    def company_id(self, company_id):
        self.service_type = get_product_code_from_gsn(company_id)
        self.set_data("company_id", company_id)
        return self

    def ban(self, ban):
        self.set_data("ban", ban)
        return self


class UpdatePermissionEvent(SnsEvent):

    EVENT_ACTION = "permission_updated"
    REQUIRED_PARAMS = ("company_id",)

    def __init__(self):
        self.arn = settings.SNS_ARN_FEATURE_UPDATE
        super().__init__()

    def company_id(self, company_id):
        self.service_type = get_product_code_from_gsn(company_id)
        self.set_data("company_id", company_id)
        return self


class FeatureActivationEvent(SnsEventV2):
    EVENT_ACTION = "activated"
    REQUIRED_PARAMS = ("company_id", "truecaller")

    def __init__(self):
        self.arn = settings.SNS_ARN_ACCOUNT_FEATURE
        self.namespace = "account_features"
        super().__init__()

    def company_id(self, company_id):
        self.service_type = get_product_code_from_gsn(company_id)
        self.set_data("company_id", company_id)
        return self

    def add_feature_with_data(self, feature, feature_data=None):
        self.add_feature(feature)
        if feature_data:
            self.set_data(feature, feature_data)
        return self


class FeatureDeactivationEvent(SnsEventV2):
    EVENT_ACTION = "deactivated"
    REQUIRED_PARAMS = ("company_id", "truecaller")

    def __init__(self):
        self.arn = settings.SNS_ARN_ACCOUNT_FEATURE
        self.namespace = "account_features"
        super().__init__()

    def company_id(self, company_id):
        self.service_type = get_product_code_from_gsn(company_id)
        self.set_data("company_id", company_id)
        return self

    def add_feature_with_data(self, feature, feature_data=None):
        self.add_feature(feature)
        if feature_data:
            self.set_data(feature, feature_data)
        return self


def get_product_code_from_gsn(gsn):
    service = Services.objects.filter(gsn=gsn).first()
    if service:
        return service.product.short_code
    return None
