from django.utils import timezone as tz

from factory import Faker, Iterator, SubFactory
from factory.django import DjangoModelFactory

from accounts.billing_accounts.tests.factories import BillingAccountFactory
from accounts.core.tests.factories import CountryFactory
from accounts.packages.tests.factories import (
    PackageFactory,
    PackageFeatureFactory,
)
from accounts.products.tests.factories import ProductFactory
from accounts.services.enums import ServiceLiveStatusEnum
from accounts.services.models import (
    ActivityNotes,
    FeatureUsagesLogs,
    OtherCharges,
    RenewRetains,
    ResourceCharges,
    ServiceNumbers,
    ServicePackages,
    ServiceRentals,
    Services,
)
from accounts.users.tests.factories import UserProfileFactory


class ServiceFactory(DjangoModelFactory):

    product = SubFactory(ProductFactory)
    billing_account = SubFactory(BillingAccountFactory)
    gsn = Faker("pystr", max_chars=10)
    user_profile = SubFactory(UserProfileFactory)
    country = SubFactory(CountryFactory)
    timezone = "00:00"
    rent_per_month = Faker("random_int")
    renew_cycle = Faker("random_int")
    activation_date = Faker("date_time_ad", tzinfo=tz.get_current_timezone())
    expiry_date = Faker("date_time_ad", tzinfo=tz.get_current_timezone())
    last_renewal = None
    live_status = ServiceLiveStatusEnum.DEMO.value
    churn_date = None
    comment = Faker("text")
    current_usages = Faker("random_int", min=0, max=*********)

    class Meta:
        model = Services


class ServiceNumberFactory(DjangoModelFactory):

    service = SubFactory(ServiceFactory)
    service_number = Faker("phone_number")
    number_type = Iterator(["1", "2", "3"])
    number_cost = Faker("pyint", min_value=1, max_value=100)
    is_paid = Faker("boolean")
    is_live = Faker("boolean")

    class Meta:
        model = ServiceNumbers


class RenewRetainFactory(DjangoModelFactory):

    product = SubFactory(ProductFactory)
    product_name = Faker("company")
    product_currency = "INR"
    billing_account = SubFactory(BillingAccountFactory)
    ban = Faker("random_number", digits=8)
    service = SubFactory(ServiceFactory)
    service_number = Faker("phone_number")
    business_name = Faker("company")
    account_manager = Faker("name")
    account_manager_id = Faker("uuid4")
    last_renewal = Faker(
        "date_time_this_decade", tzinfo=tz.get_current_timezone()
    )
    expiry_date = Faker("date_time_this_year", tzinfo=tz.get_current_timezone())
    package = SubFactory(PackageFactory)
    package_period_start = Faker(
        "date_time_this_year", tzinfo=tz.get_current_timezone()
    )
    package_period_end = Faker(
        "date_time_this_year", tzinfo=tz.get_current_timezone()
    )
    package_name = Faker("word")
    package_rent_per_month = Faker("pyfloat", positive=True)
    package_amount = Faker("pyfloat", positive=True)
    renewal_amount = Faker("pyfloat", positive=True)
    package_cycle = Faker("random_int", min=1, max=12)
    contact_persons = Faker("text")
    renewal_status = 1
    retention_status = 0

    class Meta:
        model = RenewRetains


class ActivityNoteFactory(DjangoModelFactory):

    billing_account = SubFactory(BillingAccountFactory)
    service = SubFactory(ServiceFactory)
    renew_retain = SubFactory(RenewRetainFactory)
    note = Faker("text")
    created_by = SubFactory(UserProfileFactory)
    created = Faker("date_time_this_decade")
    modified = Faker("date_time_this_month")

    class Meta:
        model = ActivityNotes


class ServiceRentalFactory(DjangoModelFactory):

    service = SubFactory(ServiceFactory)
    rental_amount = Faker("pyfloat", left_digits=5, right_digits=2)
    pending_rental = Faker("pyfloat", left_digits=5, right_digits=2)
    trans_type = "cr"

    class Meta:
        model = ServiceRentals


class ServicePackageFactory(DjangoModelFactory):

    service = SubFactory(ServiceFactory)
    package = SubFactory(PackageFactory)
    package_type = Faker("random_element", elements=["main", "addon"])
    start_time = Faker(
        "date_time_this_decade", tzinfo=tz.get_current_timezone()
    )
    end_time = Faker("date_time_this_decade", tzinfo=tz.get_current_timezone())

    class Meta:
        model = ServicePackages


class ResourceChargeFactory(DjangoModelFactory):

    service = SubFactory(ServiceFactory)
    created = Faker("date_time_this_year", tzinfo=tz.get_current_timezone())

    class Meta:
        model = ResourceCharges


class OtherChargesFactory(DjangoModelFactory):

    service = SubFactory(ServiceFactory)
    date = Faker("date_object")

    class Meta:
        model = OtherCharges


class FeatureUsagesLogFactory(DjangoModelFactory):

    service = SubFactory(ServiceFactory)
    package_feature = SubFactory(PackageFeatureFactory)
    leg_a_use = 0
    leg_b_min = 0
    leg_b_rate = 0.00
    start_date = Faker("date_time_ad", tzinfo=tz.get_current_timezone())
    end_date = Faker("date_time_ad", tzinfo=tz.get_current_timezone())

    class Meta:
        model = FeatureUsagesLogs
