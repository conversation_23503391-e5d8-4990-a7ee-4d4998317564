from decimal import Decimal
from django.test import TestCase
from accounts.billing_accounts.tests.factories import BillingAccountFactory
from accounts.services.models import (
    Services,
    ServiceRentals,
    OtherCharges,
    ServiceNumbers,
)
from accounts.services.tests.factories import (
    ServiceFactory,
    ServiceRentalFactory,
    OtherChargesFactory,
    ServiceNumberFactory,
)
from accounts.billing_accounts.enums import BillingStatusEnum
from accounts.services.enums import (
    OtherChargesStatusEnum,
    ServiceNumberPaidEnum,
)


class TestServiceManagerSet(TestCase):
    def setUp(self):
        self.billing_account = BillingAccountFactory(
            status=BillingStatusEnum.ACTIVE.value
        )
        self.service = ServiceFactory(billing_account=self.billing_account)

    def test_total_usage_for_active_billing_account(self):
        self.service.current_usages = 100
        self.service.save()

        total_current_usage = Services.objects.total_current_usage()
        self.assertIsInstance(total_current_usage, Decimal)
        self.assertEqual(total_current_usage, 100)

    def test_total_usage_for_inactive_billing_account(self):
        self.billing_account.status = BillingStatusEnum.INACTIVE.value
        self.billing_account.save()
        self.service.current_usages = 100
        self.service.save()

        total_current_usage = Services.objects.billing_account(
            self.billing_account
        ).total_current_usage()
        self.assertIsInstance(total_current_usage, Decimal)
        self.assertEqual(total_current_usage, 100)


class TestServiceRentalManager(TestCase):
    def setUp(self):
        self.billing_account = BillingAccountFactory(
            status=BillingStatusEnum.ACTIVE.value
        )
        self.service = ServiceFactory(billing_account=self.billing_account)

    def test_total_pending_rental_with_active_billing_account(self):
        ServiceRentalFactory(service=self.service, pending_rental=199)
        total_pending_rental = ServiceRentals.entries.billing_account(
            self.billing_account
        ).total_pending_rental()
        self.assertIsInstance(total_pending_rental, Decimal)
        self.assertEqual(total_pending_rental, 199)

    def test_total_pending_rental_for_inactive_billing_account(self):
        self.billing_account.status = BillingStatusEnum.INACTIVE.value
        self.billing_account.save()

        ServiceRentalFactory(service=self.service, pending_rental=199)
        total_pending_rental = ServiceRentals.entries.billing_account(
            self.billing_account
        ).total_pending_rental()
        self.assertIsInstance(total_pending_rental, Decimal)
        self.assertEqual(total_pending_rental, 199)


class TestOtherChargesManager(TestCase):
    def setUp(self):
        self.billing_account = BillingAccountFactory(
            status=BillingStatusEnum.ACTIVE.value
        )
        self.service = ServiceFactory(billing_account=self.billing_account)

    def test_total_other_charges_with_active_billing_account(self):
        OtherChargesFactory(
            service=self.service,
            charge=100,
            status=OtherChargesStatusEnum.PAID.value,
        )
        OtherChargesFactory(
            service=self.service,
            charge=200,
            status=OtherChargesStatusEnum.UNPAID.value,
        )
        total_other_charges = OtherCharges.unpaid.billing_account(
            self.billing_account
        ).total_other_charges()
        self.assertIsInstance(total_other_charges, Decimal)
        self.assertEqual(total_other_charges, 200)

    def test_total_other_charges_for_inactive_billing_account(self):
        self.billing_account.status = BillingStatusEnum.INACTIVE.value
        self.billing_account.save()

        OtherChargesFactory(
            service=self.service,
            charge=100,
            status=OtherChargesStatusEnum.PAID.value,
        )
        OtherChargesFactory(
            service=self.service,
            charge=200,
            status=OtherChargesStatusEnum.UNPAID.value,
        )
        total_other_charges = OtherCharges.unpaid.billing_account(
            self.billing_account
        ).total_other_charges()
        self.assertIsInstance(total_other_charges, Decimal)
        self.assertEqual(total_other_charges, 200)


class TestUnpaidOtherChargesManager(TestCase):
    def setUp(self):
        self.billing_account = BillingAccountFactory()
        self.service = ServiceFactory(billing_account=self.billing_account)
        self.other_charge1 = OtherChargesFactory(
            service=self.service,
            charge=100,
            status=OtherChargesStatusEnum.UNPAID.value,
        )
        self.other_charge2 = OtherChargesFactory(
            service=self.service,
            charge=100,
            status=OtherChargesStatusEnum.PAID.value,
        )

    def test_unpaid_other_charges(self):
        qs = OtherCharges.unpaid.all()
        self.assertIn(self.other_charge1, qs)
        self.assertNotIn(self.other_charge2, qs)
        self.assertEqual(qs.count(), 1)


class TestServiceNumberManager(TestCase):
    def setUp(self):
        self.billing_account = BillingAccountFactory(
            status=BillingStatusEnum.ACTIVE.value
        )
        self.service = ServiceFactory(billing_account=self.billing_account)

    def test_total_unpaid_number_cost_with_active_billing_account(self):
        ServiceNumberFactory(
            service=self.service,
            number_cost=100,
            is_paid=ServiceNumberPaidEnum.PAID.value,
        )
        ServiceNumberFactory(
            service=self.service,
            number_cost=200,
            is_paid=ServiceNumberPaidEnum.UNPAID.value,
        )
        total_unpaid_number_cost = ServiceNumbers.entries.billing_account(
            self.billing_account
        ).total_unpaid_number_cost()
        self.assertIsInstance(total_unpaid_number_cost, Decimal)
        self.assertEqual(total_unpaid_number_cost, 200)

    def test_total_unpaid_number_cost_for_inactive_billing_account(self):
        self.billing_account.status = BillingStatusEnum.INACTIVE.value
        self.billing_account.save()

        ServiceNumberFactory(
            service=self.service,
            number_cost=100,
            is_paid=ServiceNumberPaidEnum.PAID.value,
        )
        ServiceNumberFactory(
            service=self.service,
            number_cost=200,
            is_paid=ServiceNumberPaidEnum.UNPAID.value,
        )
        total_unpaid_number_cost = ServiceNumbers.entries.billing_account(
            self.billing_account
        ).total_unpaid_number_cost()
        self.assertIsInstance(total_unpaid_number_cost, Decimal)
        self.assertEqual(total_unpaid_number_cost, 200)
