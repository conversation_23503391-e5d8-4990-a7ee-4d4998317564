from unittest import mock

from django.test import TestCase

import celery

from accounts.billing_accounts.enums import BillingVerificationStateEnum
from accounts.exceptions import ChatAPIException
from accounts.global_packages.tests.factories import GlobalPackageFactory
from accounts.payments.enums import PaymentActionStatusEnum
from accounts.payments.tests.factories import (
    PaymentActionFactory,
    PaymentTracksFactory,
    TrackingSettlementHistoriesFactory,
)
from accounts.services.enums import ServiceLiveStatusEnum
from accounts.services.tasks import service_activation_task
from accounts.services.tests.factories import ServiceFactory


class TestServiceActivationTask(TestCase):
    def setUp(self):
        package = GlobalPackageFactory.create()
        self.service = ServiceFactory.create()
        self.payment_track = PaymentTracksFactory.create(
            txn_settle_status=1,
        )
        self.payment_action = PaymentActionFactory.create(
            payment_id=self.payment_track.payment_id,
            action_data={
                "service_id": self.service.id,
                "package_id": package.id,
                "service_number": "myop123",
            },
        )
        self.payment_settlement_history = (
            TrackingSettlementHistoriesFactory.create(
                billing_account=self.service.billing_account,
                setl_key=self.payment_track.txn_setl_key,
                payment=self.payment_track,
                amount=self.payment_track.amount,
            )
        )

    @mock.patch("accounts.services.tasks.ServiceActivation")
    def test_service_activation_task_success(self, mock_service_activation_cls):
        mock_instance = mock_service_activation_cls.return_value
        mock_instance.initiate_activation.return_value = True
        mock_instance = mock_instance.publish_event.return_value = mock_instance
        mock_instance.sync_memcache.return_value = mock_instance

        with self.captureOnCommitCallbacks(execute=True):
            task = service_activation_task.apply(
                [self.payment_action.payment_id]
            )

        self.payment_action.refresh_from_db()
        self.assertEqual(
            self.payment_action.status, PaymentActionStatusEnum.SUCCESS.value
        )
        self.assertEqual(task.status, celery.states.SUCCESS)

        # Assert ServiceActivation was instantiated with correct params
        mock_service_activation_cls.assert_called_once_with(
            mock.ANY,  # service - we'll assert this below
            mock.ANY,  # payment_track - we'll assert this below
        )

        # Assert correct service was passed
        service_id = self.payment_action.action_data["service_id"]
        actual_service = mock_service_activation_cls.call_args[0][0]
        self.assertEqual(actual_service.id, service_id)

        # Assert payment_track was passed correctly
        payment_track = mock_service_activation_cls.call_args[0][1]
        self.assertEqual(
            payment_track.payment_id, self.payment_action.payment_id
        )

        # Assert initiate_activation was called with correct params
        mock_instance.initiate_activation.assert_called_once_with(
            self.payment_action.action_data["package_id"],
            self.payment_action.action_data.get("service_number"),
        )
        mock_instance.publish_event.assert_called_once()
        mock_instance.sync_memcache.assert_called_once()

    def test_service_activation_task_fraud_billing_account(self):
        self.service.billing_account.verification_state = (
            BillingVerificationStateEnum.FRAUD.value
        )
        self.service.billing_account.save()

        task = service_activation_task.apply([self.payment_action.payment_id])
        self.assertEqual(task.status, celery.states.FAILURE)
        self.payment_action.refresh_from_db()
        self.assertEqual(
            self.payment_action.status, PaymentActionStatusEnum.FAILED.value
        )
        self.assertEqual(
            self.payment_action.failure_reason, "Billing Account is fraud"
        )

    def test_service_activation_task_invalid_service(self):
        self.payment_action.action_data["service_id"] = "invalid_service_id"
        self.payment_action.save()

        task = service_activation_task.apply([self.payment_action.payment_id])

        self.assertEqual(task.status, celery.states.FAILURE)
        self.payment_action.refresh_from_db()
        self.assertEqual(
            self.payment_action.status, PaymentActionStatusEnum.FAILED.value
        )
        self.assertEqual(
            self.payment_action.failure_reason,
            "Services matching query does not exist.",
        )

    def test_service_activation_task_not_a_demo_service(self):
        self.service.live_status = ServiceLiveStatusEnum.PREPAID.value
        self.service.save()

        task = service_activation_task.apply([self.payment_action.payment_id])

        self.assertEqual(task.status, celery.states.FAILURE)
        self.payment_action.refresh_from_db()
        self.assertEqual(
            self.payment_action.status, PaymentActionStatusEnum.FAILED.value
        )
        self.assertEqual(
            self.payment_action.failure_reason, "Not a demo service"
        )

    def test_service_activation_task_payment_not_settled(self):
        self.payment_track.txn_settle_status = 0
        self.payment_track.save()

        task = service_activation_task.apply([self.payment_action.payment_id])

        self.assertEqual(task.status, celery.states.FAILURE)
        self.payment_action.refresh_from_db()
        self.assertEqual(
            self.payment_action.status, PaymentActionStatusEnum.FAILED.value
        )
        self.assertEqual(
            self.payment_action.failure_reason,
            "Payment is not settled",
        )
