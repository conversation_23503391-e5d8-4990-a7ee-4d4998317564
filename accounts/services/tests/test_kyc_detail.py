from freezegun import freeze_time
import pytest
from django.urls import reverse
from rest_framework import status

from accounts.billing_accounts.enums import BillingStatusEnum
from accounts.billing_accounts.tests.factories import BillingAccountFactory
from accounts.kyc.enums import (
    KYCModeEnum,
    KYCSourceEnum,
    KYCStateEnum,
    KYCStatusEnum,
)
from accounts.kyc.models import KYC
from accounts.billing_accounts.models import BillingAccounts
from accounts.services.models import Services
from accounts.services.tests.factories import ServiceFactory


@pytest.fixture
def gsn():
    return "TEST123456"


@pytest.fixture
@freeze_time("2025-01-01 00:00:00+00:00")
def service(billing_account, gsn) -> Services:
    try:
        return Services.objects.get(
            billing_account=billing_account,
            gsn=gsn,
        )
    except Services.DoesNotExist:
        return ServiceFactory(
            billing_account=billing_account,
            gsn=gsn,
        )


@pytest.fixture
@freeze_time("2025-01-01 00:00:00+00:00")
def billing_account() -> BillingAccounts:
    """Create a test billing account."""
    try:
        return BillingAccounts.objects.get(
            ac_number="123456", status=BillingStatusEnum.ACTIVE.value
        )
    except BillingAccounts.DoesNotExist:
        return BillingAccountFactory(
            ac_number="123456", status=BillingStatusEnum.ACTIVE.value
        )


@pytest.fixture
@freeze_time("2025-01-01 00:00:00+00:00")
def gst_kyc_record(billing_account) -> KYC:
    """Create a test KYC record."""
    try:
        return KYC.objects.get(
            billing_account=billing_account,
            current_state=KYCStateEnum.VERIFICATION.value,
            mode=KYCModeEnum.GST.value,
            source=KYCSourceEnum.MYOPERATOR.value,
            status=KYCStatusEnum.PENDING.value,
        )
    except KYC.DoesNotExist:
        return KYC.initialize_kyc(
            mode=KYCModeEnum.GST,
            billing_account=billing_account,
            data={},
            source=KYCSourceEnum.MYOPERATOR,
        )


@pytest.mark.django_db
@pytest.mark.unittest
@pytest.mark.parametrize("expand", ["kyc_states", None, ""])
@freeze_time("2025-01-01 00:00:00+00:00")
def test_get_kyc_details_success(client, service, gst_kyc_record, expand):
    """Test successful retrieval of KYC details."""
    url = reverse("services:kyc_detail", kwargs={"gsn": service.gsn})
    if expand:
        response = client.get(url, {"expand": expand})
    else:
        response = client.get(url)

    assert response.status_code == status.HTTP_200_OK
    json_response = response.json()

    expected_response = {
        "code": status.HTTP_200_OK,
        "message": "KYC details fetched successfully",
        "status": "success",
        "data": {
            "id": str(gst_kyc_record.id),
            "billing_account_id": str(service.billing_account_id),
            "current_state": "verification",
            "mode": "gst",
            "source": "myoperator",
            "status": "pending",
            "created": "2025-01-01T00:00:00Z",
            "modified": "2025-01-01T00:00:00Z",
            "expiry": None,
        },
    }
    if expand:
        expected_response["data"]["kyc_states"] = {
            "e_sign": "kyc_done",
            "kyc_done": None,
            "verification": {
                "aadhaar": {
                    "digilocker_pan": "video_kyc",
                    "gst_info": "video_kyc",
                    "upload_pan": "video_kyc",
                },
                "gst": "video_kyc",
            },
            "video_kyc": "e_sign",
        }
    assert json_response == expected_response


@pytest.mark.django_db
@pytest.mark.unittest
@freeze_time("2025-01-01 00:00:00+00:00")
def test_get_kyc_details_invalid_gsn(client):
    """Test KYC details retrieval with invalid GSN."""
    url = reverse("services:kyc_detail", kwargs={"gsn": "13124"})
    response = client.get(url)

    assert response.status_code == status.HTTP_404_NOT_FOUND
    json_response = response.json()
    assert json_response == {
        "code": status.HTTP_404_NOT_FOUND,
        "message": "Invalid GSN provided.",
        "status": "error",
        "errors": {"detail": "Invalid GSN provided."},
    }


@pytest.mark.django_db
@pytest.mark.unittest
@freeze_time("2025-01-01 00:00:00+00:00")
def test_get_kyc_details_no_kyc(client, service):
    """Test KYC details retrieval when no KYC exists for billing account."""
    url = reverse("services:kyc_detail", kwargs={"gsn": service.gsn})
    response = client.get(url)

    assert response.status_code == status.HTTP_404_NOT_FOUND
    json_response = response.json()
    assert json_response == {
        "code": status.HTTP_404_NOT_FOUND,
        "message": "KYC details not found.",
        "status": "error",
        "errors": {"detail": "KYC details not found."},
    }
