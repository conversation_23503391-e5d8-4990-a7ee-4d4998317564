from datetime import timed<PERSON><PERSON>
import pytest
from unittest import mock
from freezegun import freeze_time

from django.test import TestCase
from django.conf import settings
from django.utils import timezone
from accounts.services.models import Services, ServiceNumbers
from accounts.services.exceptions import (
    InvalidServiceException,
    ServiceAlreadyDeactivated,
)
from accounts.services.enums import (
    ServiceStatusEnum,
    ServiceLiveStatusEnum,
    ServiceNumberStatusEnum,
)
from accounts.services.utils.service_suspension import ServiceSuspension
from accounts.users.tests.factories import UserProfileFactory
from accounts.services.tests.factories import (
    ServiceFactory,
    ServiceNumberFactory,
)
from accounts.billing_accounts.tests.factories import (
    BillingAccountFactory,
)
from accounts.services.models import ActivityNotes


class TestServiceSuspension(TestCase):
    def setUp(self):
        self.billing_account = BillingAccountFactory.create()
        self.service = ServiceFactory.create(
            billing_account=self.billing_account,
            live_status=ServiceLiveStatusEnum.PREPAID.value,
        )
        self.service_number = ServiceNumberFactory.create(
            service=self.service, status=ServiceNumberStatusEnum.ACTIVE.value
        )
        self.user_profile = UserProfileFactory()

    @freeze_time(timezone.now())
    @mock.patch(
        "accounts.utils.api_services.account_v1.AccountApiV1.update_memcache"
    )
    @mock.patch(
        "accounts.utils.api_services.number_system.NumberSystem.deactivate_service_number"
    )
    @mock.patch(
        "accounts.utils.api_services.myoperator.MyOperator.deactivate_company"
    )
    @mock.patch("accounts.services.events.ServiceSuspensionEvent.send")
    def test_suspend_success(
        self,
        mock_sns_event,
        mock_deactivate_company,
        mock_deactivate_service_number,
        mock_sync_memcache,
    ):
        mock_sns_event.return_value = True
        mock_sync_memcache.return_value = True
        mock_deactivate_service_number.return_value = True
        mock_deactivate_company.return_value = True

        ServiceSuspension(self.service).suspend()

        mock_sync_memcache.assert_called_once()
        mock_deactivate_service_number.assert_not_called()
        mock_deactivate_company.assert_not_called()
        mock_sns_event.assert_called_once()

        self.service.refresh_from_db()
        assert self.service.status == ServiceStatusEnum.SUSPENDED.value
        assert self.service.churn_date is None
        self.service_number.refresh_from_db()
        assert (
            self.service_number.status == ServiceNumberStatusEnum.ACTIVE.value
        )

    def test_activity_note_for_suspend(self):
        self.service.status = ServiceStatusEnum.SUSPENDED.value
        self.service.save()
        ServiceSuspension(self.service).add_activity(self.user_profile.id)

        suspension_activity = ActivityNotes.objects.filter(
            service=self.service,
            note=settings.SUSPEND_NOTE,
        )
        self.assertEqual(suspension_activity.count(), 1)

    def test_suspend_already_suspended_service(self):
        service = ServiceFactory.create(
            status=ServiceStatusEnum.SUSPENDED.value
        )
        with pytest.raises(InvalidServiceException):
            ServiceSuspension(service).suspend()

    def test_suspend_inactive_service(self):
        service = ServiceFactory.create(status=ServiceStatusEnum.INACTIVE.value)
        with pytest.raises(InvalidServiceException):
            ServiceSuspension(service).suspend()
