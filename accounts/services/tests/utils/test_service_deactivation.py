from django.test import TestCase
from unittest import mock
import pytest
from freezegun import freeze_time
from accounts.services.models import Services, ServiceNumbers
from accounts.services.exceptions import (
    InvalidServiceException,
    ServiceAlreadyDeactivated,
)
from accounts.services.utils.service_deactivation import deactivate_service
from accounts.users.tests.factories import UserProfileFactory
from accounts.services.tests.factories import (
    ServiceFactory,
    ServiceNumberFactory,
    ServicePackageFactory,
)
from accounts.products.tests.factories import (
    ProductFactory,
    ProductSettingFactory,
    ProductDefaultSettingFactory,
)
from accounts.packages.tests.factories import (
    PackageFactory,
)
from accounts.global_packages.tests.factories import (
    PackageCategoryFactory,
)
from datetime import timedelta
from django.utils import timezone
from accounts.services import constants
from accounts.core.tests.factories import CountryFactory
from accounts.billing_accounts.tests.factories import (
    BillingAccountFactory,
)
from accounts.services.models import ActivityNotes
from django.conf import settings


class TestDeactivateService(TestCase):
    def setUp(self):

        self.country = CountryFactory.create()
        self.product = ProductFactory.create(country=self.country)
        self.billing_account = BillingAccountFactory.create(
            verification_state=1, status=1
        )
        self.service = ServiceFactory.create(
            billing_account=self.billing_account,
            product=self.product,
            country=self.country,
        )
        self.service_number = ServiceNumberFactory.create(
            service=self.service,
            service_number="1234",
        )
        self.package_category = PackageCategoryFactory.create(
            product=self.product,
            code="abcd",
        )
        self.package = PackageFactory.create(
            product=self.product, package_category=self.package_category
        )
        self.service_package = ServicePackageFactory.create(
            service_id=self.service.id,
            package=self.package,
            start_time=timezone.now(),
            end_time=timezone.now() + timedelta(days=30),
        )
        self.user_profile = UserProfileFactory()

    @freeze_time(timezone.now())
    @mock.patch(
        "accounts.utils.api_services.account_v1.AccountApiV1.update_memcache"
    )
    @mock.patch(
        "accounts.utils.api_services.number_system.NumberSystem.deactivate_service_number"
    )
    @mock.patch(
        "accounts.utils.api_services.myoperator.MyOperator.deactivate_company"
    )
    @mock.patch("accounts.services.events.ServiceDeactivationEvent.send")
    def test_deactivate_service_success(
        self,
        mock_service_deactivation_event,
        mock_deactivate_company,
        mock_deactivate_service_number,
        mock_sync_memcache,
    ):
        mock_service_deactivation_event.return_value = True
        mock_sync_memcache.return_value = True
        mock_deactivate_service_number.return_value = True
        mock_deactivate_company.return_value = True

        SETTING_KEY = f"SERVICE_NUMBER_ARCHIVE_DAYS_{self.service.live_status}_{self.package_category.code}"
        SERVICE_NUMBER_ARCHIVE_DAYS = 2

        ProductDefaultSettingFactory.create(
            setting_key=SETTING_KEY, setting_value=SERVICE_NUMBER_ARCHIVE_DAYS
        )

        deactivate_service(self.service.id, self.user_profile.id)

        self.service.refresh_from_db()
        self.service_number.refresh_from_db()

        deactivation_activity = ActivityNotes.objects.filter(
            service_id=self.service.id,
            note=settings.DEACTIVATE_NOTE,
            created_by=self.user_profile.id,
        ).first()

        mock_sync_memcache.assert_called_once()
        mock_deactivate_service_number.assert_called_once()
        mock_deactivate_company.assert_called_once()
        mock_service_deactivation_event.assert_called_once()

        assert self.service.status == 0
        assert self.service.churn_date == timezone.now()
        assert self.service_number.status == 0
        assert self.service_number.is_archived == 0
        archive_date = timezone.now() + timedelta(
            days=SERVICE_NUMBER_ARCHIVE_DAYS
        )
        self.assertEqual(self.service_number.archive_date, archive_date)
        self.assertEqual(
            deactivation_activity.created.date(), self.service.churn_date.date()
        )

    # Case when service is not found
    def test_invalid_service(self):
        service_id = 123
        with pytest.raises(InvalidServiceException):
            deactivate_service(service_id, self.user_profile.id)

    # Case when service is already deactivated
    def test_deactivate_service_already_deactivated(self):
        service = ServiceFactory.create(gsn="abc123456", status=0)
        with pytest.raises(ServiceAlreadyDeactivated):
            deactivate_service(service.id, self.user_profile.id)

    # Case when an exception occurs while deactivating service number
    @mock.patch(
        "accounts.utils.api_services.number_system.NumberSystem.deactivate_service_number"
    )
    def test_exception_deactivate_number(self, mock_deactivate_service_number):
        mock_deactivate_service_number.side_effect = Exception(
            "Error deactivating service number"
        )
        with pytest.raises(Exception):
            deactivate_service(self.service.id, self.user_profile.id)

    # Case when an exception occurs while adding activity notes
    @mock.patch("accounts.services.utils.activity_note.add_activity_notes")
    def test_deactivate_service_exception_add_activity_notes(
        self, mock_add_activity_notes
    ):
        mock_add_activity_notes.side_effect = Exception(
            "Error adding activity notes"
        )
        with pytest.raises(Exception):
            deactivate_service(self.service.id, self.user_profile.id)

    @freeze_time(timezone.now())
    @mock.patch(
        "accounts.utils.api_services.account_v1.AccountApiV1.update_memcache"
    )
    @mock.patch(
        "accounts.utils.api_services.number_system.NumberSystem.deactivate_service_number"
    )
    @mock.patch(
        "accounts.utils.api_services.myoperator.MyOperator.deactivate_company"
    )
    @mock.patch("accounts.services.events.ServiceDeactivationEvent.send")
    def test_deactivate_with_already_inactive_service_number(
        self,
        mock_service_deactivation_event,
        mock_deactivate_company,
        mock_deactivate_service_number,
        mock_update_memcache,
    ):
        mock_service_deactivation_event.return_value = True
        mock_deactivate_company.return_value = True
        mock_deactivate_service_number.return_value = True
        mock_update_memcache.return_value = True

        # deactivate service number & set archive date
        self.service_number.set_archive_date(
            timezone.now() + timedelta(days=10)
        )

        deactivate_service(self.service.id, self.user_profile.id)

        self.service.refresh_from_db()
        self.service_number.refresh_from_db()

        mock_update_memcache.assert_called_once()
        mock_deactivate_company.assert_called_once()
        mock_service_deactivation_event.assert_called_once()
        mock_deactivate_service_number.assert_not_called()

        assert self.service.status == 0
        assert self.service.churn_date == timezone.now()
        assert self.service_number.status == 0
        assert self.service_number.is_archived == 0
        assert self.service_number.archive_date == timezone.now() + timedelta(
            days=10
        )
