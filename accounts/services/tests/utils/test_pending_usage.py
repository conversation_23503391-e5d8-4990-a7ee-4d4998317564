from decimal import Decimal
from django.test import TestCase

from accounts.billing_accounts.tests.factories import (
    BillingAccountFactory,
    BillingAccountCreditsFactory,
)
from accounts.services.tests.factories import (
    ServiceFactory,
    ServiceRentalFactory,
    ServiceNumberFactory,
    OtherChargesFactory,
)
from accounts.discounts.tests.factories import DiscountBucketsFactory
from accounts.services.utils.pending_usage import get_pending_usage
from accounts.services.cache_handler import PendingUsageCacheHandler


class TestPendingUsage(TestCase):
    def setUp(self):
        parent_billing = BillingAccountFactory.create()
        billing_account = BillingAccountFactory.create(parent=parent_billing)
        self.service = ServiceFactory.create(
            billing_account=billing_account, current_usages=100, status=1
        )
        ServiceRentalFactory.create(
            pending_rental=500, service=self.service, status=1
        )
        ServiceNumberFactory.create(
            number_cost=300, service=self.service, status=1, is_paid=0
        )
        OtherChargesFactory.create(charge=150, service=self.service, status=1)
        DiscountBucketsFactory.create(
            billing_account=parent_billing, apply_on="R", status=1, value=300
        )
        BillingAccountCreditsFactory.create(
            billing_account=parent_billing, credit_amount=-200, status=1
        )

    def test_pending_usage_success(self):
        result = get_pending_usage(self.service)
        assert result["currency"] == "INR"
        assert result["pending_rental"] == Decimal("500")
        assert result["pending_usages"] == Decimal("200")
        assert result["current_usages"] == Decimal("100")
        assert result["other_charges"] == Decimal("150")
        assert result["service_number_cost"] == Decimal("300")
        assert result["discount"] == Decimal("300")
        assert result["advance"] == Decimal("0")
        assert result["available"] == Decimal("-950")

    def test_pending_usage_for_inactive_service(self):
        self.service.status = 0
        self.service.save()
        result = get_pending_usage(self.service)
        assert result["currency"] == "INR"
        assert result["pending_rental"] == Decimal("500")
        assert result["pending_usages"] == Decimal("200")
        assert result["current_usages"] == Decimal("100")
        assert result["other_charges"] == Decimal("150")
        assert result["service_number_cost"] == Decimal("300")
        assert result["discount"] == Decimal("300")
        assert result["advance"] == Decimal("0")
        assert result["available"] == Decimal("-950")

    def test_pending_usage_cache(self):
        result = get_pending_usage(self.service)
        cache_handler = PendingUsageCacheHandler(self.service.billing_account)

        assert cache_handler.exists() is True

        cached_data = cache_handler.get()
        assert result["currency"] == cached_data["currency"]
        assert result["pending_rental"] == Decimal(
            cached_data["pending_rental"]
        )
        assert result["pending_usages"] == Decimal(
            cached_data["pending_usages"]
        )
        assert result["current_usages"] == Decimal(
            cached_data["current_usages"]
        )
        assert result["other_charges"] == Decimal(cached_data["other_charges"])
        assert result["service_number_cost"] == Decimal(
            cached_data["service_number_cost"]
        )
        assert result["discount"] == Decimal(cached_data["discount"])
        assert result["advance"] == Decimal(cached_data["advance"])
        assert result["available"] == Decimal(cached_data["available"])
