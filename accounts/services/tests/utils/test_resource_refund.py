import logging
import pytest
from django.test import TestCase
from unittest import mock
from accounts.services.tests.factories import ResourceChargeFactory
from accounts.services.tests.factories import ServiceFactory
from accounts.billing_accounts.tests.factories import (
    BillingAccountFactory,
    BillingAccountCreditsFactory,
)
from accounts.services.utils.resource_refund import process_refund_for_resource
from accounts.services.models import ResourceCharges
from accounts.billing_accounts.models import BillingAccountCredits
from accounts.billing_accounts.exceptions import (
    BillingAccountCreditNotFoundException,
)
from accounts.services.exceptions import ResourceChargeRefundException
from decimal import Decimal

logger = logging.getLogger(__name__)


class TestResourceRefund(TestCase):
    def setUp(self):
        self.billing_account = BillingAccountFactory.create()
        self.billing_credit = BillingAccountCreditsFactory.create(
            billing_account=self.billing_account, credit_amount=100
        )
        service = ServiceFactory.create(billing_account=self.billing_account)
        self.resource = ResourceChargeFactory.create(
            resource_id=123, service=service, estimated_amount=100
        )

    @mock.patch("accounts.utils.api_services.chat.ChatApi.get_campaign_detail")
    def test_refund_with_resource_completed(self, mock_api):
        mock_api.return_value = {
            "id": 1,
            "billing": {"expected_cost": 100, "actual_cost": 90.5},
            "status": "completed",
        }

        process_refund_for_resource(self.resource)
        updated_resource = ResourceCharges.objects.get(id=self.resource.id)
        old_billing_credit = BillingAccountCredits.objects.get(
            id=self.billing_credit.id
        )
        new_billing_credit = BillingAccountCredits.objects.get(
            billing_account=self.billing_account, status=1
        )
        assert old_billing_credit.status == 0
        assert new_billing_credit.credit_amount == 109.50
        assert new_billing_credit.trans_type == "cr"
        assert (
            new_billing_credit.description
            == f"9.500 refund credited for: {self.resource.resource}, {self.resource.resource_id}"
        )
        assert updated_resource.is_refunded is True
        assert updated_resource.estimated_amount == 100
        assert updated_resource.actual_amount == 90.5

    @mock.patch("accounts.utils.api_services.chat.ChatApi.get_campaign_detail")
    def test_refund_with_resource_completed_in_decimal(self, mock_api):
        billing_account = BillingAccountFactory.create()
        billing_credit = BillingAccountCreditsFactory.create(
            billing_account=billing_account, credit_amount=100
        )
        service = ServiceFactory.create(billing_account=billing_account)

        resource = ResourceChargeFactory.create(
            resource_id=456, service=service, estimated_amount=Decimal("0.800")
        )
        mock_api.return_value = {
            "id": 1,
            "billing": {"expected_cost": 0.8, "actual_cost": 0.7},
            "status": "completed",
        }

        process_refund_for_resource(resource)
        updated_resource = ResourceCharges.objects.get(id=resource.id)
        old_billing_credit = BillingAccountCredits.objects.get(
            id=billing_credit.id
        )
        new_billing_credit = BillingAccountCredits.objects.get(
            billing_account=billing_account, status=1
        )
        assert old_billing_credit.status == 0
        assert new_billing_credit.credit_amount == Decimal("100.10")
        assert new_billing_credit.trans_type == "cr"
        assert (
            new_billing_credit.description
            == f"0.100 refund credited for: {resource.resource}, {resource.resource_id}"
        )
        assert updated_resource.is_refunded is True
        assert updated_resource.estimated_amount == Decimal("0.800")
        assert updated_resource.actual_amount == Decimal("0.700")

    @mock.patch("accounts.utils.api_services.chat.ChatApi.get_campaign_detail")
    def test_refund_with_resource_failed(self, mock_api):
        mock_api.return_value = {
            "id": 1,
            "billing": {"expected_cost": 100, "actual_cost": 90.5},
            "status": "failed",
        }

        process_refund_for_resource(self.resource)
        updated_resource = ResourceCharges.objects.get(id=self.resource.id)
        assert updated_resource.is_refunded is True
        assert updated_resource.estimated_amount == 100
        assert updated_resource.actual_amount == 0

        billing_credit = BillingAccountCredits.entries.get_credit_object(
            self.billing_account
        )
        assert billing_credit.status == 1
        assert billing_credit.credit_amount == 200

    @mock.patch("accounts.utils.api_services.chat.ChatApi.get_campaign_detail")
    def test_refund_with_negative_amount(self, mock_api):
        mock_api.return_value = {
            "id": 1,
            "billing": {"expected_cost": 100, "actual_cost": 110},
            "status": "completed",
        }

        updated_resource = ResourceCharges.objects.get(id=self.resource.id)
        credit_bucket = BillingAccountCredits.objects.get(
            id=self.billing_credit.id
        )

        with self.assertRaises(ResourceChargeRefundException):
            process_refund_for_resource(self.resource)

        credit_bucket.refresh_from_db()
        assert credit_bucket.status == 1
        assert credit_bucket.credit_amount == 100

        updated_resource.refresh_from_db()
        assert updated_resource.is_refunded is False
        assert updated_resource.estimated_amount == 100
        assert updated_resource.actual_amount is None
