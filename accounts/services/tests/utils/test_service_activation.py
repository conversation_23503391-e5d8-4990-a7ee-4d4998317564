import logging
from unittest import mock

from django.test import TestCase
from django.utils import timezone

import pytest

from accounts.billing_accounts.enums import BillingVerificationStateEnum
from accounts.billing_accounts.exceptions import (
    FraudBillingAccountException,
    InvalidBillingAccountException,
)
from accounts.billing_accounts.models import BillingStatusEnum
from accounts.billing_accounts.tests.factories import BillingAccountFactory
from accounts.global_packages.tests.factories import GlobalPackageFactory
from accounts.packages.tests.factories import (
    PackageCustomFactory,
    PackageFactory,
)
from accounts.payments.exceptions import InvalidPaymentException
from accounts.payments.tests.factories import (
    PaymentTracksFactory,
    TrackingSettlementHistoriesFactory,
)
from accounts.services.enums import (
    ServiceLiveStatusEnum,
    ServiceNumberArchiveEnum,
    ServiceNumberStatusEnum,
    ServiceRentalStatusEnum,
    ServiceStatusEnum,
)
from accounts.services.exceptions import (
    InvalidServiceException,
    ServiceActivationFailedException,
)
from accounts.services.models import (
    ServiceNumbers,
    ServicePackages,
    ServiceRentals,
)
from accounts.services.tests.factories import (
    ServiceFactory,
    ServiceNumberFactory,
    ServicePackageFactory,
    ServiceRentalFactory,
)
from accounts.services.utils.service_activation import ServiceActivation
from accounts.utils.common import add_months

logger = logging.getLogger(__name__)


class TestServiceActivation(TestCase):
    def setUp(self):
        self.payment_track = PaymentTracksFactory.create(
            amount=100, txn_settle_status=1
        )
        self.service = ServiceFactory.create(
            billing_account=BillingAccountFactory(
                status=BillingStatusEnum.INACTIVE.value,
            ),
            status=ServiceStatusEnum.INACTIVE.value,
            live_status=ServiceLiveStatusEnum.DEMO.value,
        )
        self.service_number = ServiceNumberFactory.create(
            service=self.service,
            service_number="**********",
            number_cost=0,
            is_live=0,
            is_paid=0,
            number_type="5",
        )
        self.service_package = ServicePackageFactory.create(
            service=self.service,
        )
        self.service_rental = ServiceRentalFactory.create(
            service=self.service, rental_amount=0, pending_rental=0
        )
        TrackingSettlementHistoriesFactory.create(
            billing_account=self.service.billing_account,
            payment=self.payment_track,
            amount=self.payment_track.amount,
            setl_key=self.payment_track.txn_setl_key,
        )

    @mock.patch(
        "accounts.utils.api_services.number_system.NumberSystem.make_number_live"
    )
    @mock.patch(
        "accounts.utils.api_services.myoperator.MyOperator.update_service_number"
    )
    @mock.patch(
        "accounts.utils.api_services.myoperator.MyOperator.update_permissions"
    )
    @mock.patch(
        "accounts.utils.api_services.number_system.NumberSystem.get_number_details"
    )
    def test_service_activation_with_global_package_and_existing_service_number(
        self,
        mock_get_number_details,
        mock_update_permissions,
        mock_update_service_number,
        mock_make_number_live,
    ):
        mock_get_number_details.return_value = {
            "display_number": "**********",
            "number_cost": 0,
            "number_status": "open",
            "is_mapped": "1",
            "country_short_code": "IN",
            "country_id_code": "91",
            "currency": "INR",
            "number_type": "5",
        }

        mock_update_permissions.return_value = True
        mock_update_service_number.return_value = True
        mock_make_number_live.return_value = {
            "status": "success",
            "dnid1": "dummy_did1",
            "dnid2": "dummy_did2",
            "service_number_new": "**********",
            "service_number_old": "**********",
            "company_id_myop": self.service.gsn,
            "message": "Number updated successfully",
        }

        global_package = GlobalPackageFactory.create(
            rent_per_month=199,
            renew_cycle=12,
        )
        ServiceActivation(self.service, self.payment_track).initiate_activation(
            package_id=global_package.id,
            service_number=self.service_number.service_number,
        )
        self.service.refresh_from_db()
        self.assertEqual(self.service.status, ServiceStatusEnum.ACTIVE.value)
        self.assertEqual(
            self.service.live_status, ServiceLiveStatusEnum.PREPAID.value
        )
        self.service_rental.refresh_from_db()
        self.assertEqual(self.service_rental.rental_amount, 0)
        self.assertEqual(
            self.service_rental.pending_rental,
            global_package.rent_per_month * global_package.renew_cycle,
        )
        self.assertEqual(self.service_rental.trans_type, "cr")
        self.assertEqual(
            self.service_rental.status, ServiceRentalStatusEnum.ACTIVE.value
        )
        self.assertEqual(
            self.service.expiry_date,
            add_months(
                self.service.activation_date,
                global_package.renew_cycle,
                self.service.billing_account.billing_day,
            ),
        )
        self.service_package.refresh_from_db()
        self.assertEqual(
            self.service_package.package.global_package, global_package
        )
        self.assertEqual(
            self.service_package.start_time, self.service.activation_date
        )
        self.assertEqual(
            self.service_package.end_time, self.service.expiry_date
        )
        self.assertEqual(
            ServiceRentals.objects.count(), 1
        )  # there should be one row in service_rentals table
        self.assertEqual(
            ServicePackages.objects.count(), 1
        )  # there should be one row in service_packages table
        self.assertEqual(
            ServiceNumbers.objects.filter(
                service=self.service,
                status=ServiceNumberStatusEnum.ACTIVE.value,
            ).count(),
            1,
        )  # there should be one row in service_numbers table

        service_number = ServiceNumbers.objects.get(
            service=self.service, status=ServiceNumberStatusEnum.ACTIVE.value
        )
        self.assertEqual(service_number.is_live, 1)
        self.assertEqual(service_number.is_paid, 1)
        self.assertEqual(
            service_number.is_archived, ServiceNumberArchiveEnum.FALSE.value
        )
        self.assertEqual(service_number.archive_date, None)
        self.assertEqual(service_number.swapped_date, None)
        self.assertEqual(
            service_number.status, ServiceNumberStatusEnum.ACTIVE.value
        )

        self.assertEqual(mock_update_permissions.call_count, 1)
        mock_update_permissions.assert_called_once_with(
            data={"gsn": self.service.gsn},
            country_short_code=self.service.product.country.short_code,
        )

        self.assertEqual(mock_update_service_number.call_count, 1)
        mock_update_service_number.assert_called_once_with(
            gsn=self.service.gsn,
            service_number=self.service_number.service_number,
            country_code=self.service.product.country.short_code,
            live_status=self.service.live_status,
            number_property=self.service_number.number_type,
            expiry=self.service.expiry_date.strftime("%Y-%m-%d %H:%M:%S"),
            dnid1="dummy_did1",
            dnid2="dummy_did2",
            status=ServiceStatusEnum.ACTIVE.value,
        )

        self.assertEqual(mock_make_number_live.call_count, 1)
        mock_make_number_live.assert_called_once_with(
            gsn=self.service.gsn,
            service_number=self.service_number.service_number,
            service_number_old=self.service_number.service_number,
            live_status=self.service.live_status,
            service_id=self.service.id,
            activation_date=self.service.activation_date.strftime(
                "%Y-%m-%d %H:%M:%S"
            ),
        )

    @mock.patch(
        "accounts.utils.api_services.number_system.NumberSystem.make_number_live"
    )
    @mock.patch(
        "accounts.utils.api_services.myoperator.MyOperator.update_service_number"
    )
    @mock.patch(
        "accounts.utils.api_services.myoperator.MyOperator.update_permissions"
    )
    @mock.patch(
        "accounts.utils.api_services.number_system.NumberSystem.fetch_demo_number"
    )
    @mock.patch(
        "accounts.utils.api_services.number_system.NumberSystem.get_number_details"
    )
    def test_service_activation_with_global_package_and_fetch_demo_number(
        self,
        mock_get_number_details,
        mock_fetch_demo_number,
        mock_update_permissions,
        mock_update_service_number,
        mock_make_number_live,
    ):
        mock_get_number_details.return_value = {
            "display_number": "myop280",
            "number_cost": 0,
            "number_status": "open",
            "is_mapped": "1",
            "country_short_code": "IN",
            "country_id_code": "91",
            "currency": "INR",
            "number_type": "5",
        }
        mock_fetch_demo_number.return_value = {
            "display_number": "myop228",
            "tele_code": "91",
            "country_id": "99",
            "country_short_code": "IN",
            "country_isd_code": "91",
            "number_type": "5",
            "did1": "myop256",
            "did2": "myop257",
        }
        mock_update_permissions.return_value = True
        mock_update_service_number.return_value = True
        mock_make_number_live.return_value = {
            "status": "success",
            "dnid1": "dummy_did1",
            "dnid2": "dummy_did2",
            "service_number_new": "**********",
            "service_number_old": "**********",
            "company_id_myop": self.service.gsn,
            "message": "Number updated successfully",
        }

        global_package = GlobalPackageFactory.create(
            rent_per_month=199,
            renew_cycle=12,
        )
        ServiceActivation(self.service, self.payment_track).initiate_activation(
            package_id=global_package.id
        )
        self.service.refresh_from_db()
        self.assertEqual(self.service.status, ServiceStatusEnum.ACTIVE.value)
        self.assertEqual(
            self.service.live_status, ServiceLiveStatusEnum.PREPAID.value
        )
        self.service_rental.refresh_from_db()
        self.assertEqual(self.service_rental.rental_amount, 0)
        self.assertEqual(
            self.service_rental.pending_rental,
            global_package.rent_per_month * global_package.renew_cycle,
        )
        self.assertEqual(self.service_rental.trans_type, "cr")
        self.assertEqual(
            self.service_rental.status, ServiceRentalStatusEnum.ACTIVE.value
        )
        self.assertEqual(
            self.service.expiry_date,
            add_months(
                self.service.activation_date,
                global_package.renew_cycle,
                self.service.billing_account.billing_day,
            ),
        )
        self.service_package.refresh_from_db()
        self.assertEqual(
            self.service_package.package.global_package, global_package
        )
        self.assertEqual(
            self.service_package.start_time, self.service.activation_date
        )
        self.assertEqual(
            self.service_package.end_time, self.service.expiry_date
        )
        self.assertEqual(
            ServiceRentals.objects.count(), 1
        )  # there should be one row in service_rentals table
        self.assertEqual(
            ServicePackages.objects.count(), 1
        )  # there should be one row in service_packages table
        self.assertEqual(
            ServiceNumbers.objects.filter(
                service=self.service,
                status=ServiceNumberStatusEnum.ACTIVE.value,
            ).count(),
            1,
        )  # there should be one row in service_numbers table

        service_number = ServiceNumbers.objects.get(
            service=self.service, status=ServiceNumberStatusEnum.ACTIVE.value
        )
        self.assertEqual(service_number.is_live, 1)
        self.assertEqual(service_number.is_paid, 1)
        self.assertEqual(
            service_number.is_archived, ServiceNumberArchiveEnum.FALSE.value
        )
        self.assertEqual(service_number.archive_date, None)
        self.assertEqual(service_number.swapped_date, None)
        self.assertEqual(
            service_number.status, ServiceNumberStatusEnum.ACTIVE.value
        )

        self.assertEqual(mock_update_permissions.call_count, 1)
        mock_update_permissions.assert_called_once_with(
            data={"gsn": self.service.gsn},
            country_short_code=self.service.product.country.short_code,
        )

        self.assertEqual(mock_update_service_number.call_count, 1)
        mock_update_service_number.assert_called_once_with(
            gsn=self.service.gsn,
            service_number=mock_fetch_demo_number.return_value[
                "display_number"
            ],
            country_code=self.service.product.country.short_code,
            live_status=self.service.live_status,
            number_property=mock_fetch_demo_number.return_value["number_type"],
            expiry=self.service.expiry_date.strftime("%Y-%m-%d %H:%M:%S"),
            dnid1="dummy_did1",
            dnid2="dummy_did2",
            status=ServiceStatusEnum.ACTIVE.value,
        )

        self.assertEqual(mock_make_number_live.call_count, 1)
        mock_make_number_live.assert_called_once_with(
            gsn=self.service.gsn,
            service_number=mock_fetch_demo_number.return_value[
                "display_number"
            ],
            service_number_old=self.service_number.service_number,
            live_status=self.service.live_status,
            service_id=self.service.id,
            activation_date=self.service.activation_date.strftime(
                "%Y-%m-%d %H:%M:%S"
            ),
        )

    @mock.patch(
        "accounts.utils.api_services.number_system.NumberSystem.make_number_live"
    )
    @mock.patch(
        "accounts.utils.api_services.myoperator.MyOperator.update_service_number"
    )
    @mock.patch(
        "accounts.utils.api_services.myoperator.MyOperator.update_permissions"
    )
    @mock.patch(
        "accounts.utils.api_services.number_system.NumberSystem.get_number_details"
    )
    def test_service_activation_without_global_package_and_existing_service_number(
        self,
        mock_get_number_details,
        mock_update_permissions,
        mock_update_service_number,
        mock_make_number_live,
    ):
        payment_track = PaymentTracksFactory.create(
            amount=100, txn_settle_status=1
        )
        service = ServiceFactory.create(
            billing_account=BillingAccountFactory(
                status=BillingStatusEnum.INACTIVE.value,
            ),
            status=ServiceStatusEnum.INACTIVE.value,
            live_status=ServiceLiveStatusEnum.DEMO.value,
        )
        service_number = ServiceNumberFactory.create(
            service=service,
            service_number="**********",
            number_cost=0,
            is_live=0,
            is_paid=0,
            number_type=5,
        )
        service_package = ServicePackageFactory.create(
            service=service,
        )
        service_rental = ServiceRentalFactory.create(
            service=service, rental_amount=0, pending_rental=0
        )
        TrackingSettlementHistoriesFactory.create(
            billing_account=service.billing_account,
            payment=payment_track,
            amount=payment_track.amount,
            setl_key=payment_track.txn_setl_key,
        )
        mock_get_number_details.return_value = {
            "display_number": "**********",
            "number_cost": 0,
            "number_status": "live",
            "is_mapped": "1",
            "country_short_code": "IN",
            "country_id_code": "91",
            "currency": "INR",
            "number_type": "5",
        }

        mock_update_permissions.return_value = True
        mock_update_service_number.return_value = True
        mock_make_number_live.return_value = {
            "status": "success",
            "dnid1": "dummy_did1",
            "dnid2": "dummy_did2",
            "service_number_new": "**********",
            "service_number_old": "**********_old",
            "company_id_myop": service.gsn,
            "message": "Number updated successfully",
        }

        custom_package = PackageCustomFactory.create()
        package = PackageFactory.create(
            rent_per_month=199,
            renew_cycle=12,
            package_custom=custom_package,
        )
        ServiceActivation(service, payment_track).initiate_activation(
            package_id=package.id, service_number=service_number.service_number
        )
        service.refresh_from_db()
        self.assertEqual(service.status, ServiceStatusEnum.ACTIVE.value)
        self.assertEqual(
            service.live_status, ServiceLiveStatusEnum.PREPAID.value
        )
        service_rental.refresh_from_db()
        self.assertEqual(service_rental.rental_amount, 0)
        self.assertEqual(
            service_rental.pending_rental,
            package.rent_per_month * package.renew_cycle,
        )
        self.assertEqual(service_rental.trans_type, "cr")
        self.assertEqual(
            service_rental.status, ServiceRentalStatusEnum.ACTIVE.value
        )
        self.assertEqual(
            service.expiry_date,
            add_months(
                service.activation_date,
                package.renew_cycle,
                service.billing_account.billing_day,
            ),
        )
        service_package.refresh_from_db()
        self.assertEqual(service_package.package, package)
        self.assertEqual(service_package.start_time, service.activation_date)
        self.assertEqual(service_package.end_time, service.expiry_date)
        self.assertEqual(
            ServiceRentals.objects.filter(service=service).count(), 1
        )  # there should be one row in service_rentals table
        self.assertEqual(
            ServicePackages.objects.filter(service=service).count(), 1
        )  # there should be one row in service_packages table
        self.assertEqual(
            ServiceNumbers.objects.filter(
                service=service, status=ServiceNumberStatusEnum.ACTIVE.value
            ).count(),
            1,
        )  # there should be one row in service_numbers table

        service_number = ServiceNumbers.objects.get(
            service=service, status=ServiceNumberStatusEnum.ACTIVE.value
        )
        self.assertEqual(service_number.is_live, 1)
        self.assertEqual(service_number.is_paid, 1)
        self.assertEqual(
            service_number.is_archived, ServiceNumberArchiveEnum.FALSE.value
        )
        self.assertEqual(service_number.archive_date, None)
        self.assertEqual(service_number.swapped_date, None)
        self.assertEqual(
            service_number.status, ServiceNumberStatusEnum.ACTIVE.value
        )

        self.assertEqual(mock_update_permissions.call_count, 1)
        mock_update_permissions.assert_called_once_with(
            data={"gsn": service.gsn},
            country_short_code=service.product.country.short_code,
        )

        self.assertEqual(mock_update_service_number.call_count, 1)
        mock_update_service_number.assert_called_once_with(
            gsn=service.gsn,
            service_number=service_number.service_number,
            country_code=service.product.country.short_code,
            live_status=service.live_status,
            number_property=service_number.number_type,
            expiry=service.expiry_date.strftime("%Y-%m-%d %H:%M:%S"),
            dnid1="dummy_did1",
            dnid2="dummy_did2",
            status=ServiceStatusEnum.ACTIVE.value,
        )

        self.assertEqual(mock_make_number_live.call_count, 1)
        mock_make_number_live.assert_called_once_with(
            gsn=service.gsn,
            service_number=service_number.service_number,
            service_number_old=service_number.service_number,
            live_status=service.live_status,
            service_id=service.id,
            activation_date=service.activation_date.strftime(
                "%Y-%m-%d %H:%M:%S"
            ),
        )

    def test_service_activation_invalid_payment(self):
        payment_track = PaymentTracksFactory.create(
            amount=100, txn_settle_status=1
        )
        package = GlobalPackageFactory.create(
            rent_per_month=199,
            renew_cycle=12,
        )
        with pytest.raises(InvalidPaymentException):
            ServiceActivation(self.service, payment_track).initiate_activation(
                package_id=package.id
            )

    def test_service_activation_with_fraud_billing(self):
        billing = BillingAccountFactory.create(
            verification_state=BillingVerificationStateEnum.FRAUD.value
        )
        service = ServiceFactory.create(billing_account=billing)
        payment_track = PaymentTracksFactory.create(
            amount=100, txn_settle_status=1
        )
        TrackingSettlementHistoriesFactory.create(
            billing_account=service.billing_account,
            payment=payment_track,
            amount=payment_track.amount,
            setl_key=payment_track.txn_setl_key,
        )
        package = GlobalPackageFactory.create(
            rent_per_month=199,
            renew_cycle=12,
        )
        with pytest.raises(FraudBillingAccountException):
            ServiceActivation(service, payment_track).initiate_activation(
                package_id=package.id
            )

    def test_service_activation_not_demo_service(self):
        billing = BillingAccountFactory.create()
        service = ServiceFactory.create(
            billing_account=billing,
            live_status=ServiceLiveStatusEnum.PREPAID.value,
        )
        payment_track = PaymentTracksFactory.create(
            amount=100, txn_settle_status=1
        )
        TrackingSettlementHistoriesFactory.create(
            billing_account=service.billing_account,
            payment=payment_track,
            amount=payment_track.amount,
            setl_key=payment_track.txn_setl_key,
        )
        package = GlobalPackageFactory.create(
            rent_per_month=199,
            renew_cycle=12,
        )
        with pytest.raises(InvalidServiceException):
            ServiceActivation(service, payment_track).initiate_activation(
                package_id=package.id
            )

    @mock.patch(
        "accounts.utils.api_services.number_system.NumberSystem.get_number_details"
    )
    def test_service_activation_number_not_open(self, mock_get_number_details):
        billing = BillingAccountFactory.create()
        service = ServiceFactory.create(
            billing_account=billing,
            live_status=ServiceLiveStatusEnum.DEMO.value,
        )
        payment_track = PaymentTracksFactory.create(
            amount=100, txn_settle_status=1
        )
        service_number = ServiceNumberFactory.create(
            service=service,
            service_number="**********",
            number_cost=0,
            is_live=0,
            is_paid=0,
            number_type=5,
        )
        TrackingSettlementHistoriesFactory.create(
            billing_account=service.billing_account,
            payment=payment_track,
            amount=payment_track.amount,
            setl_key=payment_track.txn_setl_key,
        )
        package = GlobalPackageFactory.create(
            rent_per_month=199,
            renew_cycle=12,
        )
        mock_get_number_details.return_value = {
            "display_number": "**********",
            "number_cost": 0,
            "number_status": "booked",
            "is_mapped": "1",
            "country_short_code": "IN",
            "country_id_code": "91",
            "currency": "INR",
            "number_type": "5",
        }
        with pytest.raises(ServiceActivationFailedException):
            ServiceActivation(service, payment_track).initiate_activation(
                package_id=package.id,
                service_number=service_number.service_number,
            )

    # def test_service_activation_invalid_billing_account(self):
    #     with pytest.raises(InvalidBillingAccountException) as excinfo:
    #         service_activation("ban123", "payment123")
    #     assert str(excinfo.value) == "Billing Account not found"

    # def test_service_activation_fraud_billing_account(self):
    #     billing_account = BillingAccountFactory.create(verification_state=5)
    #     with pytest.raises(FraudBillingAccountException) as excinfo:
    #         service_activation(billing_account.id, "payment123")
    #     assert str(excinfo.value) == "Billing Account is fraud"

    # def test_service_activation_invalid_service(self):
    #     billing_account = BillingAccountFactory.create()
    #     with pytest.raises(InvalidServiceException) as excinfo:
    #         service_activation(billing_account.id, "payment123")
    #     assert str(excinfo.value) == "Service not found"

    # def test_service_activation_non_demo_service(self):
    #     recharge = RechargesFactory.create(
    #         payment_id="abc_333", request_of="activation"
    #     )
    #     ServiceFactory.create(
    #         billing_account=recharge.billing_account, live_status=1
    #     )
    #     with pytest.raises(InvalidServiceException) as excinfo:
    #         service_activation(recharge.billing_account.id, recharge.payment_id)
    #     assert str(excinfo.value) == "Not a demo service"

    # def test_service_activation_payment_not_settled(self):
    #     recharge = RechargesFactory.create(
    #         payment_id="abc_333", request_of="activation"
    #     )
    #     ServiceFactory.create(
    #         billing_account=recharge.billing_account, status=0
    #     )
    #     with pytest.raises(ServiceActivationFailedException) as excinfo:
    #         service_activation(recharge.billing_account.id, recharge.payment_id)
    #     assert str(excinfo.value) == "Invalid Payment or not settled"

    # @mock.patch(
    #     "accounts.utils.api_services.account_v1.AccountApiV1.initiate_activation"
    # )
    # def test_service_activation_failed(self, mock_initiate_activation):
    #     mock_initiate_activation.return_value = False
    #     with pytest.raises(ServiceActivationFailedException) as excinfo:
    #         service_activation(
    #             self.recharge.billing_account.id, self.recharge.payment_id
    #         )
    #     assert str(excinfo.value) == "Service activation failed"
