from decimal import Decimal
from unittest.mock import patch

from django.test import TestCase

from accounts.billing_accounts.tests.factories import (
    BillingAccountCreditsFactory,
)
from accounts.core.tests.factories import StateCodeFactory, TaxVoicetreeFactory
from accounts.discounts.enums import DiscountApplyOnEnum
from accounts.discounts.tests.factories import DiscountFactory
from accounts.global_packages.tests.factories import GlobalPackageFactory
from accounts.packages.enums import PackageStatusEnum
from accounts.packages.exceptions import InvalidPackageException
from accounts.packages.tests.factories import (
    PackageCustomFactory,
    PackageFactory,
)
from accounts.packages.utils.create_package import (
    create_package_from_custom_package,
)
from accounts.payments.enums import (
    PaymentActionStatusEnum,
    PaymentActionTypeEnum,
)
from accounts.payments.exceptions import PaymentLinkGenerationException
from accounts.payments.models import PaymentActions
from accounts.services.enums import ServiceLiveStatusEnum
from accounts.services.exceptions import (
    ServiceAlreadyRunningException,
    ServiceNotDemoException,
)
from accounts.services.tests.factories import (
    ServiceFactory,
    ServiceNumberFactory,
)
from accounts.services.utils.activation_payment import ActivationPaymentService


class TestActivationPayment(TestCase):
    def setUp(self):
        self.state_code = StateCodeFactory.create()
        TaxVoicetreeFactory.create_gst_tax(self.state_code)
        self.service = ServiceFactory.create(
            billing_account__state_id=self.state_code.id,
            product__country=self.state_code.country,
        )
        self.global_package = GlobalPackageFactory.create(
            rent_per_month=100, renew_cycle=12
        )

    @patch(
        "accounts.utils.api_services.number_system.NumberSystem.get_number_details"
    )
    def test_fetch_payable_amount_with_service_number(
        self, mock_number_details
    ):
        mock_number_details.return_value = {
            "number_cost": 50,
            "number_status": "open",
        }

        res = ActivationPaymentService(
            self.service, self.global_package.id, "myop123"
        ).fetch_payable_amount()
        self.assertEqual(
            res,
            {
                "rental_amount": Decimal(1200),
                "number_cost": Decimal(50),
                "additional_cost": Decimal(0),
                "discount_amount": Decimal(0),
                "advance_amount": Decimal(0),
                "amount_without_tax": Decimal(1250),
                "tax_details": [
                    {
                        "name": "GST",
                        "tax_percent": Decimal(18),
                        "amount": Decimal(1250),
                        "tax": Decimal(225),
                    }
                ],
                "tax_total": Decimal(225),
                "payable_amount": Decimal(1475),
                "currency": "INR",
            },
        )
        mock_number_details.assert_called_once()

    @patch(
        "accounts.utils.api_services.number_system.NumberSystem.get_number_details"
    )
    def test_fetch_payable_amount_without_service_number(
        self, mock_number_details
    ):
        res = ActivationPaymentService(
            self.service, self.global_package.id
        ).fetch_payable_amount()
        self.assertEqual(
            res,
            {
                "rental_amount": Decimal(1200),
                "number_cost": Decimal(0),
                "additional_cost": Decimal(0),
                "discount_amount": Decimal(0),
                "advance_amount": Decimal(0),
                "amount_without_tax": Decimal(1200),
                "tax_details": [
                    {
                        "name": "GST",
                        "tax_percent": Decimal(18),
                        "amount": Decimal(1200),
                        "tax": Decimal(216),
                    }
                ],
                "tax_total": Decimal(216),
                "payable_amount": Decimal(1416),
                "currency": "INR",
            },
        )
        mock_number_details.assert_not_called()

    @patch(
        "accounts.utils.api_services.number_system.NumberSystem.get_number_details"
    )
    def test_fetch_payable_amount_with_custom_package(
        self, mock_number_details
    ):
        custom_package = PackageCustomFactory.create()
        package = create_package_from_custom_package(custom_package.id)

        res = ActivationPaymentService(
            self.service, package.id
        ).fetch_payable_amount()
        self.assertEqual(
            res,
            {
                "rental_amount": Decimal(4800.000),
                "number_cost": Decimal(0),
                "additional_cost": Decimal(50),
                "discount_amount": Decimal(0),
                "advance_amount": Decimal(0),
                "amount_without_tax": Decimal(4850),
                "tax_details": [
                    {
                        "name": "GST",
                        "tax_percent": Decimal(18),
                        "amount": Decimal(4850),
                        "tax": Decimal(873),
                    }
                ],
                "tax_total": Decimal(873),
                "payable_amount": Decimal(5723),
                "currency": "INR",
            },
        )
        mock_number_details.assert_not_called()

    @patch(
        "accounts.utils.api_services.number_system.NumberSystem.get_number_details"
    )
    def test_fetch_payable_amount_with_prepaid_amount(
        self, mock_number_details
    ):
        BillingAccountCreditsFactory.create(
            billing_account=self.service.billing_account,
            credit_amount=100,
            trans_type="cr",
        )
        res = ActivationPaymentService(
            self.service, self.global_package.id
        ).fetch_payable_amount()
        self.assertEqual(
            res,
            {
                "rental_amount": Decimal(1200),
                "number_cost": Decimal(0),
                "additional_cost": Decimal(0),
                "discount_amount": Decimal(0),
                "advance_amount": Decimal(100),
                "amount_without_tax": Decimal(1100),
                "tax_details": [
                    {
                        "name": "GST",
                        "tax_percent": Decimal(18),
                        "amount": Decimal(1100),
                        "tax": Decimal(198),
                    }
                ],
                "tax_total": Decimal(198),
                "payable_amount": Decimal(1298),
                "currency": "INR",
            },
        )
        mock_number_details.assert_not_called()

    @patch(
        "accounts.utils.api_services.number_system.NumberSystem.get_number_details"
    )
    def test_fetch_payable_amount_with_greater_prepaid_amount(
        self, mock_number_details
    ):
        BillingAccountCreditsFactory.create(
            billing_account=self.service.billing_account,
            credit_amount=10000,
            trans_type="cr",
        )
        res = ActivationPaymentService(
            self.service, self.global_package.id
        ).fetch_payable_amount()
        self.assertEqual(
            res,
            {
                "rental_amount": Decimal(1200),
                "number_cost": Decimal(0),
                "additional_cost": Decimal(0),
                "discount_amount": Decimal(0),
                "advance_amount": Decimal(10000),
                "amount_without_tax": Decimal(-8800.000),
                "tax_details": [],
                "tax_total": Decimal(0),
                "payable_amount": Decimal(0),
                "currency": "INR",
            },
        )
        mock_number_details.assert_not_called()

    def test_fetch_payable_amount_with_rental_discount(self):
        custom_package = PackageCustomFactory.create(additional_cost=0)
        discount = DiscountFactory.create(
            value=100, apply_on=DiscountApplyOnEnum.RENTAL.value
        )
        package = PackageFactory(
            rent_per_month=100,
            renew_cycle=12,
            package_custom=custom_package,
            global_package=None,
            discount=discount,
        )
        res = ActivationPaymentService(
            self.service, package.id
        ).fetch_payable_amount()
        self.assertEqual(
            res,
            {
                "rental_amount": Decimal(1200),
                "number_cost": Decimal(0),
                "additional_cost": Decimal(0),
                "discount_amount": Decimal(100),
                "advance_amount": Decimal(0),
                "amount_without_tax": Decimal(1100),
                "tax_details": [
                    {
                        "name": "GST",
                        "tax_percent": Decimal(18),
                        "amount": Decimal(1100),
                        "tax": Decimal(198),
                    }
                ],
                "tax_total": Decimal(198),
                "payable_amount": Decimal(1298),
                "currency": "INR",
            },
        )

    def test_fetch_payable_amount_with_rental_discount_greater_than_rental(
        self,
    ):
        custom_package = PackageCustomFactory.create(additional_cost=0)
        discount = DiscountFactory.create(
            value=1500, apply_on=DiscountApplyOnEnum.RENTAL.value
        )
        package = PackageFactory(
            rent_per_month=100,
            renew_cycle=12,
            package_custom=custom_package,
            global_package=None,
            discount=discount,
        )
        res = ActivationPaymentService(
            self.service, package.id
        ).fetch_payable_amount()
        self.assertEqual(
            res,
            {
                "rental_amount": Decimal(1200),
                "number_cost": Decimal(0),
                "additional_cost": Decimal(0),
                "discount_amount": Decimal(1200),
                "advance_amount": Decimal(0),
                "amount_without_tax": Decimal(0),
                "tax_details": [],
                "tax_total": Decimal(0),
                "payable_amount": Decimal(0),
                "currency": "INR",
            },
        )

    def test_fetch_payable_amount_with_usage_discount(self):
        """Usage discount is only applied in monhtly statement or package change"""
        custom_package = PackageCustomFactory.create(additional_cost=0)
        discount = DiscountFactory.create(
            value=100, apply_on=DiscountApplyOnEnum.USAGES.value
        )
        package = PackageFactory(
            rent_per_month=100,
            renew_cycle=12,
            package_custom=custom_package,
            global_package=None,
            discount=discount,
        )
        res = ActivationPaymentService(
            self.service, package.id
        ).fetch_payable_amount()
        self.assertEqual(
            res,
            {
                "rental_amount": Decimal(1200),
                "number_cost": Decimal(0),
                "additional_cost": Decimal(0),
                "discount_amount": Decimal(0),
                "advance_amount": Decimal(0),
                "amount_without_tax": Decimal(1200),
                "tax_details": [
                    {
                        "name": "GST",
                        "tax_percent": Decimal(18),
                        "amount": Decimal(1200),
                        "tax": Decimal(216),
                    }
                ],
                "tax_total": Decimal(216),
                "payable_amount": Decimal(1416),
                "currency": "INR",
            },
        )

    @patch(
        "accounts.utils.api_services.number_system.NumberSystem.get_number_details"
    )
    def test_fetch_payable_amount_with_all_discount(self, mock_number_details):
        mock_number_details.return_value = {
            "number_cost": 50,
            "number_status": "open",
        }
        custom_package = PackageCustomFactory.create(additional_cost=50)
        discount = DiscountFactory.create(
            value=1500, apply_on=DiscountApplyOnEnum.ALL.value
        )
        package = PackageFactory(
            rent_per_month=100,
            renew_cycle=12,
            package_custom=custom_package,
            global_package=None,
            discount=discount,
        )
        res = ActivationPaymentService(
            self.service, package.id, service_number="myop123"
        ).fetch_payable_amount()
        self.assertEqual(
            res,
            {
                "rental_amount": Decimal(1200),
                "number_cost": Decimal(50),
                "additional_cost": Decimal(50),
                "discount_amount": Decimal(1300),
                "advance_amount": Decimal(0),
                "amount_without_tax": Decimal(0),
                "tax_details": [],
                "tax_total": Decimal(0),
                "payable_amount": Decimal(0),
                "currency": "INR",
            },
        )
        mock_number_details.assert_called_once()

    def test_fetch_payable_amount_with_invalid_package(self):
        # test with inactive custom package
        custom_package = PackageCustomFactory.create(additional_cost=50)
        package1 = PackageFactory(
            package_custom=custom_package,
            status=PackageStatusEnum.INACTIVE.value,
        )
        with self.assertRaises(InvalidPackageException):
            ActivationPaymentService(
                self.service, package1.id
            ).fetch_payable_amount()

        # test with inactive global package
        package2 = GlobalPackageFactory(status=PackageStatusEnum.INACTIVE.value)
        with self.assertRaises(InvalidPackageException):
            ActivationPaymentService(
                self.service, package2.id
            ).fetch_payable_amount()

        # test with non custom package
        package3 = PackageFactory()
        with self.assertRaises(InvalidPackageException):
            ActivationPaymentService(
                self.service, package3.id
            ).fetch_payable_amount()

        # test with invalid package id
        with self.assertRaises(InvalidPackageException):
            ActivationPaymentService(
                self.service, "invalid_package_id"
            ).fetch_payable_amount()

    @patch(
        "accounts.utils.api_services.payment_service.PaymentService.generate_payment_url"
    )
    def test_generate_payment_link(self, mock_generate_payment_url):
        mock_generate_payment_url.return_value = {
            "payment_id": "123abc",
            "url": "https://example.com/123abc",
        }
        aps = ActivationPaymentService(self.service, self.global_package.id)
        payable_amount = aps.fetch_payable_amount()["payable_amount"]
        payment_id, url = aps.generate_payment_link(
            redirect_url="https://google.com",
            name="John Doe",
            phone="**********",
            email="<EMAIL>",
        )
        self.assertEqual(payment_id, "123abc")
        self.assertEqual(url, "https://example.com/123abc")
        payment_action = PaymentActions.objects.first()
        self.assertEqual(payment_action.payment_id, payment_id)
        self.assertEqual(
            payment_action.action_type,
            PaymentActionTypeEnum.SERVICE_ACTIVATION.value,
        )
        self.assertEqual(
            payment_action.action_data,
            {
                "package_id": str(self.global_package.id),
                "service_id": self.service.id,
                "service_number": None,
            },
        )
        self.assertEqual(
            payment_action.status, PaymentActionStatusEnum.PENDING.value
        )
        mock_generate_payment_url.assert_called_once_with(
            billing_account_id=self.service.billing_account_id,
            ac_number=self.service.billing_account.ac_number,
            amount=payable_amount,
            country_id=self.service.product.country_id,
            state_id=self.service.billing_account.state_id,
            name="John Doe",
            phone="**********",
            email="<EMAIL>",
            request_of="activation",
            response_url="https://google.com",
            payment_method=None,
        )

    @patch(
        "accounts.utils.api_services.payment_service.PaymentService.generate_payment_url"
    )
    @patch(
        "accounts.utils.api_services.number_system.NumberSystem.get_number_details"
    )
    def test_generate_payment_link_with_service_number(
        self, mock_number_details, mock_generate_payment_url
    ):
        mock_number_details.return_value = {
            "number_cost": 0,
            "number_status": "open",
        }
        mock_generate_payment_url.return_value = {
            "payment_id": "123abc",
            "url": "https://example.com/123abc",
        }
        aps = ActivationPaymentService(
            self.service, self.global_package.id, service_number="myop123"
        )
        payable_amount = aps.fetch_payable_amount()["payable_amount"]
        payment_id, url = aps.generate_payment_link(
            redirect_url="https://google.com",
            name="John Doe",
            phone="**********",
            email="<EMAIL>",
        )
        self.assertEqual(payment_id, "123abc")
        self.assertEqual(url, "https://example.com/123abc")
        payment_action = PaymentActions.objects.first()
        self.assertEqual(payment_action.payment_id, payment_id)
        self.assertEqual(
            payment_action.action_type,
            PaymentActionTypeEnum.SERVICE_ACTIVATION.value,
        )
        self.assertEqual(
            payment_action.action_data,
            {
                "package_id": str(self.global_package.id),
                "service_id": self.service.id,
                "service_number": "myop123",
            },
        )
        self.assertEqual(
            payment_action.status, PaymentActionStatusEnum.PENDING.value
        )
        mock_generate_payment_url.assert_called_once_with(
            billing_account_id=self.service.billing_account_id,
            ac_number=self.service.billing_account.ac_number,
            amount=payable_amount,
            country_id=self.service.product.country_id,
            state_id=self.service.billing_account.state_id,
            name="John Doe",
            phone="**********",
            email="<EMAIL>",
            request_of="activation",
            response_url="https://google.com",
            payment_method=None,
        )
        mock_number_details.assert_called()

    @patch(
        "accounts.utils.api_services.payment_service.PaymentService.generate_payment_url"
    )
    def test_generate_payment_link_for_non_demo_account(
        self, mock_generate_payment_url
    ):
        self.service.live_status = ServiceLiveStatusEnum.PREPAID.value
        self.service.save()
        aps = ActivationPaymentService(self.service, self.global_package.id)
        with self.assertRaises(ServiceNotDemoException):
            aps.generate_payment_link("https://google.com")
        mock_generate_payment_url.assert_not_called()

    @patch(
        "accounts.utils.api_services.payment_service.PaymentService.generate_payment_url"
    )
    def test_generate_payment_link_for_allocated_service_number(
        self, mock_generate_payment_url
    ):
        ServiceNumberFactory(service_number="myop123")
        aps = ActivationPaymentService(
            self.service, self.global_package.id, "myop123"
        )
        with self.assertRaises(ServiceAlreadyRunningException):
            aps.generate_payment_link("https://google.com")
        mock_generate_payment_url.assert_not_called()

    @patch(
        "accounts.utils.api_services.payment_service.PaymentService.generate_payment_url"
    )
    def test_generate_payment_link_with_already_fully_paid(
        self, mock_generate_payment_url
    ):
        BillingAccountCreditsFactory.create(
            billing_account=self.service.billing_account,
            credit_amount=10000,
        )
        aps = ActivationPaymentService(self.service, self.global_package.id)
        with self.assertRaises(PaymentLinkGenerationException):
            aps.generate_payment_link(
                redirect_url="https://google.com",
                name="John Doe",
                phone="**********",
                email="<EMAIL>",
            )

        self.assertFalse(PaymentActions.objects.exists())
        mock_generate_payment_url.assert_not_called()

    @patch(
        "accounts.utils.api_services.payment_service.PaymentService.generate_payment_url"
    )
    @patch(
        "accounts.utils.api_services.number_system.NumberSystem.get_number_details"
    )
    def test_generate_payment_link_with_custom_amount(
        self, mock_number_details, mock_generate_payment_url
    ):
        mock_number_details.return_value = {
            "number_cost": 0,
            "number_status": "open",
        }
        mock_generate_payment_url.return_value = {
            "payment_id": "123abc",
            "url": "https://example.com/123abc",
        }
        aps = ActivationPaymentService(
            self.service, self.global_package.id, service_number="myop123"
        )
        payment_id, url = aps.generate_payment_link(
            redirect_url="https://google.com",
            name="John Doe",
            phone="**********",
            email="<EMAIL>",
            custom_amount=Decimal(100),
        )
        self.assertEqual(payment_id, "123abc")
        self.assertEqual(url, "https://example.com/123abc")
        payment_action = PaymentActions.objects.first()
        self.assertEqual(payment_action.payment_id, payment_id)
        self.assertEqual(
            payment_action.action_type,
            PaymentActionTypeEnum.SERVICE_ACTIVATION.value,
        )
        self.assertEqual(
            payment_action.action_data,
            {
                "package_id": str(self.global_package.id),
                "service_id": self.service.id,
                "service_number": "myop123",
            },
        )
        self.assertEqual(
            payment_action.status, PaymentActionStatusEnum.PENDING.value
        )
        mock_generate_payment_url.assert_called_once_with(
            billing_account_id=self.service.billing_account_id,
            ac_number=self.service.billing_account.ac_number,
            amount=Decimal(100),
            country_id=self.service.product.country_id,
            state_id=self.service.billing_account.state_id,
            name="John Doe",
            phone="**********",
            email="<EMAIL>",
            request_of="activation",
            response_url="https://google.com",
            payment_method=None,
        )
        mock_number_details.assert_called()
