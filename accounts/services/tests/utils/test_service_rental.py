from django.test import TestCase
from django.utils import timezone
from accounts.services.tests.factories import (
    ServiceFactory,
    ServiceRentalFactory,
)
from accounts.services.utils.service_rental import is_pending_rental


class TestServiceRentals(TestCase):
    def setUp(self):
        self.service = ServiceFactory.create(
            user_profile=None, live_status=1, status=1
        )

    def test_is_pending_rental_false(self):
        ServiceRentalFactory.create(
            service_id=self.service.id,
            pending_rental=0,
            rental_amount=1,
            status=1,
            date=timezone.now(),
        )
        assert is_pending_rental(self.service.id) is False

    def test_is_pending_rental_true(self):
        ServiceRentalFactory.create(
            service_id=self.service.id,
            pending_rental=100,
            rental_amount=0,
            status=1,
            date=timezone.now(),
        )
        assert is_pending_rental(self.service.id) is True
