from freezegun import freeze_time
from datetime import timedelta
from dateutil.relativedelta import relativedelta
from django.utils import timezone
from django.test import TestCase

from accounts.products.tests.factories import ProductFeatureFactory
from accounts.services.tests.factories import (
    ServiceFactory,
    ServicePackageFactory,
    FeatureUsagesLogFactory,
)
from accounts.packages.enums import PackageFeatureStatusEnum
from accounts.packages.tests.factories import PackageFeatureFactory
from accounts.services.models import FeatureUsagesLogs

from accounts.services.utils.update_feature_usage import FeatureUsageUpdater


class TestFeatureUsageUpdater(TestCase):
    @freeze_time("2023-04-01 18:30:00")
    def setUp(self):
        self.service = ServiceFactory.create()
        self.service_package = ServicePackageFactory.create(
            service=self.service,
            start_time=timezone.now(),
            end_time=timezone.now()
            + relativedelta(months=self.service.renew_cycle),
        )
        self.product_feature = ProductFeatureFactory(
            product=self.service.product, billing_type="N"
        )
        self.package_feature = PackageFeatureFactory.create(
            package=self.service_package.package,
            product_feature=self.product_feature,
        )

    @freeze_time("2023-04-01 18:35:00")
    def test_create_feature_usage_log(self):
        self.assertEqual(FeatureUsagesLogs.objects.count(), 0)

        feature_usage = FeatureUsageUpdater(
            self.service, self.service_package.start_time, timezone.now()
        )
        log = feature_usage.update(self.package_feature.id, 1)
        self.assertIsInstance(log, FeatureUsagesLogs)

        feature_usage_log = FeatureUsagesLogs.objects.first()
        self.assertEqual(feature_usage_log.service, self.service)
        self.assertEqual(
            feature_usage_log.package_feature, self.package_feature
        )
        self.assertEqual(feature_usage_log.leg_a_use, 1)
        self.assertEqual(feature_usage_log.leg_b_min, 0)
        self.assertEqual(feature_usage_log.leg_b_rate, 0.00)
        self.assertEqual(
            feature_usage_log.start_date, self.service_package.start_time
        )
        self.assertEqual(feature_usage_log.end_date, timezone.now())

    @freeze_time("2023-04-01 18:35:00")
    def test_update_feature_usage_log(self):
        feature_usage_log = FeatureUsagesLogFactory(
            service=self.service,
            package_feature=self.package_feature,
            leg_a_use=1,
            start_date=self.service_package.start_time,
        )
        self.assertEqual(FeatureUsagesLogs.objects.count(), 1)

        feature_usage = FeatureUsageUpdater(
            self.service, self.service_package.start_time, timezone.now()
        )
        updated_log = feature_usage.update(self.package_feature.id, 5)
        self.assertIsInstance(updated_log, FeatureUsagesLogs)

        self.assertEqual(updated_log.id, feature_usage_log.id)
        feature_usage_log = FeatureUsagesLogs.objects.first()
        self.assertEqual(feature_usage_log.service, self.service)
        self.assertEqual(
            feature_usage_log.package_feature, self.package_feature
        )
        self.assertEqual(feature_usage_log.leg_a_use, 5)
        self.assertEqual(feature_usage_log.leg_b_min, 0)
        self.assertEqual(feature_usage_log.leg_b_rate, 0.00)
        self.assertEqual(
            feature_usage_log.start_date, self.service_package.start_time
        )
        self.assertEqual(feature_usage_log.end_date, timezone.now())
