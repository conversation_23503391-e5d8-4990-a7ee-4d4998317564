import pytest

from django.test import TestCase

from freezegun import freeze_time
from django.utils import timezone
from accounts.services.models import ServiceNumbers
from accounts.services import constants
from accounts.services.tests.factories import (
    ServiceFactory,
    ServiceNumberFactory,
)
from accounts.global_packages.tests.factories import (
    PackageCategoryFactory,
)
from accounts.products.tests.factories import (
    ProductFactory,
    ProductSettingFactory,
    ProductDefaultSettingFactory,
)
from accounts.core.tests.factories import CountryFactory
from accounts.services.utils.service_number import (
    add_new_service_number,
    free_old_service_numbers,
    get_service_number_details,
    calculate_archive_date,
)


class TestServiceNumber(TestCase):
    def setUp(self):
        self.country = CountryFactory.create()
        self.product = ProductFactory.create(country=self.country)
        self.service = ServiceFactory.create(
            product=self.product,
            country=self.country,
            user_profile=None,
        )
        self.package_category = PackageCategoryFactory.create(
            product=self.product,
            code="abcd",
        )
        self.service_number = ServiceNumberFactory.create(
            service=self.service,
            service_number="**********",
            is_paid=1,
            status=1,
        )
        self.setting_key = f"SERVICE_NUMBER_ARCHIVE_DAYS_{self.service.live_status}_{self.package_category.code}"  # noqa

    def test_free_old_service_numbers_success(self):
        free_old_service_numbers(self.service.id)

        result = ServiceNumbers.objects.filter(
            service_id=self.service.id
        ).first()

        assert result.status == 0
        assert result.service_number == "**********_free"

    def test_free_service_number_failure(self):
        result = free_old_service_numbers(12345)
        assert result is not True

    def test_add_new_service_number_success(self):
        service_number = "987654321"
        new_number_details = {"number_cost": 100, "number_type": "1"}

        result = add_new_service_number(
            service_id=self.service.id,
            service_number=service_number,
            number_type=new_number_details.get("number_type", 1),
            number_cost=new_number_details.get("number_cost", 0),
            is_live=1,
        )
        assert result.service_id == self.service.id
        assert result.service_number == service_number
        assert result.number_type == new_number_details["number_type"]
        assert result.number_cost == new_number_details["number_cost"]
        assert result.is_live == 1

    def test_get_service_number_details_success(self):
        result = get_service_number_details(self.service.id)
        assert result.service_number == self.service_number.service_number

    def test_get_service_number_details_failure(self):
        result = get_service_number_details(12345)
        assert result is None

    @freeze_time("2023-04-01 18:35:00")
    def test_calculate_archive_date_with_product_setting(self):
        days = 7
        ProductSettingFactory.create(
            product=self.product,
            setting_key=self.setting_key,
            setting_value=days,
        )
        archive_date = calculate_archive_date(
            self.product.id,
            self.service.live_status,
            self.package_category.code,
        )
        expected_archive_date = timezone.now() + timezone.timedelta(days=days)
        assert archive_date == expected_archive_date

    @freeze_time("2023-04-01 18:35:00")
    def test_calculate_archive_date_with_product_default_setting(self):
        days = 2
        ProductDefaultSettingFactory.create(
            setting_key=self.setting_key, setting_value=days
        )
        archive_date = calculate_archive_date(
            self.product.id,
            self.service.live_status,
            self.package_category.code,
        )
        expected_archive_date = timezone.now() + timezone.timedelta(days=days)
        assert archive_date == expected_archive_date

    @freeze_time("2023-04-01 18:35:00")
    def test_calculate_archive_date_without_setting(self):
        days = constants.DEFAULT_ARCHIVE_DAYS
        archive_date = calculate_archive_date(
            self.product.id,
            self.service.live_status,
            self.package_category.code,
        )
        expected_archive_date = timezone.now() + timezone.timedelta(days=days)
        assert archive_date == expected_archive_date

    @freeze_time("2023-04-01 18:35:00")
    def test_calculate_archive_date_with_minimum_days(self):
        days = 0
        ProductDefaultSettingFactory.create(
            setting_key=self.setting_key, setting_value=days
        )
        archive_date = calculate_archive_date(
            self.product.id,
            self.service.live_status,
            self.package_category.code,
        )
        expected_archive_date = timezone.now() + timezone.timedelta(
            days=constants.DEFAULT_ARCHIVE_DAYS
        )
        assert archive_date == expected_archive_date
