import pytest
from faker import Faker

from django.test import TestCase
from django.utils import timezone

from accounts.billing_accounts.tests.factories import BillingAccountFactory
from accounts.core.tests.factories import CountryFactory
from accounts.discounts.tests.factories import DiscountFactory
from accounts.products.tests.factories import ProductFactory
from accounts.services import constants
from accounts.services.models import (
    ServicePackages,
    ServiceRentals,
    Services,
    ServiceNumbers,
)
from accounts.services.tests.factories import (
    ServiceFactory,
    ServiceRentalFactory,
    ServicePackageFactory,
    ServiceNumberFactory,
)
from accounts.packages.tests.factories import (
    PackageFactory,
)
from accounts.global_packages.tests.factories import (
    PackageCategoryFactory,
)


class TestModels(TestCase):
    def setUp(self):
        self.fake = Faker()

        self.current_datetime = timezone.now()
        self.expiry_datetime = timezone.now() + timezone.timedelta(days=365)
        self.current_date = timezone.now()
        self.start_date = timezone.now() - timezone.timedelta(days=30)
        self.end_date = timezone.now() - timezone.timedelta(days=2)

        # services
        self.country = CountryFactory.create()
        self.product = ProductFactory.create(country=self.country)
        self.billing_account = BillingAccountFactory.create(
            discount=None,
        )
        self.service = ServiceFactory.create(
            product=self.product,
            billing_account=self.billing_account,
            country=self.country,
            live_status=3,
            expiry_date=self.expiry_datetime,
            user_profile=None,
        )

        self.service_number = ServiceNumberFactory.create(
            service=self.service,
            service_number="1234",
        )

        # create faker with active live status 1
        ServiceFactory.create_batch(
            size=2, user_profile=None, live_status=1, status=1
        )

        # create faker with active live status 2
        ServiceFactory.create_batch(
            size=2, user_profile=None, live_status=2, status=1
        )

        # create faker with status inactive
        ServiceFactory.create_batch(
            size=2, user_profile=None, live_status=1, status=0
        )

        # create faker with status suspended
        ServiceFactory.create_batch(
            size=2, user_profile=None, live_status=1, status=2
        )

        self.service_rental = ServiceRentalFactory.create(
            service=self.service,
            rental_amount=0,
            pending_rental=0,
            trans_type="cr",
            status=1,
            date=self.current_date,
        )

        # inactive service rentals
        self.inactive_service = ServiceFactory.create(
            user_profile=None,
            status=0,
        )
        ServiceRentalFactory.create(
            service=self.inactive_service,
            rental_amount=0,
            pending_rental=0,
            trans_type="cr",
            status=0,
            date=self.current_date,
        )
        package_categoy = PackageCategoryFactory.create(
            product=self.product,
            code="CategoryCode",
            name="Category Name",
            proposal_file="proposal.pdf",
            description="Category description",
        )
        discount = DiscountFactory.create(
            code="ABCD123",
        )
        self.package = PackageFactory.create(
            product_id=self.product.id,
            name="Test Package",
            package_type="Type",
            rent_per_month=100.0,
            renew_cycle=12,
            package_custom=None,
            code="ABCDE123",
            ocs_flag=2,
            package_for="vn",
            package_category_id=package_categoy.id,
            package_number="1234",
            discount_id=discount.id,
            description="This is a test package.",
        )
        # service Packages
        self.service_package = ServicePackageFactory.create(
            service=self.service,
            package=self.package,
            start_time=self.start_date,
            end_time=self.expiry_datetime,
        )

        # inactive service Packages
        ServicePackageFactory.create(
            service=self.inactive_service,
            package=self.package,
            start_time=self.start_date,
            end_time=self.end_date,
        )

    def test_product_manager(self):

        # test case with data found
        result = Services.objects.product(self.product.id).first()
        assert result == self.service
        assert result.product.id == self.service.product.id

        # test case with data not found
        result = Services.objects.product("123456").first()
        assert result is None

    def test_service_billing_account_manager(self):

        # test case with data found
        result = Services.objects.billing_account(self.billing_account).first()
        assert result == self.service
        assert result.billing_account == self.billing_account

    def test_service_not_found(self):
        # test case with data not found
        billing_account = BillingAccountFactory.create()
        result = Services.objects.billing_account(billing_account).first()
        assert result is None

    def test_active_service_manager(self):

        count = Services.active.count()
        assert count == 5

    def test_inactive_service_manager(self):

        count = Services.inactive.count()
        assert count == 3

    def test_suspended_service_manager(self):

        count = Services.suspended.count()
        assert count == 2

    def test_live_service_manager(self):

        count = Services.live.count()
        assert count == 8

    def test_service_rental_manager(self):

        # test case with data found
        result = ServiceRentals.objects.service(self.service.id).first()
        assert result == self.service_rental
        assert result.service_id == self.service_rental.service_id

        # test case with data not found
        result = ServiceRentals.objects.service("123456").first()
        assert result is None

    def test_active_service_rental_manager(self):

        # test case with data found
        # result = ServiceRentals.objects.active_service(self.service.id)
        result = ServiceRentals.objects.service(self.service.id).active()
        assert result == self.service_rental
        assert result.service_id == self.service_rental.service_id

        # test case with data not found
        # result = ServiceRentals.objects.active_service(self.inactive_service.id)
        result = ServiceRentals.objects.service(
            self.inactive_service.id
        ).active()
        assert result is None

        count = ServiceRentals.active.count()
        assert count == 1

    def test_service_package_manager(self):

        # test case with data found
        result = ServicePackages.objects.service(self.service.id).first()
        assert result == self.service_package
        assert result.service_id == self.service_package.service_id

        # test case with data not found
        result = ServicePackages.objects.service("123456").first()
        assert result is None

    def test_active_service_package_manager(self):

        # test case with data found
        result = ServicePackages.objects.service(self.service.id).active(
            self.current_datetime
        )
        assert result == self.service_package
        assert result.service_id == self.service_package.service_id

        # test case with data not found
        result = ServicePackages.objects.service(
            self.inactive_service.id
        ).active(self.current_datetime)
        assert result is None

        count = ServicePackages.objects.active_packages(
            self.current_datetime
        ).count()
        assert count == 1

    def test_is_demo_service_success(self):
        assert self.service.is_demo() is True

    def test_is_demo_service_failure(self):
        self.service.live_status = constants.SERVICE_LIVE_STATUS_PREPAID
        self.service.save()
        assert self.service.is_demo() is not True

    def test_service_deactivate_success(self):
        self.service.deactivate()
        assert self.service.status == constants.SERVICE_STATUS_INACTIVE
        assert self.service.churn_date is not None

    def test_service_deactivate_negative(self):
        with pytest.raises(Services.DoesNotExist):
            service = Services.objects.get(id=12345678)
            service.deactivate()

    def test_package_expire_success(self):
        self.service_package.expire()
        assert self.service_package.end_time.strftime(
            "%Y-%m-%d %H:%M:%S"
        ) == timezone.now().strftime("%Y-%m-%d %H:%M:%S")

    def test_package_expire_failure(self):
        with pytest.raises(ServicePackages.DoesNotExist):
            service_package = ServicePackages.objects.get(id=12345678)
            service_package.expire()

    def test_set_archive_date_success(self):
        archive_date = timezone.now()
        self.service_number.set_archive_date(archive_date)
        self.service_number.refresh_from_db()
        assert (
            self.service_number.is_archived
            == constants.SERVICE_NUMBER_ARCHIVED_NO
        )
        assert self.service_number.archive_date == archive_date
        assert (
            self.service_number.status
            == constants.SERVICE_NUMBER_STATUS_INACTIVE
        )
