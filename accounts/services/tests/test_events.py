import pytest
from django.test import TestCase
import unittest.mock as mock
from accounts.services.events import (
    ServiceActivationEvent,
    ServiceDeactivationEvent,
    ServiceTerminationEvent,
)
from accounts.billing_accounts.tests.factories import BillingAccountFactory
from accounts.core.tests.factories import CountryFactory
from accounts.products.tests.factories import ProductFactory
from accounts.services.tests.factories import ServiceFactory


class TestEvents(TestCase):
    def setUp(self):
        self.country = CountryFactory.create()
        self.product = ProductFactory.create(
            name="MyOperator India", country=self.country, short_code="myopin"
        )
        self.product_2 = ProductFactory.create(
            name="Heyo India", country=self.country, short_code="heyoin"
        )
        self.account = BillingAccountFactory.create(ac_number="ABCXYZ")
        ServiceFactory.create(
            billing_account=self.account,
            product_id=self.product.id,
            gsn="abc123",
            status=1,
            live_status=1,
        )

        self.account_2 = BillingAccountFactory.create()
        ServiceFactory.create(
            billing_account=self.account_2,
            product_id=self.product_2.id,
        )

    @classmethod
    def sns_client(cls):
        sns_client = mock.MagicMock()
        sns_client.publish.return_value = {"MessageId": "1234"}
        return sns_client

    def test_service_activation_event_success(self):
        sns_client = self.sns_client()
        event = ServiceActivationEvent()
        event.company_id("abc123")
        event.sns = sns_client
        response = event.send()
        assert response == "1234"
        assert event.EVENT_ACTION == "service_activated"
        assert event.service_type == "myopin"
        assert event.data["company_id"] == "abc123"

    def test_service_activation_event_failure(self):
        event = ServiceActivationEvent()
        with pytest.raises(TypeError):
            event.company_id()

    def test_service_deactivation_event_success(self):
        sns_client = self.sns_client()
        event = ServiceDeactivationEvent()
        event.company_id("abc123")
        event.sns = sns_client
        response = event.send()
        assert response == "1234"
        assert event.EVENT_ACTION == "service_deactivated"
        assert event.service_type == "myopin"
        assert event.data["company_id"] == "abc123"

    def test_service_deactivation_event_failure(self):
        event = ServiceDeactivationEvent()
        with pytest.raises(TypeError):
            event.company_id()

    def test_service_termination_event_success(self):
        sns_client = self.sns_client()
        event = ServiceTerminationEvent()
        event.company_id("abc123")
        event.sns = sns_client
        response = event.send()
        assert response == "1234"
        assert event.EVENT_ACTION == "service_terminated"
        assert event.service_type == "myopin"
        assert event.data["company_id"] == "abc123"

    def test_service_termination_event_failure(self):
        event = ServiceTerminationEvent()
        with pytest.raises(TypeError):
            event.company_id()

    def test_invalid_company_exception(self):
        event = ServiceActivationEvent()
        event.company_id("aaa")
        with pytest.raises(ValueError):
            event.send()
