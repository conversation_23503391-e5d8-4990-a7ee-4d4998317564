from unittest.mock import patch
from freezegun import freeze_time
from dateutil.relativedelta import relativedelta

from django.utils import timezone
from django.test import TestCase

from accounts.products.tests.factories import ProductFeatureFactory
from accounts.packages.tests.factories import PackageFeatureFactory
from accounts.services.tests.factories import (
    ServiceFactory,
    ServicePackageFactory,
)
from accounts.packages.enums import PackageFeatureStatusEnum
from accounts.services.management.commands.billing_event_consumer import (
    Command,
    FeatureUsageParser,
)


class BillingEventConsumerTest(TestCase):
    def setUp(self):
        self.service = ServiceFactory.create()
        with freeze_time("2023-04-01 18:30:00"):
            self.service_package = ServicePackageFactory.create(
                service=self.service,
                start_time=timezone.now(),
                end_time=timezone.now()
                + relativedelta(months=self.service.renew_cycle),
            )
        self.product_feature = ProductFeatureFactory.create(
            product=self.service.product, billing_type="N"
        )
        self.package_feature = PackageFeatureFactory.create(
            package=self.service_package.package,
            product_feature=self.product_feature,
        )

    @patch(
        "accounts.services.utils.update_feature_usage.FeatureUsageUpdater.update"
    )
    def test_process_message(self, mock_feature_usage_updater):
        mock_feature_usage_updater.return_value = True
        units = 1
        parsed_message = FeatureUsageParser(
            {
                "company_id": self.service.gsn,
                "features": [
                    {
                        "resource_key": self.package_feature.get_resource_key(),
                        "units": units,
                    }
                ],
            }
        )
        Command()._process_message(parsed_message)

        # Ensure each feature was processed
        self.assertEqual(mock_feature_usage_updater.call_count, 1)
        mock_feature_usage_updater.assert_called_once_with(
            self.package_feature.id, units
        )

    @patch(
        "accounts.services.utils.update_feature_usage.FeatureUsageUpdater.update"
    )
    def test_process_message_with_multiple_features(
        self, mock_feature_usage_updater
    ):
        mock_feature_usage_updater.return_value = True
        package_feature_2 = PackageFeatureFactory.create(
            package=self.service_package.package,
            product_feature=self.product_feature,
        )
        package_feature_3 = PackageFeatureFactory.create(
            package=self.service_package.package,
            product_feature=self.product_feature,
        )
        parsed_message = FeatureUsageParser(
            {
                "company_id": self.service.gsn,
                "features": [
                    {
                        "resource_key": self.package_feature.get_resource_key(),
                        "units": 1,
                    },
                    {
                        "resource_key": package_feature_2.get_resource_key(),
                        "units": 5,
                    },
                    {
                        "resource_key": package_feature_3.get_resource_key(),
                        "units": 0,
                    },
                ],
            }
        )
        Command()._process_message(parsed_message)

        # Ensure each feature was processed
        self.assertEqual(mock_feature_usage_updater.call_count, 2)

    @freeze_time("2023-04-01 18:35:00")
    def test_fetch_features(self):
        package_feature_2 = PackageFeatureFactory.create(
            package=self.service_package.package,
            product_feature=self.product_feature,
        )
        package_feature_3 = PackageFeatureFactory.create(
            package=self.service_package.package,
            product_feature=self.product_feature,
        )

        features = Command()._fetch_features(
            self.service, self.service_package.start_time, timezone.now()
        )
        self.assertEqual(len(features), 3)
        self.assertIn(self.package_feature.get_resource_key(), features)
        self.assertIn(package_feature_2.get_resource_key(), features)
        self.assertIn(package_feature_3.get_resource_key(), features)
        self.assertEqual(
            features,
            {
                self.package_feature.get_resource_key(): self.package_feature.id,
                package_feature_2.get_resource_key(): package_feature_2.id,
                package_feature_3.get_resource_key(): package_feature_3.id,
            },
        )

    def test_get_package_features_with_disabled_features(self):
        package_feature_2 = PackageFeatureFactory.create(
            package=self.service_package.package,
            product_feature=self.product_feature,
        )
        with freeze_time("2023-04-05 00:00:00"):
            package_feature_3 = PackageFeatureFactory.create(
                package=self.service_package.package,
                product_feature=self.product_feature,
                status=PackageFeatureStatusEnum.DISABLED.value,
                last_disabled_date=timezone.now(),
            )

        with freeze_time("2023-03-05 00:00:00"):
            package_feature_4 = PackageFeatureFactory.create(
                package=self.service_package.package,
                product_feature=self.product_feature,
                status=PackageFeatureStatusEnum.DISABLED.value,
                last_disabled_date=timezone.now(),
            )

        with freeze_time("2023-04-05 00:00:01"):
            features = Command()._fetch_features(
                self.service, self.service_package.start_time, timezone.now()
            )

        self.assertEqual(len(features), 3)
        self.assertIn(self.package_feature.get_resource_key(), features)
        self.assertIn(package_feature_2.get_resource_key(), features)
        self.assertIn(package_feature_3.get_resource_key(), features)
        self.assertNotIn(package_feature_4.get_resource_key(), features)
        self.assertEqual(
            features,
            {
                self.package_feature.get_resource_key(): self.package_feature.id,
                package_feature_2.get_resource_key(): package_feature_2.id,
                package_feature_3.get_resource_key(): package_feature_3.id,
            },
        )
