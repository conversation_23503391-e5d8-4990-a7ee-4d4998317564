from unittest import mock
from django.test import TestCase, override_settings
from django.core.management import call_command
from accounts.services.tests.factories import ServiceFactory
from accounts.billing_accounts.tests.factories import (
    BillingAccountFactory,
    BillingAccountCreditsFactory,
)
from accounts.services.tests.factories import ResourceChargeFactory
from accounts.services.models import ResourceCharges
from accounts.billing_accounts.models import BillingAccountCredits
from accounts.exceptions import ChatAPIException


class TestResourcePendingRefundCommand(TestCase):
    def setUp(self):
        self.billing_account = BillingAccountFactory.create()
        self.billing_credit = BillingAccountCreditsFactory.create(
            billing_account=self.billing_account, credit_amount=100
        )
        self.service = ServiceFactory.create(
            billing_account=self.billing_account
        )

    def test_process_pending_refund_with_no_resources(self):
        call_command("refund_pending_resource_charges")

    @override_settings(
        RESOURCE_REFUND_CONFIG={"REFUND_AFTER": 0, "MAX_REFUND_DAYS": 7}
    )
    @mock.patch("accounts.utils.api_services.chat.ChatApi.get_campaign_detail")
    def test_process_pending_refund_task_success(self, mock_api):
        resource = ResourceChargeFactory.create(
            resource_id=123, service=self.service, estimated_amount=100
        )
        mock_api.return_value = {
            "id": 1,
            "billing": {"expected_cost": 100, "actual_cost": 90.5},
            "status": "completed",
        }
        call_command("refund_pending_resource_charges")

        updated_resource = ResourceCharges.objects.get(id=resource.id)
        old_billing_credit = BillingAccountCredits.objects.get(
            id=self.billing_credit.id
        )
        new_billing_credit = BillingAccountCredits.objects.get(
            billing_account=self.billing_account, status=1
        )
        assert old_billing_credit.status == 0
        assert new_billing_credit.credit_amount == 109.50
        assert new_billing_credit.trans_type == "cr"
        assert (
            new_billing_credit.description
            == f"9.500 refund credited for: {resource.resource}, {resource.resource_id}"
        )
        assert updated_resource.is_refunded is True
        assert updated_resource.estimated_amount == 100
        assert updated_resource.actual_amount == 90.5

    @override_settings(
        RESOURCE_REFUND_CONFIG={"REFUND_AFTER": 0, "MAX_REFUND_DAYS": 7}
    )
    @mock.patch("accounts.utils.api_services.chat.ChatApi.get_campaign_detail")
    def test_process_pending_refund_task_with_chat_api_exception(
        self, mock_api
    ):
        resource = ResourceChargeFactory.create(
            resource_id=123, service=self.service, estimated_amount=100
        )
        resource = ResourceChargeFactory.create(
            resource_id=456, service=self.service, estimated_amount=50
        )
        # Mock `get_campaign_detail` to raise an exception
        mock_api.side_effect = ChatAPIException()
        call_command("refund_pending_resource_charges")

        updated_resource = ResourceCharges.objects.get(id=resource.id)
        old_billing_credit = BillingAccountCredits.objects.get(
            id=self.billing_credit.id
        )
        assert old_billing_credit.status == 1
        assert updated_resource.is_refunded is False
        assert updated_resource.estimated_amount == 50
        assert updated_resource.actual_amount is None

    @override_settings(
        RESOURCE_REFUND_CONFIG={"REFUND_AFTER": 0, "MAX_REFUND_DAYS": 7}
    )
    @mock.patch("accounts.utils.api_services.chat.ChatApi.get_campaign_detail")
    def test_process_pending_refund_task_with_base_exception(self, mock_api):
        resource = ResourceChargeFactory.create(
            resource_id=123, service=self.service, estimated_amount=100
        )
        resource = ResourceChargeFactory.create(
            resource_id=456, service=self.service, estimated_amount=50
        )
        # Mock `get_campaign_detail` to raise an exception
        mock_api.side_effect = Exception("Test exception")
        with self.assertRaises(Exception):
            call_command("refund_pending_resource_charges")

        updated_resource = ResourceCharges.objects.get(id=resource.id)
        old_billing_credit = BillingAccountCredits.objects.get(
            id=self.billing_credit.id
        )
        assert old_billing_credit.status == 1
        assert updated_resource.is_refunded is False
        assert updated_resource.estimated_amount == 50
        assert updated_resource.actual_amount is None
