from unittest import skip
from django.test import TestCase

from rest_framework import status
from rest_framework.test import APIClient
from rest_framework.response import Response

from django.urls import reverse

from accounts.services.tests.factories import (
    ServiceFactory,
    ServiceRentalFactory,
    ServiceNumberFactory,
    OtherChargesFactory,
)
from accounts.services.utils.pending_usage import get_pending_usage
from accounts.services.cache_handler import PendingUsageCacheHandler


class TestPendingUsages(TestCase):
    def setUp(self):
        self.service = ServiceFactory.create(current_usages=100, status=1)
        ServiceRentalFactory.create(
            pending_rental=200, service=self.service, status=1
        )
        ServiceNumberFactory.create(
            number_cost=300, service=self.service, status=1, is_paid=0
        )
        OtherChargesFactory.create(charge=150, service=self.service, status=1)

    def test_get_pending_usages_success(self):
        url = reverse(
            "services:pending_usage", kwargs={"gsn": self.service.gsn}
        )
        client = APIClient()

        response = client.get(url, format="json")

        assert response.status_code == status.HTTP_200_OK

        response = response.json()
        assert response["status"] == "success"
        assert response["code"] == 200
        assert response["message"] == "Pending Usages"

        assert response["data"]["pending_rental"] == 200.0
        assert response["data"]["pending_usages"] == 0
        assert response["data"]["current_usages"] == 100.0
        assert response["data"]["other_charges"] == 150.0
        assert response["data"]["service_number_cost"] == 300.0
        assert response["data"]["discount"] == 0
        assert response["data"]["advance"] == 0.0
        assert response["data"]["available"] == -750.0
        assert response["data"]["currency"] == "INR"

    def test_get_pending_usages_404(self):
        url = reverse("services:pending_usage", kwargs={"gsn": "1111"})
        client = APIClient()

        response = client.get(url, format="json")

        assert response.status_code == status.HTTP_404_NOT_FOUND
        response = response.json()
        assert response["status"] == "error"
        assert response["code"] == "not_found"
        assert response["message"] == "Not found."

    @skip(
        "Cached results are disabled in view, enable this test case once it is enabled."
    )
    def test_get_pending_usages_with_cache(self):
        # save data in cache before calling the api
        get_pending_usage(self.service)
        # add other changes in service
        OtherChargesFactory.create(service=self.service, charge=200, status=1)

        url = reverse(
            "services:pending_usage", kwargs={"gsn": self.service.gsn}
        )
        client = APIClient()

        response = client.get(url, format="json")
        assert response.status_code == status.HTTP_200_OK
        response = response.json()

        assert response["data"]["pending_rental"] == 200.0
        assert response["data"]["pending_usages"] == 0
        assert response["data"]["current_usages"] == 100.0
        # data retrived from cache, other charge should be same as old
        assert response["data"]["other_charges"] == 150.0
        assert response["data"]["service_number_cost"] == 300.0
        assert response["data"]["discount"] == 0
        assert response["data"]["advance"] == 0.0
        assert response["data"]["available"] == -750.0
        assert response["data"]["currency"] == "INR"

    @skip(
        "Cached results are disabled in view, enable this test case once it is enabled."
    )
    def test_get_pending_usages_with_cache_false(self):
        # save data in cache before calling the api
        get_pending_usage(self.service)
        # add other changes in service
        OtherChargesFactory.create(service=self.service, charge=200, status=1)

        url = reverse(
            "services:pending_usage", kwargs={"gsn": self.service.gsn}
        )
        client = APIClient()
        response = client.get(url, data={"cache": "false"}, format="json")
        assert response.status_code == status.HTTP_200_OK
        response = response.json()

        assert response["data"]["pending_rental"] == 200.0
        assert response["data"]["pending_usages"] == 0
        assert response["data"]["current_usages"] == 100.0
        # data retrived from db, other charge should be updated
        assert response["data"]["other_charges"] == 350.0
        assert response["data"]["service_number_cost"] == 300.0
        assert response["data"]["discount"] == 0
        assert response["data"]["advance"] == 0
        assert response["data"]["available"] == -950.0
        assert response["data"]["currency"] == "INR"

    @skip(
        "Cached results are disabled in view, enable this test case once it is enabled."
    )
    def test_get_pending_usages_with_cache_invalidate(self):
        # save data in cache before calling the api
        get_pending_usage(self.service)
        # add other changes in service
        OtherChargesFactory.create(service=self.service, charge=200, status=1)

        url = reverse(
            "services:pending_usage", kwargs={"gsn": self.service.gsn}
        )
        client = APIClient()
        response = client.get(url, data={"cache": "false"}, format="json")
        assert response.status_code == status.HTTP_200_OK
        response = response.json()

        assert response["data"]["pending_rental"] == 200.0
        assert response["data"]["pending_usages"] == 0
        assert response["data"]["current_usages"] == 100.0
        # data retrived from db, other charge should be updated
        assert response["data"]["other_charges"] == 350.0
        assert response["data"]["service_number_cost"] == 300.0
        assert response["data"]["discount"] == 0
        assert response["data"]["advance"] == 0
        assert response["data"]["available"] == -950.0
        assert response["data"]["currency"] == "INR"
