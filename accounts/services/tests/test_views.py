from datetime import datetime, timed<PERSON><PERSON>
from unittest import mock

from django.test import TestCase
from django.urls import reverse
from django.utils import timezone

import pytest
from rest_framework import status
from rest_framework.serializers import ValidationError
from rest_framework.test import APIClient

from accounts.billing_accounts.tests.factories import (
    BillingAccountCreditsFactory,
    BillingAccountFactory,
)
from accounts.discounts.tests.factories import DiscountBucketsFactory
from accounts.payments.tests.factories import (
    PaymentActionFactory,
)
from accounts.billing_accounts.enums import BillingStatusEnum
from accounts.services.enums import ServiceLiveStatusEnum, ServiceStatusEnum
from accounts.services.models import ResourceCharges, Services
from accounts.services.serializers import BillingAccountSerializer
from accounts.services.tests.factories import (
    OtherChargesFactory,
    ResourceChargeFactory,
    ServiceFactory,
    ServiceNumberFactory,
    ServiceRentalFactory,
)
from accounts.services.views import ServiceActivationEventListenerView


class TestServiceActivationEventListenerView(TestCase):
    def setUp(self):
        self.payment_action = PaymentActionFactory.create(
            payment_id="abc_123_333"
        )
        self.payment_action_not_activation = PaymentActionFactory.create(
            payment_id="abc_456_333",
            action_type="not_activation",
        )
        self.view = ServiceActivationEventListenerView()

    @mock.patch("accounts.services.tasks.service_activation_task.apply_async")
    def test_notification_handler_positive(self, mock_task):
        mock_task.return_value = True
        response = self.view.notification_handler(
            {"payment_id": self.payment_action.payment_id}
        )
        assert response.data["message"] == "Task Scheduled"
        assert "task_id" in response.data["data"]

    @mock.patch("accounts.services.tasks.service_activation_task.apply_async")
    def test_notification_handler_negative_payment_id_missing(self, mock_task):
        with pytest.raises(ValidationError) as exc_info:
            mock_task.return_value = True
            self.view.notification_handler({})

        error_detail = exc_info.value.detail[0]
        assert error_detail == "payment_id is missing"

    def test_notification_handler_negative_payment_action_not_found(self):
        response = self.view.notification_handler(
            {"payment_id": "non_existing_id"}
        )
        assert "payment is not for activation" in response.data["message"]
        assert response.data["data"]["task_id"] is not True

    def test_notification_handler_negative_wrong_request(self):
        response = self.view.notification_handler(
            {"payment_id": self.payment_action_not_activation.payment_id}
        )
        assert "payment is not for activation" in response.data["message"]
        assert response.data["data"]["task_id"] is not True


class TestResourceChargeCreateAPIView(TestCase):
    def setUp(self):
        billing_account = BillingAccountFactory.create()
        self.service = ServiceFactory.create(billing_account=billing_account)

    def test_resource_charge_resource_already_exists(self):
        ResourceChargeFactory.create(resource_id=123)
        url = reverse(
            "services:deduct_balance", kwargs={"gsn": self.service.gsn}
        )
        response = self.client.post(
            url,
            {"resource": "campaign", "resource_id": 123, "deduct_amount": 2.25},
            content_type="application/json",
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["status"] == "error"
        assert response.json()["code"] == "ACC_4011"
        assert response.json()["message"] == "resource_id already exists"

    def test_resource_charge_deduct_balance_in_positive(self):
        url = reverse(
            "services:deduct_balance", kwargs={"gsn": self.service.gsn}
        )
        response = self.client.post(
            url,
            {"resource": "campaign", "resource_id": 123, "deduct_amount": 0},
            content_type="application/json",
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["status"] == "error"
        assert (
            response.json()["errors"]["deduct_amount"][0]
            == "deduct_amount must be greater than 0"
        )

    def test_resource_charge_deduct_balance_inactive_service(self):
        billing_account = BillingAccountFactory.create()
        service = ServiceFactory.create(
            billing_account=billing_account, status=0
        )
        url = reverse("services:deduct_balance", kwargs={"gsn": service.gsn})
        response = self.client.post(
            url,
            {"resource": "campaign", "resource_id": 123, "deduct_amount": 0},
            content_type="application/json",
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["status"] == "error"
        assert response.json()["errors"][0] == "Service is inactive"

    def test_resource_charge_insufficient_balance(self):
        service = ServiceFactory.create()
        BillingAccountCreditsFactory.create(
            billing_account=service.billing_account, credit_amount=5
        )
        url = reverse("services:deduct_balance", kwargs={"gsn": service.gsn})
        response = self.client.post(
            url,
            {"resource": "campaign", "resource_id": 123, "deduct_amount": 10},
            content_type="application/json",
        )
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json()["status"] == "error"
        assert response.json()["message"] == "Insufficient Balance"
        assert response.json()["code"] == "ACC_4010"

    def test_resource_charge_success(self):
        billing_account = BillingAccountFactory.create()
        BillingAccountCreditsFactory.create(
            billing_account=billing_account, credit_amount=700
        )
        service = ServiceFactory.create(
            billing_account=billing_account, current_usages=100, status=1
        )

        ServiceRentalFactory.create(
            pending_rental=200, service=service, status=1
        )
        ServiceNumberFactory.create(
            number_cost=300, service=service, status=1, is_paid=0
        )
        OtherChargesFactory.create(charge=150, service=self.service, status=1)
        DiscountBucketsFactory.create(
            billing_account=billing_account, apply_on="R", status=1, value=300
        )
        url = reverse("services:deduct_balance", kwargs={"gsn": service.gsn})
        response = self.client.post(
            url,
            {"resource": "campaign", "resource_id": 456, "deduct_amount": 10},
            content_type="application/json",
        )

        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"
        assert response.json()["message"] == "Balance Deducted"

        created_resource = ResourceCharges.objects.get(resource_id=456)
        assert created_resource.resource == "campaign"
        assert created_resource.estimated_amount == 10
        assert created_resource.actual_amount is None
        assert created_resource.is_refunded is False
        assert created_resource.refunded_at is None


class TestPendingUsageRetrieveAPIView(TestCase):
    def setUp(self):
        pass

    def test_pending_usage_404(self):
        url = reverse("services:pending_usage", kwargs={"gsn": "123"})
        response = self.client.get(
            url,
            {},
            format="json",
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.json()["status"] == "error"

    def test_pending_usage_inactive_service(self):
        billing_account = BillingAccountFactory.create()
        service = ServiceFactory.create(
            status=0, billing_account=billing_account, current_usages=100
        )
        ServiceRentalFactory.create(
            pending_rental=199, service=service, status=1
        )
        ServiceNumberFactory.create(
            number_cost=300, service=service, status=1, is_paid=0
        )
        DiscountBucketsFactory.create(
            billing_account=billing_account, apply_on="R", status=1, value=400
        )
        BillingAccountCreditsFactory.create(
            billing_account=billing_account, credit_amount=100
        )
        url = reverse("services:pending_usage", kwargs={"gsn": service.gsn})
        response = self.client.get(
            url,
            {},
            format="json",
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"
        assert response.json()["data"] == {
            "pending_rental": 199.0,
            "pending_usages": 0,
            "current_usages": 100.0,
            "other_charges": 0.0,
            "service_number_cost": 300.0,
            "discount": 199.0,
            "advance": 100.0,
            "available": -300.0,
            "currency": "INR",
        }

    def test_pending_usage_with_pooling_account(self):
        parent_billing = BillingAccountFactory.create()
        ban_1 = BillingAccountFactory.create(parent=parent_billing)
        ban_2 = BillingAccountFactory.create(parent=parent_billing)
        service = ServiceFactory.create(
            billing_account=ban_1, current_usages=100, status=1
        )
        service_2 = ServiceFactory.create(
            billing_account=ban_2, current_usages=50, status=1
        )
        ServiceRentalFactory.create(
            pending_rental=200, service=service, status=1
        )
        ServiceRentalFactory.create(
            pending_rental=200, service=service_2, status=1
        )
        ServiceNumberFactory.create(
            number_cost=600, service=service, status=1, is_paid=0
        )
        ServiceNumberFactory.create(
            number_cost=600, service=service_2, status=1, is_paid=0
        )
        DiscountBucketsFactory.create(
            billing_account=parent_billing, apply_on="U", status=1, value=100
        )
        BillingAccountCreditsFactory.create(
            billing_account=parent_billing, credit_amount=1000
        )

        url = reverse("services:pending_usage", kwargs={"gsn": service.gsn})
        response = self.client.get(
            url,
            {},
            format="json",
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"
        assert response.json()["data"] == {
            "pending_rental": 400.0,
            "pending_usages": 0.0,
            "current_usages": 150.0,
            "other_charges": 0.0,
            "service_number_cost": 1200.0,
            "discount": 100.0,
            "advance": 1000.0,
            "available": -650.0,
            "currency": "INR",
        }

    def test_pending_usage_for_non_pooling_account(self):
        billing_account = BillingAccountFactory.create()
        service = ServiceFactory.create(
            billing_account=billing_account, current_usages=200, status=1
        )
        ServiceRentalFactory.create(
            pending_rental=199, service=service, status=1
        )
        ServiceNumberFactory.create(
            number_cost=200, service=service, status=1, is_paid=0
        )
        DiscountBucketsFactory.create(
            billing_account=billing_account, apply_on="R", status=1, value=500
        )
        BillingAccountCreditsFactory.create(
            billing_account=billing_account, credit_amount=0
        )

        url = reverse("services:pending_usage", kwargs={"gsn": service.gsn})
        response = self.client.get(
            url,
            {},
            format="json",
        )
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["status"] == "success"
        assert response.json()["data"] == {
            "pending_rental": 199.0,
            "pending_usages": 0,
            "current_usages": 200.0,
            "other_charges": 0.0,
            "service_number_cost": 200.0,
            "discount": 199.0,
            "advance": 0,
            "available": -400.0,
            "currency": "INR",
        }


@pytest.mark.django_db
def test_service_list_view_non_expanded():
    ServiceFactory.create_batch(2)

    client = APIClient()
    response = client.get(reverse("services:services_list"))

    assert response.status_code == status.HTTP_200_OK

    data = response.json()

    assert data["code"] == status.HTTP_200_OK
    assert data["status"] == "success"
    assert data["pagination"] == {
        "count": 2,
        "current": 1,
        "per_page": 10,
        "total_pages": 1,
    }
    assert len(data["data"]) == 2

    for service_data in data["data"]:
        service_obj = Services.objects.get(id=service_data["id"])

        assert (
            service_data["status"]
            == ServiceStatusEnum.get_name(service_obj.status).lower()
        )
        assert (
            service_data["live_status"]
            == ServiceLiveStatusEnum.get_name(service_obj.live_status).lower()
        )
        assert "billing_account" not in service_data


@pytest.mark.django_db
def test_service_list_view_expanded_billing_account():
    ServiceFactory.create_batch(2)
    client = APIClient()
    response = client.get(
        reverse("services:services_list"), data={"expand": "billing_account"}
    )

    assert response.status_code == status.HTTP_200_OK
    data = response.json()

    assert data["code"] == status.HTTP_200_OK
    assert data["status"] == "success"
    assert len(data["data"]) == 2

    for service_data in data["data"]:
        obj = Services.objects.get(id=service_data["id"])
        billing_account_data = service_data["billing_account"]

        assert isinstance(billing_account_data, dict)
        assert (
            billing_account_data
            == BillingAccountSerializer(obj.billing_account).data
        )


@pytest.mark.django_db
@pytest.mark.parametrize(
    "statuses, expected_idxs",
    [
        ("active", [0]),  # only the ACTIVE service
        ("active,suspended", [0, 1]),  # both ACTIVE and SUSPENDED
    ],
)
def test_service_list_view_filter_by_status(statuses, expected_idxs):
    client = APIClient()
    url = reverse("services:services_list")

    services = [
        ServiceFactory(status=ServiceStatusEnum.ACTIVE.value),
        ServiceFactory(status=ServiceStatusEnum.SUSPENDED.value),
        ServiceFactory(status=ServiceStatusEnum.INACTIVE.value),
    ]
    resp = client.get(url, {"status": statuses})
    assert resp.status_code == 200

    data_ids = {item["id"] for item in resp.json()["data"]}
    expected_ids = {services[i].id for i in expected_idxs}
    assert data_ids == expected_ids


@pytest.mark.django_db
@pytest.mark.parametrize(
    "live_statuses, expected_idxs",
    [
        ("postpaid", [0]),  # only the POSTPAID service
        ("postpaid,prepaid", [0, 1]),  # POSTPAID & PREPAID
    ],
)
def test_service_list_view_filter_by_live_status(live_statuses, expected_idxs):
    client = APIClient()
    url = reverse("services:services_list")
    services = [
        ServiceFactory(live_status=ServiceLiveStatusEnum.POSTPAID.value),
        ServiceFactory(live_status=ServiceLiveStatusEnum.PREPAID.value),
        ServiceFactory(live_status=ServiceLiveStatusEnum.DEMO.value),
    ]
    resp = client.get(url, {"live_status": live_statuses})
    assert resp.status_code == 200

    data_ids = {item["id"] for item in resp.json()["data"]}
    expected_ids = {services[i].id for i in expected_idxs}
    assert data_ids == expected_ids


@pytest.mark.django_db
@pytest.mark.parametrize(
    "filter_key, non_match_offset",
    [
        ("activation_date__gte", -1),  # yesterday < today → no match
        ("activation_date__lte", 1),  # tomorrow > today  → no match
        ("activation_date", 1),  # exact vs tomorrow  → no match
    ],
)
def test_service_list_view_filter_by_activation_date(
    filter_key, non_match_offset
):
    client = APIClient()
    url = reverse("services:services_list")
    base_dt = datetime(2013, 12, 1, 18, 30, 0, tzinfo=timezone.utc)
    service_match = ServiceFactory(activation_date=base_dt)
    service_non_match = ServiceFactory(
        activation_date=base_dt + timedelta(days=non_match_offset)
    )

    resp = client.get(url, {filter_key: base_dt.strftime("%Y-%m-%dT%H:%M:%SZ")})
    assert resp.status_code == 200

    data_ids = {item["id"] for item in resp.json()["data"]}
    assert service_match.id in data_ids
    assert service_non_match.id not in data_ids


@pytest.mark.django_db
def test_service_list_view_filter_by_product_short_code():
    service_1 = ServiceFactory(product__short_code="heyoin")
    service_2 = ServiceFactory(product__short_code="myopin")

    client = APIClient()
    url = reverse("services:services_list")
    resp = client.get(url, {"product_short_code": "heyoin"})
    assert resp.status_code == 200
    assert resp.json()["pagination"]["count"] == 1
    assert resp.json()["data"][0]["product_id"] == service_1.product_id

    resp = client.get(url, {"product_short_code": "myopin"})
    assert resp.status_code == 200
    assert resp.json()["pagination"]["count"] == 1
    assert resp.json()["data"][0]["product_id"] == service_2.product_id

    resp = client.get(url, {"product_short_code": "heyoin,myopin"})
    assert resp.status_code == 200
    assert resp.json()["pagination"]["count"] == 2


class TestServiceBillingDetailsView(TestCase):
    def setUp(self):
        self.service = ServiceFactory.create()

    def test_service_billing_details_for_non_pooling_account(self):
        url = reverse(
            "services:billing_details", kwargs={"gsn": self.service.gsn}
        )
        response = self.client.get(url)
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["message"] == "Billing Account Info"
        assert (
            response.json()["data"]["billing_account_id"]
            == self.service.billing_account.id
        )
        assert response.json()["data"]["is_corporate"] is False
        assert (
            response.json()["data"]["ac_number"]
            == self.service.billing_account.ac_number
        )

    def test_service_billing_details_for_pooling_account(self):
        parent_billing_account = BillingAccountFactory.create()
        child_billing_account = BillingAccountFactory.create(
            parent=parent_billing_account
        )
        service = ServiceFactory.create(billing_account=child_billing_account)
        url = reverse("services:billing_details", kwargs={"gsn": service.gsn})
        response = self.client.get(url)
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["message"] == "Billing Account Info"
        assert (
            response.json()["data"]["billing_account_id"]
            == parent_billing_account.id
        )
        assert response.json()["data"]["is_corporate"] is True
        assert (
            response.json()["data"]["ac_number"]
            == parent_billing_account.ac_number
        )

    def test_service_billing_details_for_non_existing_service(self):
        url = reverse(
            "services:billing_details", kwargs={"gsn": "non_existing_gsn"}
        )
        response = self.client.get(url)
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert response.json()["message"] == "Not found."

    def test_service_billing_details_for_inactive_service(self):
        self.service.status = ServiceStatusEnum.INACTIVE.value
        self.service.save()
        url = reverse(
            "services:billing_details", kwargs={"gsn": self.service.gsn}
        )
        response = self.client.get(url)
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["message"] == "Billing Account Info"
        assert (
            response.json()["data"]["billing_account_id"]
            == self.service.billing_account.id
        )
        assert response.json()["data"]["is_corporate"] is False
        assert (
            response.json()["data"]["ac_number"]
            == self.service.billing_account.ac_number
        )

    def test_service_billing_details_for_suspended_service(self):
        self.service.status = ServiceStatusEnum.SUSPENDED.value
        self.service.save()
        url = reverse(
            "services:billing_details", kwargs={"gsn": self.service.gsn}
        )
        response = self.client.get(url)
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["message"] == "Billing Account Info"
        assert (
            response.json()["data"]["billing_account_id"]
            == self.service.billing_account.id
        )
        assert response.json()["data"]["is_corporate"] is False
        assert (
            response.json()["data"]["ac_number"]
            == self.service.billing_account.ac_number
        )

    def test_service_billing_details_for_demo_service(self):
        self.service.live_status = ServiceLiveStatusEnum.DEMO.value
        self.service.save()
        url = reverse(
            "services:billing_details", kwargs={"gsn": self.service.gsn}
        )
        response = self.client.get(url)
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["message"] == "Billing Account Info"
        assert (
            response.json()["data"]["billing_account_id"]
            == self.service.billing_account.id
        )
        assert response.json()["data"]["is_corporate"] is False
        assert (
            response.json()["data"]["ac_number"]
            == self.service.billing_account.ac_number
        )

    def test_service_inactive_billing_account(self):
        self.service.billing_account.status = BillingStatusEnum.INACTIVE.value
        self.service.billing_account.save()
        url = reverse(
            "services:billing_details", kwargs={"gsn": self.service.gsn}
        )
        response = self.client.get(url)
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["message"] == "Billing Account Info"
        assert (
            response.json()["data"]["billing_account_id"]
            == self.service.billing_account.id
        )
        assert response.json()["data"]["is_corporate"] is False
        assert (
            response.json()["data"]["ac_number"]
            == self.service.billing_account.ac_number
        )
