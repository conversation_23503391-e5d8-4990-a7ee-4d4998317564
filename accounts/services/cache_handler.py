import json
from django.conf import settings
from django.core.serializers.json import DjangoJSONEncoder

from accounts.utils.cache_handler import BaseCacheHandler
from accounts.billing_accounts.models import BillingAccounts


class PendingUsageCacheHandler(BaseCacheHandler):
    def __init__(self, billing_account: "BillingAccounts"):
        if billing_account.parent:
            key = self._generate_key(billing_account.parent.id)
        else:
            key = self._generate_key(billing_account.id)
        super().__init__(key)

    def _generate_key(self, billing_account_id: str):
        return f"pending_usage:{billing_account_id}"

    def save(self, data: dict, timeout=None):
        if not timeout:
            timeout = settings.PENDING_USAGE_CACHE_EXPIRY_SECONDS

        data = json.dumps(data, cls=DjangoJSONEncoder)
        return super().save(data, timeout)

    def get(self):
        data = super().get()
        if data:
            return json.loads(data)
        return None
