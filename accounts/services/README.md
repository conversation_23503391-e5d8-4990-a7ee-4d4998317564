### Model Managers

Query to fetch live services
```
Services.live.all()
SELECT * FROM services WHERE live_status IN (1, 2);
```

Query to fetch active services
```
Services.objects.active().all()
SELECT * FROM services WHERE status=1;
```

Query to fetch suspended services
```
Services.objects.suspended().all()
SELECT * FROM services WHERE status=2;
```

Query to fetch inactive services
```
Services.objects.inactive().all()
SELECT * FROM services WHERE status=0;
```

Query to fetch services of a product
```
Services.objects.product('58ee66511dba5458').all()
SELECT * FROM services WHERE product_id='58ee66511dba5458';
```

Query to fetch services of a billing account
```
Services.objects.billing_account('63f35a5dee78c571').all()
SELECT * FROM services WHERE billing_account_id='63f35a5dee78c571';
```

Query to fetch service rentals of a service
```
ServiceRentals.objects.service('abc').all()
select * from service_rentals where service_id='abc'
```

Query to fetch active service rentals of a service
```
ServiceRentals.objects.service('abc').active()
select * from service_rentals where service_id='abc' and status=1 LIMIT 1
```

Query to fetch service packages of a service
```
ServicePackages.objects.service('abc').all()
select * from service_packages where service_id='abc';
```

Query to fetch active service packages of a service
```
ServicePackages.objects.service('abc').active('2023-02-21 10:00:00')
select * from service_packages where service_id='abc' and '2023-02-21 10:00:00' BETWEEN start_time and end_time LIMIT 1;
```