import logging
from django.conf import settings
from accounts.services.models import Services
from accounts.packages.models import PackageFeatures
from accounts.utils.api_services.account_v1 import AccountApiV1
from django.db.models import Q, Model
from django.db.models.query import QuerySet
from typing import List, Optional
from accounts.services import constants
from accounts.services.exceptions import InvalidServiceException
from accounts.services.utils.service_package import get_current_service_package
from accounts.packages.enums import PackageFeatureStatusEnum
from accounts.packages.utils.package_features import (
    process_feature_reactivation,
)

logger = logging.getLogger(__name__)


def sync_memcache(service_id: str) -> None:
    service = Services.objects.get(id=service_id)
    # to be noted: future task to remove below api call and use a internal method for syncing
    mem_response = AccountApiV1().update_memcache(service.gsn)
    if not mem_response:
        logger.title("Sync Memcache Failed").error(
            f"Service Id: {service_id}, Response: {mem_response}"
        )
    return mem_response


def get_service_details_f_ban(billing_account_id: str) -> Optional[Services]:
    return Services.objects.filter(
        billing_account_id=billing_account_id
    ).first()


def get_service_by_gsn(gsn: str) -> Optional[Services]:
    service = Services.objects.filter(gsn=gsn)
    return service.first()


def get_product_short_code_from_gsn(gsn: str) -> Optional[str]:
    service = get_service_by_gsn(gsn)
    if service:
        return service.product.short_code
    return None


def get_services_for_credit_limit(
    billing_account_ids: List[str],
) -> QuerySet:
    live_statuses = [
        constants.SERVICE_LIVE_STATUS_PREPAID,
        constants.SERVICE_LIVE_STATUS_POSTPAID,
        constants.SERVICE_LIVE_STATUS_DEMO,
    ]
    billing_account_ids = [str(id) for id in billing_account_ids]

    return (
        Services.objects.filter(
            status=constants.SERVICE_STATUS_ACTIVE,
            live_status__in=live_statuses,
            billing_account_id__in=billing_account_ids,
            servicenumbers__status=constants.SERVICE_NUMBER_STATUS_ACTIVE,
            product__id=settings.HEYO_PRODUCT_ID,
        )
        .filter(
            Q(servicenumbers__service_number__isnull=False)
            & ~Q(servicenumbers__service_number="NA")
        )
        .select_related("product__country")
    )


def update_current_usages_service(service_id: str, current_usages: str) -> bool:
    obj: Model = Services.objects.get(id=service_id)
    obj.current_usages = current_usages
    obj.save()
    return True


def reactivate_feature(gsn):
    service = Services.objects.filter(
        gsn=gsn,
        status=constants.SERVICE_STATUS_ACTIVE,
        live_status__in=[
            constants.SERVICE_LIVE_STATUS_PREPAID,
            constants.SERVICE_LIVE_STATUS_POSTPAID,
        ],
    ).first()

    if not service:
        raise InvalidServiceException("Service not found")

    service_package = get_current_service_package(service.id)

    if not service_package:
        raise InvalidServiceException("Service package not found")

    features = PackageFeatures.objects.filter(
        status=PackageFeatureStatusEnum.SUSPENDED.value,
        product_feature__is_paid=True,
    )

    if features.count() == 0:
        raise InvalidServiceException("No suspended features found")

    for feature in features.all():
        process_feature_reactivation(feature, service)
