import logging
from accounts.services import constants
from accounts.services.models import ServiceRentals

logger = logging.getLogger(__name__)


def get_service_pending_rental(service_id):
    query = ServiceRentals.objects.filter(
        service_id=service_id, status=constants.SERVICE_RENTAL_STATUS_ACTIVE
    ).first()

    if query is not None:
        return query.pending_rental
    else:
        return 0


def is_pending_rental(service_id):
    rental = get_service_pending_rental(service_id)
    if rental > 0:
        return True
    return False
