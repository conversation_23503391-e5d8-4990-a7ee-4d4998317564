import logging
import typing as t
from accounts.utils.api_services.chat import Chat<PERSON><PERSON>
from django.db import transaction
from accounts.billing_accounts.models import BillingAccountCredits
from accounts.services.models import ResourceCharges
from decimal import Decimal, ROUND_HALF_UP
from accounts.services.exceptions import ResourceChargeRefundException

logger = logging.getLogger(__name__)


def process_refund_for_resource(
    resource: "ResourceCharges",
) -> t.Union[Decimal, None]:
    logger.info(f"input received #process_refund_for_resource, {resource}")
    resource_id = resource.resource_id
    campaign_data = ChatApi().get_campaign_detail(resource_id)

    if campaign_data["status"] not in ("completed", "failed"):
        logger.error(
            f"skipping, as {resource_id} not in completed/failed state"
        )
        return None

    if campaign_data["status"] == "failed":
        actual_amount = 0
    else:
        actual_amount = campaign_data["billing"][
            "actual_cost"
        ]  # actual amount to refund

    actual_amount = Decimal(actual_amount).quantize(
        Decimal("0.000"), rounding=ROUND_HALF_UP
    )

    if actual_amount > resource.estimated_amount:
        raise ResourceChargeRefundException(
            "Refund can't be processed. actual_cost is greater than estimated amount"
        )

    refund_amount: Decimal = resource.estimated_amount - actual_amount
    with transaction.atomic():
        # add refund amount to credit bucket
        BillingAccountCredits.add_credit_amount(
            billing_account=resource.service.billing_account,
            amount=refund_amount,
            description=f"{refund_amount} refund credited for: {resource.resource}, {resource_id}",
        )
        # Update resource charges table
        resource.update_actual_amount(actual_amount)
        resource.mark_as_refunded()

    logger.info(f"Refund processed completed for resource_id: {resource_id}")
    return refund_amount
