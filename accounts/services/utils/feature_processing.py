import logging
from django.conf import settings
from accounts.services.exceptions import (
    InvalidServiceException,
    InvalidFeaturePaymentException,
)
from accounts.services.enums import FeatureActivationRequestStatusEnums
from accounts.services.models import FeatureActivationRequest
from django.db import transaction
from accounts.payments.models import TrackingSettlementHistories
from accounts.utils.api_services.account_v1 import AccountApiV1
from accounts.products.models import ProductDefaultRateSlabs
from accounts.services.exceptions import FeatureProcessingFailedException
from accounts.billing_accounts.models import BillingAccountCredits
from accounts.services.utils.service import get_service_details_f_ban
from accounts.services.utils.service_package import get_current_service_package
from accounts.packages.utils.package_features import create_package_feature
from accounts.packages.models import PackageFeatureHistory, PackageFeatureRates
from accounts.packages.enums import PackageFeatureEventTypeEnums
from accounts.billing_accounts.utils.billing_account import (
    calculate_pro_rata_refund_for_plan,
    calculate_next_billing_date,
)
from accounts.utils.api_services.exceptions import TruecallerApiException
from accounts.services.events import FeatureActivationEvent

logger = logging.getLogger(__name__)


def get_pending_feature_payment(payment_id: str):
    return (
        FeatureActivationRequest.objects.select_related("service")
        .select_related("product_feature")
        .filter(
            payment_id=payment_id,
            status=FeatureActivationRequestStatusEnums.PENDING.value,
        )
    ).first()


def validate_payment_settlement(payment_id: str):
    payment_settlement = TrackingSettlementHistories.objects.filter(
        payment_id=payment_id
    ).first()
    if not payment_settlement:
        raise FeatureProcessingFailedException("Invalid Payment or not settled")
    return (
        payment_settlement.billing_account.parent_id
        or payment_settlement.billing_account_id
    )


def get_feature_data_and_description(product_feature):
    queryset = ProductDefaultRateSlabs.objects.filter(
        product_feature_id=product_feature.id
    )
    logger.title("Query #get_feature_data_and_description").info(queryset.query)
    feature = queryset.first()
    feature_cost = feature.rate if feature else 0
    description = (
        f"{feature_cost} debited for {product_feature.name} activation"
    )
    return feature_cost, description, feature.min, feature.max


def deduct_credits(billing_account_id, feature_cost, description):
    service = get_service_details_f_ban(billing_account_id)
    if not service:
        raise InvalidServiceException("Service not found")
    BillingAccountCredits.deduct_credit_amount(
        service.billing_account, feature_cost, description
    )
    return service


def activate_package_feature(
    service, product_feature, feature_cost, feature_min, feature_max
):
    current_package = get_current_service_package(service.id)
    created_package_feature = create_package_feature(
        {
            "package_id": current_package.package.id,
            "product_feature_id": product_feature.id,
            "rent_per_month": feature_cost,
            "last_disabled_date": None,
        }
    )
    PackageFeatureRates.objects.create(
        package_feature=created_package_feature,
        min=feature_min,
        max=feature_max,
        rate=feature_cost,
    )
    # Log entry
    PackageFeatureHistory.objects.create(
        package_feature=created_package_feature,
        event_type=PackageFeatureEventTypeEnums.ACTIVATED.value,
    )

    return created_package_feature


def process_refund(
    service,
    feature_cost,
    feature_name,
    billing_account_id,
    next_billing_start_date,
):
    refund_amount = calculate_pro_rata_refund_for_plan(
        feature_cost, next_billing_start_date, service.timezone
    )
    if refund_amount > 0:
        description = (
            f"prorata refund {refund_amount} for feature {feature_name} "
        )
        result = AccountApiV1().add_credit(
            billing_account_id,
            refund_amount,
            description,
            settings.INTERNAL_API_USER_ID,
        )
        if not result:
            raise FeatureProcessingFailedException("Credit addition API failed")


def process_pending_feature_payment(payment_id: str) -> None:
    try:
        with transaction.atomic():
            # Step 1: check if there are any pending feature payment with `payment_id`,if yes then process further, if no then return
            feature_payment = get_pending_feature_payment(payment_id)
            if feature_payment is None:
                raise InvalidFeaturePaymentException()

            product_feature = feature_payment.product_feature

            # Step 2: Validate payment settlement
            billing_account_id = validate_payment_settlement(payment_id)

            # Step 3: Fetch feature cost and description
            (
                feature_cost,
                description,
                feature_min,
                feature_max,
            ) = get_feature_data_and_description(product_feature)

            # Step 4: Deduct credits
            service = deduct_credits(
                billing_account_id, feature_cost, description
            )

            # Step 5: Handle package feature activation
            activate_package_feature(
                service, product_feature, feature_cost, feature_min, feature_max
            )

            # Step 6: Update feature activation request
            feature_payment.status = (
                FeatureActivationRequestStatusEnums.SUCCESS.value
            )
            feature_payment.save()

        # Step 7: sns activation event
        FeatureActivationEvent().company_id(service.gsn).add_feature_with_data(
            product_feature.memcache_key, {"request_id": feature_payment.ref_id}
        ).send()

        # fetch next billing start date for refund process
        next_billing_start_date = calculate_next_billing_date(
            service.billing_account.billing_day,
            service.activation_date,
            service.timezone,
        )

        # Step 8: Calculate pro rata and process refund
        process_refund(
            service,
            feature_cost,
            product_feature.name,
            billing_account_id,
            next_billing_start_date,
        )
        logger.info("Feature processed successfully")

    except FeatureProcessingFailedException:
        FeatureActivationRequest.objects.filter(payment_id=payment_id).update(
            status=FeatureActivationRequestStatusEnums.FAILED.value
        )
        raise
    except TruecallerApiException as e:
        logger.error(f"Truecaller activation failed: {str(e)}")
        raise
