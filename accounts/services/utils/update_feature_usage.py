from datetime import datetime

from accounts.services.models import (
    Services,
    FeatureUsagesLogs,
)
from accounts.packages.models import PackageFeatures
from accounts.packages.utils.package_features import calculate_leg_a_use


class FeatureUsageUpdater:
    def __init__(
        self, service: Services, start_time: datetime, end_time: datetime
    ):
        self._service: Services = service
        self._start_time: datetime = start_time
        self._end_time: datetime = end_time

    def update(self, package_feature_id: str, use: int):
        package_feature = PackageFeatures.objects.select_related(
            "product_feature"
        ).get(id=package_feature_id)
        feature_usage_log = (
            FeatureUsagesLogs.entries.find_max_leg_a_usage_instance(
                package_feature=package_feature,
                service=self._service,
                start_date=self._start_time,
            )
        )
        # default 0 usage, when feature_usage_log does not exist
        current_leg_a_use = 0
        if feature_usage_log:
            current_leg_a_use = feature_usage_log.leg_a_use

        # calculate leg_a_use, extend the code in future to support leg_b_use and leg_b_rate
        leg_a_use = calculate_leg_a_use(package_feature, use, current_leg_a_use)
        feature_usage_log = self._update_usage_log(package_feature, leg_a_use)
        return feature_usage_log

    def _update_usage_log(
        self, package_feature: PackageFeatures, leg_a_use: int
    ):
        """
        Used update_or_create to prevent duplication in case of multiple consumers/requests
        """
        feature_usage_log, _ = FeatureUsagesLogs.objects.update_or_create(
            service=self._service,
            package_feature=package_feature,
            start_date=self._start_time,
            defaults={
                "leg_a_use": leg_a_use,
                "end_date": self._end_time,
            },
        )
        return feature_usage_log
