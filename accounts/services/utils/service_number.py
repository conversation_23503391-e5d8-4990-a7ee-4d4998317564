import logging
from django.db.models import Value
from django.db.models.functions import Concat, Replace
from django.utils import timezone
from accounts.services import constants
from accounts.services.models import ServiceNumbers
from accounts.products.utils import (
    get_product_setting,
    get_product_default_setting,
)

logger = logging.getLogger(__name__)


def get_old_service_number_details(service_id):
    queryset = ServiceNumbers.objects.filter(
        service_id=service_id,
        status=constants.SERVICE_NUMBER_STATUS_INACTIVE,
    ).order_by("-created")
    return queryset.first()


def free_old_service_numbers(service_id):
    return ServiceNumbers.objects.filter(service_id=service_id).update(
        service_number=Concat(
            Replace("service_number", Value("_free"), Value("")), Value("_free")
        ),
        status=constants.SERVICE_NUMBER_STATUS_INACTIVE,
    )


def add_new_service_number(service_id, service_number, **kwargs):
    number_cost = kwargs["number_cost"]
    swapped_date = kwargs.get("swapped_date")
    if swapped_date is not None:
        swapped_date = swapped_date
    else:
        swapped_date = timezone.now()

    return ServiceNumbers.objects.create(
        service_id=service_id,
        service_number=service_number,
        number_type=kwargs["number_type"],
        number_cost=number_cost,
        is_paid=0 if int(number_cost) > 0 else 1,
        is_live=kwargs["is_live"],
        is_archived=0,
        swapped_date=swapped_date,
    )


def get_service_number_details(service_id):
    return ServiceNumbers.objects.filter(
        service_id=service_id, status=constants.SERVICE_NUMBER_STATUS_ACTIVE
    ).first()


def calculate_archive_date(product_id: str, live_status: int, package_category_code: str) -> timezone:
    """
    Calculates the number of days for which a service number should be archived.
    
    Args:
        product_id (str): The ID of the product.
        live_status (int): The live status of the service.
        package_category_code (str): The category code of the package.
    
    Returns:
        timezone: The calculated archive date.
    """
    archive_days = 0
    key = (
        f"SERVICE_NUMBER_ARCHIVE_DAYS_{live_status}_{package_category_code}"
    )
    product_setting = get_product_setting(product_id, key)
    if not product_setting:
        product_setting = get_product_default_setting(key)
    
    if product_setting:
        archive_days = int(product_setting.setting_value)
    
    if archive_days < constants.DEFAULT_ARCHIVE_DAYS:
        archive_days = constants.DEFAULT_ARCHIVE_DAYS
    
    return timezone.now() + timezone.timedelta(days=archive_days)
