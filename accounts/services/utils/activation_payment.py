import logging
import typing as t
from decimal import Decimal

from accounts.billing_accounts.models import BillingAccountCredits
from accounts.billing_accounts.utils.service_contact import (
    get_billing_contact_person,
)
from accounts.core.utils.tax_calculation import calculate_tax
from accounts.discounts.enums import DiscountApplyOnEnum
from accounts.global_packages.enums import StatusEnum as PackageStatusEnum
from accounts.global_packages.models import GlobalPackages
from accounts.packages.exceptions import InvalidPackageException
from accounts.packages.models import Packages
from accounts.payments.enums import PaymentActionTypeEnum
from accounts.payments.exceptions import PaymentLinkGenerationException
from accounts.payments.models import PaymentActions
from accounts.services.enums import NumberSystemNumberStatusEnum
from accounts.services.exceptions import (
    ServiceAlreadyRunningException,
    ServiceNotDemoException,
)
from accounts.services.models import ServiceNumbers, Services
from accounts.utils.api_services.number_system import NumberSystem
from accounts.utils.api_services.payment_service import PaymentService

if t.TYPE_CHECKING:
    from accounts.discounts.models import Discounts

logger = logging.getLogger(__name__)


class ActivationPaymentService:
    """
    A service class for handling activation payment operations.
    """

    def __init__(
        self,
        service: Services,
        package_id: str,
        service_number: str = None,
    ):
        self.service = service
        self.package_id = package_id
        self.service_number = service_number
        self._number_details = {}

    def generate_payment_link(
        self,
        redirect_url: str = None,
        custom_amount: Decimal = None,
        payment_method: str = None,
        name: str = None,
        email: str = None,
        phone: str = None,
    ) -> t.Dict[str, t.Any]:
        if not self.service.is_demo():
            raise ServiceNotDemoException("Service is already activated")

        # Check if service number is assigned to some other service
        if (
            self.service_number
            and ServiceNumbers.entries.is_allocated_to_other_service(
                self.service.id, self.service_number
            )
        ):
            raise ServiceAlreadyRunningException()

        if self.service_number:
            number_details = self.get_number_details()
            if not NumberSystemNumberStatusEnum.allowed_for_making_live(
                number_details["number_status"]
            ):
                raise ServiceAlreadyRunningException()

        # fetch contact details for the payment
        if not name or not email or not phone:
            contact_info = get_billing_contact_person(
                self.service.billing_account.id
            )
            name = name or contact_info.get("name")
            email = email or contact_info.get("email")
            phone = phone or contact_info.get("mobile")

        if custom_amount is None:
            amount: Decimal = self.fetch_payable_amount()["payable_amount"]
        else:
            amount = custom_amount
        if amount <= 0:
            raise PaymentLinkGenerationException(
                "Payable amount is zero or negative, cannot generate payment link"
            )

        # create payment data for generating payment URL
        payment_data = PaymentService().generate_payment_url(
            billing_account_id=self.service.billing_account_id,
            ac_number=self.service.billing_account.ac_number,
            amount=amount,
            country_id=self.service.product.country_id,
            state_id=self.service.billing_account.state_id,
            name=name,
            email=email,
            phone=phone,
            request_of="activation",
            response_url=redirect_url,
            payment_method=payment_method,
        )
        PaymentActions.objects.create(
            payment_id=payment_data["payment_id"],
            action_type=PaymentActionTypeEnum.SERVICE_ACTIVATION.value,
            action_data={
                "service_id": self.service.id,
                "package_id": self.package_id,
                "service_number": self.service_number,
            },
        )
        return payment_data["payment_id"], payment_data["url"]

    def fetch_payable_amount(self) -> t.Dict[str, t.Any]:
        rental_amount = 0
        additional_cost = 0
        number_cost = 0
        discount_amount = 0
        advance_amount = 0
        amount_without_tax = 0
        tax_total = 0
        payable_amount = 0
        tax_details = []

        if self.service_number:
            number_details = self.get_number_details()
            number_cost = number_details["number_cost"]

        package = self.get_package_details()
        rental_amount = Decimal(package.rent_per_month) * package.renew_cycle
        if isinstance(package, Packages):
            additional_cost = Decimal(package.package_custom.additional_cost)

        credit_amount = BillingAccountCredits.entries.billing_account(
            self.service.billing_account
        ).credit_amount()
        advance_amount = BillingAccountCredits.calc_advance_amount(
            credit_amount
        )
        total_amount, discount_amount = self.apply_package_discount(
            package.discount, rental_amount, additional_cost, number_cost
        )
        amount_without_tax = total_amount - advance_amount

        country_short_code = self.service.product.country.short_code

        if amount_without_tax > 0:
            tax_data = calculate_tax(
                amount_without_tax,
                country_short_code,
                self.service.billing_account.state_id,
            )
            tax_total = tax_data["tax_total"]
            tax_details = list(tax_data["detail"].values())
            payable_amount = amount_without_tax + tax_total

        return {
            "rental_amount": Decimal(rental_amount),
            "number_cost": Decimal(number_cost),
            "additional_cost": Decimal(additional_cost),
            "discount_amount": Decimal(discount_amount),
            "advance_amount": Decimal(advance_amount),
            "amount_without_tax": Decimal(amount_without_tax),
            "tax_details": tax_details,
            "tax_total": Decimal(tax_total),
            "payable_amount": Decimal(payable_amount),
            "currency": self.service.product.currency,
        }

    def get_package_details(self) -> t.Union[GlobalPackages, Packages]:
        package = GlobalPackages.objects.filter(
            id=self.package_id, status=PackageStatusEnum.ACTIVE.value
        ).first()
        if not package:
            package = (
                Packages.entries.filter(id=self.package_id)
                .active()
                .must_be_custom_package()
                .first()
            )

        if not package:
            raise InvalidPackageException(
                "Invalid Package ID or Package is not active"
            )
        return package

    def apply_package_discount(
        self,
        discount: "Discounts",
        rental_amount: Decimal,
        additional_cost: Decimal,
        number_cost: Decimal,
    ) -> t.Tuple[Decimal, Decimal]:
        total_amount = rental_amount + additional_cost + number_cost
        discount_amount = Decimal(0)

        if discount and not discount.is_expired():
            if discount.apply_on == DiscountApplyOnEnum.RENTAL.value:
                rental_discount = discount.calculate_discount_amount(
                    rental_amount
                )
                if rental_amount >= rental_discount:
                    discount_amount = rental_discount
                else:
                    discount_amount = rental_amount
            elif discount.apply_on == DiscountApplyOnEnum.ALL.value:
                all_discount = discount.calculate_discount_amount(total_amount)
                if total_amount >= all_discount:
                    discount_amount = all_discount
                else:
                    discount_amount = total_amount

        final_amount = total_amount - discount_amount
        return Decimal(final_amount), Decimal(discount_amount)

    def get_number_details(self):
        if not self._number_details:
            number_details = NumberSystem().get_number_details(
                self.service.gsn, self.service_number
            )
            self._number_details = number_details
        return self._number_details
