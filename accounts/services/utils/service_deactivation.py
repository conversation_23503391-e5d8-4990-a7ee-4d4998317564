import logging

from django.conf import settings
from django.db import transaction
from accounts.services.utils.activity_note import (
    add_activity_notes,
)
from accounts.services.utils.service import sync_memcache
from accounts.services.utils.service_package import get_current_service_package

from accounts.utils.api_services.number_system import NumberSystem
from accounts.utils.api_services.myoperator import MyOperator
from accounts.services.utils.service_number import (
    get_service_number_details,
    calculate_archive_date,
)
from accounts.products.utils import get_country_code
from accounts.services.models import Services
from accounts.services.constants import (
    SERVICE_STATUS_INACTIVE,
    SERVICE_LIVE_STATUS_DEMO,
)
from accounts.services.exceptions import (
    InvalidServiceException,
    ServiceAlreadyDeactivated
)
from accounts.services.events import ServiceDeactivationEvent

logger = logging.getLogger(__name__)


def deactivate_service(service_id: str, user_profile_id: str):
    """
    Deactivates a service.

    :param service_id: The ID of the service to be deactivated. (str)
    :param user_profile_id: The ID of the user profile. (str)
    :raises InvalidServiceException: If the service is not found.
    :raises ServiceAlreadyDeactivated: If the service is already deactivated.
    """
    logger.title("input #deactivate_service").info(locals())
    service = Services.objects.filter(id=service_id).first()
    if not service:
        raise InvalidServiceException("Service not found")
    
    if service.status == SERVICE_STATUS_INACTIVE:
        raise ServiceAlreadyDeactivated("Service already deactivated")
    
    service_package = get_current_service_package(service.id)
    package_category_code = service_package.package.package_category.code
    country_short_code = get_country_code(service.product_id)

    with transaction.atomic():
        # deactivate service
        service.deactivate()
        # sync memcache
        sync_memcache(service.id)
    
    service_number = get_service_number_details(service.id)
    if not service_number:
        logger.error("service number not found")
    else:
        # deactivate service number
        NumberSystem().deactivate_service_number(service_number.service_number)
        archive_date = calculate_archive_date(
            service.product.id, service.live_status, package_category_code
        )
        service_number.set_archive_date(archive_date)

    # add activity note
    add_activity_notes(
        service.billing_account.id,
        service.id,
        settings.DEACTIVATE_NOTE,
        user_profile_id,
    )

    # notify myoperator for deactivation
    MyOperator().deactivate_company(service.gsn, country_short_code)
    
    # trigger deactivation event
    ServiceDeactivationEvent().company_id(service.gsn).send()


def deactivate_demo_service(service_id: str, user_profile_id: str) -> None:
    """
    Deactivates a demo service.
    Args:
        service_id (str): The ID of the service to deactivate.
        user_profile_id (str): The ID of the user's profile.
    Returns:
        None
    """
    logger.title("input #deactivate_demo_service").info(locals())

    service = Services.objects.filter(
        id=service_id, live_status=SERVICE_LIVE_STATUS_DEMO
    ).first()
    if not service:
        raise InvalidServiceException("Invalid Service")

    # deactivate service
    deactivate_service(service_id, user_profile_id)
    
    # expire service package
    service_package = get_current_service_package(service.id)
    if service_package:
        service_package.expire()
