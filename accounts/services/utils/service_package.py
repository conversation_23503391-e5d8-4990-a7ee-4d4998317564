from django.utils import timezone
from accounts.services.models import ServicePackages
from django.db import connection
from django.db.models import Case, CharField, Value, When, Q
import logging

logger = logging.getLogger(__name__)


def get_current_service_package(service_id):

    current_date = timezone.now().strftime("%Y-%m-%d %H:%M:%S")

    raw_query = """
        SELECT id, package_id, start_time
        FROM (
            SELECT A.*, 1 priority
            FROM service_packages A
            WHERE service_id = %s AND start_time <= %s AND end_time >= %s
            UNION ALL
            SELECT B.*, 2 priority
            FROM service_packages B
            WHERE service_id = %s
        ) A
        ORDER BY priority, id DESC
        LIMIT 1
    """

    with connection.cursor() as cursor:
        cursor.execute(
            raw_query, [service_id, current_date, current_date, service_id]
        )
        row = cursor.fetchone()

    if row is not None:
        sp_id = row[0]
        return ServicePackages.objects.get(id=sp_id)
    return False


def get_current_and_upcoming_service_packages(gsn):
    current_time = timezone.now()
    queryset = (
        ServicePackages.objects.filter(service__gsn=gsn)
        .annotate(
            package_status=Case(
                When(
                    start_time__lte=current_time,
                    end_time__gte=current_time,
                    then=Value("current"),
                ),
                When(start_time__gt=current_time, then=Value("upcoming")),
                default=Value("past"),
                output_field=CharField(),
            )
        )
        .filter(Q(package_status="current") | Q(package_status="upcoming"))
        .order_by("start_time")
    )
    logger.title("Query #get_current_and_upcoming_service_packages").info(
        queryset.query
    )
    return queryset


def get_service_packages(gsn):
    queryset = ServicePackages.objects.filter(service__gsn=gsn).order_by(
        "start_time"
    )
    logger.title("Query #get_service_packages").info(queryset.query)
    return queryset
