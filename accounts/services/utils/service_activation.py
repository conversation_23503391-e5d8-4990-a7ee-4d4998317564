import logging
import typing as t
from datetime import timed<PERSON><PERSON>
from decimal import Decimal

from django.db import transaction  # todo: use this for transaction
from django.db.models import F, Value
from django.db.models.functions import Concat, Replace
from django.utils import timezone

from accounts.billing_accounts.enums import BillingStatusEnum
from accounts.billing_accounts.exceptions import (
    FraudBillingAccountException,
    InvalidBillingAccountException,
)
from accounts.billing_accounts.models import BillingAccounts
from accounts.global_packages.enums import StatusEnum as PackageStatusEnum
from accounts.global_packages.models import GlobalPackages
from accounts.packages.exceptions import InvalidPackageException
from accounts.packages.models import Packages
from accounts.packages.utils.create_package import (
    create_package_from_global_package,
)
from accounts.payments.enums import PaymentActionTypeEnum
from accounts.payments.exceptions import InvalidPaymentException
from accounts.payments.models import (
    PaymentActions,
    PaymentTracks,
    TrackingSettlementHistories,
)
from accounts.services.enums import (
    NumberSystemNumberStatusEnum,
    ServiceLiveStatusEnum,
    ServiceNumberStatusEnum,
    ServiceStatusEnum,
)
from accounts.services.events import ServiceActivationEvent
from accounts.services.exceptions import (
    InvalidServiceException,
    ServiceActivationFailedException,
)
from accounts.services.models import (
    ServiceNumbers,
    ServicePackages,
    ServiceRentals,
    Services,
)
from accounts.utils.api_services.account_v1 import AccountApiV1
from accounts.utils.api_services.myoperator import MyOperator
from accounts.utils.api_services.number_system import NumberSystem
from accounts.utils.common import add_months

logger = logging.getLogger(__name__)


class ServiceActivation:
    def __init__(self, service: Services, payment_track: PaymentTracks):
        self.service = service
        self.payment_track = payment_track

    def initiate_activation(
        self, package_id: str, service_number: t.Optional[str] = None
    ):
        if not self.payment_track.is_txn_settled():
            raise InvalidPaymentException("Payment is not settled")

        if not TrackingSettlementHistories.objects.filter(
            billing_account=self.service.billing_account,
            payment=self.payment_track,
        ).exists():
            raise InvalidPaymentException("Invalid payment ID or not settled")

        if self.service.billing_account.is_fraud():
            raise FraudBillingAccountException("Billing Account is fraud")

        if not self.service.is_demo():
            raise InvalidServiceException("Not a demo service")

        old_service_number = ServiceNumbers.entries.last_allocated_number(
            self.service
        )
        if not old_service_number:
            raise ServiceActivationFailedException(
                "No previous service number available"
            )
        package: t.Optional[GlobalPackages | Packages] = self._package_details(
            package_id
        )
        should_fetch_from_number_system = (
            not service_number or old_service_number.is_archived
        )
        if should_fetch_from_number_system:
            number_cost = 0  # fetch only free numbers
            demo_number_details = NumberSystem().fetch_demo_number(
                self.service.product.country.short_code,
                package.package_for,
                old_service_number.number_type,
                number_cost,
            )
            service_number = demo_number_details["display_number"]
            number_type = demo_number_details["number_type"]
        else:
            number_details = NumberSystem().get_number_details(
                self.service.gsn, service_number
            )
            if not NumberSystemNumberStatusEnum.allowed_for_making_live(
                number_details["number_status"]
            ):
                raise ServiceActivationFailedException(
                    "Service number is not in open state"
                )
            number_type = number_details["number_type"]
            number_cost = number_details["number_cost"]

        if ServiceNumbers.entries.is_allocated_to_other_service(
            self.service.id, service_number
        ):
            raise ServiceActivationFailedException(
                "Service number is already allocated to another service"
            )

        if isinstance(package, GlobalPackages):
            package: Packages = create_package_from_global_package(package.id)
            package.status = PackageStatusEnum.INACTIVE.value
            package.save()
        elif isinstance(package, Packages):
            package.status = PackageStatusEnum.INACTIVE.value
            package.save()

        self._update_billing_account()
        self._update_service(package.rent_per_month, package.renew_cycle)
        self._update_service_rental(package.rent_per_month, package.renew_cycle)
        self._update_service_package(package)
        self._make_service_number_live(
            service_number, old_service_number, number_type
        )
        self._add_service_number(service_number, number_type, number_cost)
        # todo: settle the credit bucket

    def publish_event(self):
        ServiceActivationEvent().company_id(self.service.gsn).send()
        return self

    def sync_memcache(self):
        # TODO: Implement memcache sync
        return self

    def _make_service_number_live(
        self,
        new_service_number: str,
        old_service_number: ServiceNumbers,
        number_type: str,
    ):
        country_short_code = self.service.product.country.short_code
        number_details = NumberSystem().make_number_live(
            service_number=new_service_number,
            gsn=self.service.gsn,
            service_number_old=old_service_number.real_service_number(),
            live_status=self.service.live_status,
            service_id=self.service.id,
            activation_date=self.service.activation_date.strftime(
                "%Y-%m-%d %H:%M:%S"
            ),
        )
        # Update myoperator service number details
        MyOperator().update_service_number(
            gsn=self.service.gsn,
            service_number=new_service_number,
            country_code=country_short_code,
            live_status=self.service.live_status,
            number_property=number_type,
            expiry=self.service.expiry_date.strftime("%Y-%m-%d %H:%M:%S"),
            dnid1=number_details["dnid1"],
            dnid2=number_details["dnid2"],
            status=ServiceStatusEnum.ACTIVE.value,
        )
        MyOperator().update_permissions(
            data={"gsn": self.service.gsn},
            country_short_code=country_short_code,
        )

    def _update_service_package(self, package: Packages):
        current_service_package: ServicePackages = (
            ServicePackages.objects.filter(service=self.service).first()
        )
        current_service_package.package = package
        current_service_package.start_time = self.service.activation_date
        current_service_package.end_time = self.service.expiry_date
        current_service_package.save()

    def _update_service(self, rent_per_month: Decimal, renew_cycle: int):
        self.service.renew_cycle = renew_cycle
        self.service.rent_per_month = rent_per_month
        self.service.activation_date = timezone.now()
        self.service.expiry_date = add_months(
            self.service.activation_date,
            renew_cycle,
            self.service.billing_account.billing_day,
        )
        self.service.live_status = ServiceLiveStatusEnum.PREPAID.value
        self.service.status = ServiceStatusEnum.ACTIVE.value
        self.service.save()

    def _update_service_rental(self, rent_per_month: Decimal, renew_cycle: int):
        pending_rental_amount = Decimal(rent_per_month) * renew_cycle
        service_rental: ServiceRentals = ServiceRentals.get_rental(self.service)
        if service_rental and service_rental.rental_amount == 0:
            service_rental.pending_rental = pending_rental_amount
            service_rental.save()
        else:
            service_rental = ServiceRentals.add_rental(
                service=self.service,
                rental_amount=Decimal(0),
                pending_rental=pending_rental_amount,
            )

    def _add_service_number(
        self, service_number: str, number_type: str, number_cost: Decimal
    ):
        # Delete existing same service number from the service
        ServiceNumbers.objects.filter(service=self.service).filter(
            service_number__contains=service_number
        ).delete()

        # set _free to all other numbers
        ServiceNumbers.objects.filter(service=self.service).update(
            service_number=Concat(
                Replace(F("service_number"), Value("_free"), Value("")),
                Value("_free"),
            ),
            status=ServiceNumberStatusEnum.INACTIVE.value,
        )
        # create new service number
        service_number = ServiceNumbers.objects.create(
            service=self.service,
            service_number=service_number,
            number_type=str(number_type),
            number_cost=number_cost,
            is_paid=0 if number_cost > 0 else 1,
            is_live=1,
            status=ServiceNumberStatusEnum.ACTIVE.value,
        )

    def _update_billing_account(self):
        self.service.billing_account.billing_day = timezone.now().day
        self.service.billing_account.status = BillingStatusEnum.ACTIVE.value
        self.service.billing_account.save()

    def _package_details(
        self, package_id: str
    ) -> t.Union[GlobalPackages, Packages]:
        package = GlobalPackages.objects.filter(
            id=package_id, status=PackageStatusEnum.ACTIVE.value
        ).first()
        if not package:
            package = (
                Packages.entries.filter(id=package_id)
                .active()
                .must_be_custom_package()
                .first()
            )

        if not package:
            raise InvalidPackageException(
                "Invalid Package ID or Package is not active"
            )
        return package
