import logging

from django.conf import settings
from django.db import transaction
from accounts.services.utils.activity_note import (
    add_activity_notes,
)
from accounts.services.utils.service import sync_memcache
from accounts.products.utils import get_country_code
from accounts.services.models import Services
from accounts.services.enums import ServiceStatusEnum
from accounts.services.exceptions import (
    InvalidServiceException,
)
from accounts.services.events import ServiceSuspensionEvent

logger = logging.getLogger(__name__)


class ServiceSuspension:
    def __init__(self, service: Services):
        self._service = service
        self._billing_account = None

    def suspend(self) -> None:
        if self._service.status != ServiceStatusEnum.ACTIVE.value:
            raise InvalidServiceException(
                f"Service is already {ServiceStatusEnum.get_name(self._service.status).lower()}"
            )

        with transaction.atomic():
            self._service.suspend()
            sync_memcache(self._service.id)

        ServiceSuspensionEvent().company_id(self._service.gsn).ban(
            self.billing_account().ac_number
        ).send()

    def add_activity(self, user_profile_id: str) -> None:
        add_activity_notes(
            self.billing_account().id,
            self._service.id,
            settings.SUSPEND_NOTE,
            user_profile_id,
        )

    def billing_account(self):
        if self._billing_account:
            return self._billing_account

        self._billing_account, _ = self._service.billing_account.parent_ban()
        return self._billing_account
