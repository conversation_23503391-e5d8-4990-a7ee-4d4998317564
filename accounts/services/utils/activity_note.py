import logging
from django.db.models import Q
from accounts.services.models import ActivityNotes
from accounts.users.models import UserProfiles

logger = logging.getLogger(__name__)


def get_activity_note(service_id, note, **kwargs):
    query = ActivityNotes.objects.filter(
        Q(service_id=service_id)
        & Q(note=note)
        & Q(created__date=kwargs["activity_date"])
        & (
            Q(created_by__in=kwargs["created_by"])
            if isinstance(kwargs["created_by"], list)
            else Q(created_by=kwargs["created_by"])
        )
    )

    if query.exists():
        logger.info(f"activity notes  {query.values()}")
        return query.first()
    else:
        return False


def add_activity_notes(billing_account_id, service_id, note, created_by):
    activity = ActivityNotes.objects.create(
        billing_account_id=billing_account_id,
        service_id=service_id,
        note=note,
        created_by=UserProfiles.objects.get(id=created_by),
    )
    return activity
