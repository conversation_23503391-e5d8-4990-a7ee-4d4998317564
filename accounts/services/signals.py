import logging
from django.db import transaction
from django.dispatch import receiver
from django.db.models.signals import post_save

from accounts.billing_accounts.models import BillingAccountCredits
from accounts.services.cache_handler import PendingUsageCacheHandler

logger = logging.getLogger(__name__)


@receiver(post_save, sender=BillingAccountCredits)
def clear_pending_usage_cache(instance, created, **kwargs):
    if created:
        logger.info("Credit amount added, clearing pending usage cache")
        transaction.on_commit(
            lambda: PendingUsageCacheHandler(instance.billing_account).delete()
        )
