import sys
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from django.db.models import Q
from accounts.services.models import ServiceNumbers
from accounts.services import constants
from django.utils import timezone


class Command(BaseCommand):
    help = (
        "Set service number archive date to today, This Command should"
        " not be executed on prod server, It is created only for testing servers."
    )

    def handle(self, *args, **options):
        if settings.DATABASES["default"]["USER"] != "stageaccountusr":
            raise CommandError("Invalid Environment, not a stage user")

        self.stdout.write("Fetching Archivable Services...")
        archivable_service_numbers = self.fetch_archivable_service_numbers()
        archivable_service_numbers_cnt = archivable_service_numbers.count()
        self.stdout.write(
            f"Archivable Services: {archivable_service_numbers_cnt}"
        )
        if archivable_service_numbers_cnt == 0:
            self.stdout.write(
                self.style.SUCCESS("No archivable service numbers found")
            )
            sys.exit()

        for service_number in archivable_service_numbers:
            self.update_archive_date(service_number.id, timezone.now())
        self.stdout.write(
            self.style.SUCCESS("Service numbers archived successfully.")
        )

    def fetch_archivable_service_numbers(self):
        return ServiceNumbers.objects.filter(
            Q(is_archived=constants.SERVICE_NUMBER_ARCHIVED_NO)
            & Q(archive_date__gte=timezone.now())
            & Q(status=constants.SERVICE_NUMBER_STATUS_INACTIVE)
        )

    def update_archive_date(self, id, archive_date):
        ServiceNumbers.objects.filter(id=id).update(archive_date=archive_date)
