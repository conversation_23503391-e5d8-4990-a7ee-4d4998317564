import logging

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.conf import settings
from django.forms.models import model_to_dict

from accounts.services.models import Services
from accounts.packages.utils.package_features import (
    process_feature_deactivation,
)
from accounts.packages.models import PackageFeatures
from accounts.packages.enums import PackageFeatureStatusEnum


logger = logging.getLogger("cancelled_feature_deactivation")


class Command(BaseCommand):
    help = "Deactivate package features cancelled by customer "

    def handle(self, *args, **options):
        try:
            features_to_deactivate = PackageFeatures.objects.filter(
                last_disabled_date__lt=timezone.now(),
                status=PackageFeatureStatusEnum.ENABLED.value,
                is_customer_cancelled=True,
            ).order_by("last_disabled_date")

            for feature in features_to_deactivate.all():
                logger.title("Deactivate cancelled Feature").info(
                    model_to_dict(feature)
                )
                process_feature_deactivation(feature)
        except Exception as e:
            logger.title("cancelled Feature deactivation Failed").critical(
                e, exc_info=True
            )
