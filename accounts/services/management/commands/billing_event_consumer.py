import json
import time
import logging
import typing as t

from dataclasses import dataclass

from django.conf import settings
from django.utils import timezone
from django.db import transaction
from django.db.models import Q
from django.core.management.base import BaseCommand

from accounts.utils.time import calc_prev_billing_date
from accounts.utils.sqs_manager.main import SQSManager
from accounts.services.models import Services, ServicePackages
from accounts.services.utils.update_feature_usage import FeatureUsageUpdater
from accounts.packages.models import PackageFeatures

from accounts.services.exceptions import InvalidServicePackageException
from accounts.packages.enums import PackageFeatureStatusEnum
from accounts.products.enums import ProductFeatureTypeEnum
from accounts.services.utils.service_package import get_current_service_package

logger = logging.getLogger(__name__)


class InvalidDataError(Exception):
    """Custom exception for invalid input data."""

    pass


@dataclass
class Feature:
    resource_key: str
    units: int


class FeatureUsageParser:
    """Parses and provides structured access to feature usage data."""

    def __init__(self, data: t.Dict[str, t.Any]):
        """
        Initializes the parser with JSON data.

        :param data: Dictionary containing company usage data.
        :raises InvalidDataError: If required fields are missing or incorrect.
        """
        self._data = data
        self._validate_data()
        self._company_id = data["company_id"]
        self._features = {
            feature["resource_key"]: Feature(**feature)
            for feature in data.get("features", [])
        }

    def _validate_data(self):
        """Validates the structure and required fields in the input data."""
        if not isinstance(self._data, dict):
            raise InvalidDataError("Data should be a dictionary.")

        if "company_id" not in self._data or not isinstance(
            self._data["company_id"], str
        ):
            raise InvalidDataError("Invalid or missing 'company_id'.")

        if "features" in self._data and not isinstance(
            self._data["features"], list
        ):
            raise InvalidDataError("'features' should be a list.")

        for feature in self._data.get("features", []):
            if (
                not isinstance(feature, dict)
                or "resource_key" not in feature
                or "units" not in feature
            ):
                raise InvalidDataError(
                    "Each feature must have 'resource_key' and 'units' fields."
                )
            if not isinstance(feature["resource_key"], str) or not isinstance(
                feature["units"], int
            ):
                raise InvalidDataError(
                    "'resource_key' must be a string and 'units' must be an integer."
                )

    @property
    def company_id(self) -> str:
        return self._company_id

    @property
    def features(self) -> t.List[t.Dict[str, t.Any]]:
        """Returns the list of features as dictionaries."""
        return self._features

    def get_units(self, resource_key: str) -> int:
        """Returns the usage count for a specific feature, or 0 if not found."""
        return self._features.get(resource_key, Feature(resource_key, 0)).units

    def exists(self, resource_key: str) -> bool:
        """Checks if the given resource_key exists."""
        return resource_key in self._features


class Command(BaseCommand):
    help = "Process billing events from SQS"

    def add_arguments(self, parser):
        parser.add_argument(
            "--max_iter",
            "-m",
            type=int,
            default=settings.BILLING_EVENT_SQS_MAX_ITER,
            help="An optional max_iter to be run from single command",
        )
        parser.add_argument(
            "--limit",
            "-l",
            type=int,
            default=10,
            help="An optional limit on the number of items to process",
        )

    def handle(self, *args, **options):
        """
        Exit the command when the max_iter is reached, or when an exception is raised
        Supervisord will restart the command
        """
        self._sqs: SQSManager = SQSManager(settings.BILLING_EVENT_SQS_QUEUE_URL)
        max_iter: int = options["max_iter"]

        logger.title("Billing Event Consumer").debug(
            f"Consumer started with limit: {options['limit']}, max_iter: {max_iter}"
        )

        for _ in range(max_iter):
            try:
                self._process(options["limit"])
            except Exception as e:
                logger.title("Billing Event Consumer").critical(
                    e, exc_info=True
                )
                raise
            time.sleep(0.01)
        logger.title("Billing Event Consumer").debug("Consumer completed")

    def _process(self, limit: int) -> None:
        sqs_messages = self._fetch_messages(limit)
        if not sqs_messages:
            return

        for message in sqs_messages:
            self._reset_logging_uid(message["MessageId"])
            logger.title("Billing Event Consumer").info(
                f"Processing message: {message}"
            )
            self._sqs.sqs_delete_message(message["ReceiptHandle"])
            try:
                message_body = json.loads(message["Body"])
                parsed_message = FeatureUsageParser(message_body)
                self._process_message(parsed_message)
            except Services.DoesNotExist:
                logger.title("Billing Event Consumer").error(
                    "Service does not exist for company_id: "
                    + parsed_message.company_id
                )
                continue
            except InvalidDataError as e:
                logger.title("Billing Event Consumer").error(e)
                continue

    def _fetch_messages(self, limit: int, wait_time: int = 20) -> t.List:
        return self._sqs.receive_messages(limit, wait_time)

    def _process_message(self, parsed_message: FeatureUsageParser) -> None:
        service = Services.objects.select_related("billing_account").get(
            gsn=parsed_message.company_id
        )
        start_time = calc_prev_billing_date(
            service.billing_account.billing_day,
            service.activation_date,
            service.timezone,
        )
        end_time = timezone.now()

        feature_usage = FeatureUsageUpdater(service, start_time, end_time)
        package_features: dict = self._fetch_features(
            service, start_time, end_time
        )
        for _, feature in parsed_message.features.items():
            if feature.resource_key in package_features and feature.units > 0:
                package_feature_id: str = package_features[feature.resource_key]
                with transaction.atomic():
                    feature_usage.update(package_feature_id, feature.units)

    def _fetch_features(
        self, service: Services, start_time, end_time
    ) -> t.Dict[str, str]:
        current_service_package: ServicePackages = get_current_service_package(
            service.id
        )
        if not current_service_package:
            raise InvalidServicePackageException(
                "Current service package not found, for service: %s"
                % service.id
            )
        package_features = (
            PackageFeatures.objects.select_related(
                "product_feature", "product_feature_property"
            )
            .filter(
                package=current_service_package.package,
                product_feature__type=ProductFeatureTypeEnum.BILLABLE.value,
            )
            .filter(
                Q(status=PackageFeatureStatusEnum.ENABLED.value)
                | Q(
                    status=PackageFeatureStatusEnum.DISABLED.value,
                    last_disabled_date__range=(
                        start_time,
                        end_time,
                    ),
                )
            )
        )
        return {
            feature.get_resource_key(): feature.id
            for feature in package_features
        }

    def _reset_logging_uid(self, uid=None):
        try:
            from myoperator.centrallog import config, helpers

            if uid is None:
                config.configure(uid=helpers.get_uuid())
            else:
                config.configure(uid=uid)
        except ImportError:
            pass
