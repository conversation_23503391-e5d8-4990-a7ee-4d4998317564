import logging

from django.core.management.base import BaseCommand
from django.forms.models import model_to_dict

from accounts.services.models import ServicePackages
from accounts.packages.utils.package_features import (
    process_feature_suspension,
)
from accounts.packages.models import PackageFeatures
from accounts.packages.enums import PackageFeatureStatusEnum
from accounts.services.utils.pending_usage import get_pending_usage
from accounts.billing_accounts.utils.billing_account import (
    is_billing_day_passed,
)
from accounts.payments.models import (
    PaymentSubscriptions,
    SubscriptionPaymentAttempt,
)
from accounts.payments.enums import (
    PaymentSubscriptionStatusEnum,
    SubscriptionPaymentAttemptStatusEnum,
)

logger = logging.getLogger("unpaid_feature_deactivation")


class Command(BaseCommand):
    help = "Deactivate paid package features which are unpaid and exceeded grace period "

    def handle(self, *args, **options):
        try:
            features_to_deactivate = PackageFeatures.objects.filter(
                last_disabled_date=None,
                status=PackageFeatureStatusEnum.ENABLED.value,
                is_customer_cancelled=False,
                product_feature__is_paid=True,
            )

            logger.info(
                f"Count of unpaid features to be deactivated: {features_to_deactivate.count()}"
            )
            for feature in features_to_deactivate.all():
                logger.title("Deactivate Feature").info(model_to_dict(feature))
                service_package = ServicePackages.objects.filter(
                    package=feature.package
                ).first()
                if not service_package:
                    return
                usage = get_pending_usage(service_package.service)
                is_autopay_enabled = False
                mark_suspension = True
                payment_subscription = PaymentSubscriptions.objects.filter(
                    billing_account=service_package.service.billing_account,
                ).first()
                if payment_subscription:
                    if (
                        payment_subscription.status
                        == PaymentSubscriptionStatusEnum.ENABLED.value
                    ):
                        is_autopay_enabled = True

                    payment_attempt = (
                        SubscriptionPaymentAttempt.objects.filter(
                            payment_subscription=payment_subscription
                        )
                        .order_by("-created")
                        .first()
                    )
                    #  if payment attempt is pending and autopay is not enabled then do not mark suspension
                    #  if autopay is enabled and payment attempt is in success or pending then do not mark suspension
                    if payment_attempt and (
                        (
                            payment_attempt.status
                            == SubscriptionPaymentAttemptStatusEnum.PENDING.value
                            and not is_autopay_enabled
                        )
                        or (
                            is_autopay_enabled
                            and payment_attempt.status
                            in [
                                SubscriptionPaymentAttemptStatusEnum.SUCCESS.value,
                                SubscriptionPaymentAttemptStatusEnum.PENDING.value,
                            ]
                        )
                    ):
                        mark_suspension = False

                if (
                    usage["available"] < 0
                    and is_billing_day_passed(
                        service_package.service.billing_account.billing_day,
                        service_package.service.activation_date,
                    )
                    and mark_suspension
                ):
                    process_feature_suspension(
                        feature, service_package.service.gsn
                    )
        except Exception as e:
            logger.title("Feature deactivation Failed").critical(
                e, exc_info=True
            )
