import logging
from datetime import datetime, timedelta

from django.core.management.base import BaseCommand
from django.conf import settings
from django.utils import timezone

from accounts.services.models import ResourceCharges
from accounts.services.utils.resource_refund import process_refund_for_resource
from accounts.exceptions import ChatAPIException

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Refund pending resource charges"

    def handle(self, *args, **options):
        # Reset the logging uid so that each time this process runs, it has a new uid.
        self._reset_logging_uid()
        try:
            start_time = timezone.now() - timedelta(
                days=settings.RESOURCE_REFUND_CONFIG["MAX_REFUND_DAYS"]
            )
            end_time = timezone.now() - timedelta(
                seconds=settings.RESOURCE_REFUND_CONFIG["REFUND_AFTER"]
            )
            query_set = self.get_querset(start_time, end_time)
            pending_refund_count = query_set.count()
            if pending_refund_count == 0:
                logger.info("No pending refunds to process.")
                return

            logger.info(
                f"Found {pending_refund_count} pending refunds to process."
            )
            self._process(query_set)
            logger.info("refunds process completed")
        except Exception as e:
            logger.title("resource charges refund failed").critical(
                e, exc_info=True
            )
            raise

    def _process(self, query_set):
        for resource in query_set[:100]:
            self._reset_logging_uid()
            try:
                process_refund_for_resource(resource)
            except ChatAPIException as e:
                logger.title("resource charges refund failed").critical(
                    e, exc_info=True
                )
                continue

    def get_querset(self, start_time: datetime, end_time: datetime):
        # Get records in refund pending state, created more than 24 hours ago but within the last 7 days
        query_set = ResourceCharges.objects.filter(
            is_refunded=False,
            created__gte=start_time,
            created__lt=end_time,
        ).order_by("created")
        return query_set

    def _reset_logging_uid(self, uid=None):
        try:
            import uuid
            from myoperator.centrallog import config, helpers

            if uid is None:
                config.configure(uid=uuid.uuid4())
            else:
                config.configure(uid=uid)
        except ImportError:
            pass
