import logging

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.conf import settings
from django.forms.models import model_to_dict

from accounts.services.models import Services
from accounts.services.utils.service_deactivation import deactivate_demo_service
from accounts.services import constants

logger = logging.getLogger("demo_deactivation")


class Command(BaseCommand):
    help = "Deactivate active demo services whose expiry date is passed"

    def handle(self, *args, **options):
        try:
            demo_services = self.fetch_demo_services()
            logger.info(f"Count of services to be deactivated: {demo_services.count()}")
            for service in demo_services.all():
                logger.title("Demo Service").info(model_to_dict(service))
                self._reset_logging_uid()
                deactivate_demo_service(service.id, settings.DD_CRON)
        except Exception as e:
            logger.title("Demo account deactivation Failed").critical(e, exc_info=True)

    def fetch_demo_services(self):
        services = Services.active.filter(
            live_status=constants.SERVICE_LIVE_STATUS_DEMO,
            expiry_date__date__lte=timezone.now().date(),
        )
        return services

    def _reset_logging_uid(self, uid=None):
        try:
            from myoperator.centrallog import config, helpers

            if uid is None:
                config.configure(uid=helpers.get_uuid())
            else:
                config.configure(uid=uid)
        except ImportError:
            pass
