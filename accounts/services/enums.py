from django.db import models
from accounts.enums import BaseEnum


class ServiceStatusEnum(BaseEnum):
    INACTIVE = 0
    ACTIVE = 1
    SUSPENDED = 2


class ServiceLiveStatusEnum(BaseEnum):
    POSTPAID = 1
    PREPAID = 2
    DEMO = 3


class ServiceRentalStatusEnum(BaseEnum):
    INACTIVE = 0
    ACTIVE = 1


class OtherChargesStatusEnum(BaseEnum):
    PAID = 0
    UNPAID = 1


class ServiceNumberStatusEnum(BaseEnum):
    INACTIVE = 0
    ACTIVE = 1


class ServiceNumberPaidEnum(BaseEnum):
    UNPAID = 0
    PAID = 1


class ServiceNumberArchiveEnum(BaseEnum):
    FALSE = 0
    TRUE = 1


class ServiceNumberTypeEnum(BaseEnum):
    LANDLINE = "1"
    VIRTUAL = "2"
    TOLLFREE = "3"
    HEYO = "4"
    DUMMY = "5"


class ResourceTypeEnums(models.TextChoices):
    campaign = "campaign", "campaign"


class FeatureActivationRequestStatusEnums(BaseEnum):
    PENDING = 0
    SUCCESS = 1
    FAILED = 2


class ServiceRentalTransactionStatusEnum(BaseEnum):
    CREDITED = "cr"
    DEBITED = "dr"


class NumberSystemNumberStatusEnum(BaseEnum):
    LIVE = "live"
    OPEN = "open"
    SERVICE_DEACTIVE = "service_deactive"
    RESERVED = "reserved"
    DEMO = "demo"
    ARCHIVE = "archive"
    BOOKED = "booked"

    @classmethod
    def allowed_for_making_live(cls, status):
        return status in [cls.LIVE.value, cls.OPEN.value]
