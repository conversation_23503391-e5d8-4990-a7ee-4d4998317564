import typing as t
from datetime import datetime
from decimal import Decimal

from django.db.models import Manager

from accounts.services.enums import (
    OtherChargesStatusEnum,
    ServiceRentalStatusEnum,
    ServiceStatusEnum,
)

from . import constants
from .queryset import (
    FeatureUsageQuerySet,
    OtherChargesQuerySet,
    ServiceNumberQuerySet,
    ServicePackageQuerySet,
    ServiceQuerySet,
    ServiceRentalQuerySet,
)

if t.TYPE_CHECKING:
    from accounts.billing_accounts.models import BillingAccounts
    from accounts.packages.models import PackageFeatures
    from accounts.services.models import (
        FeatureUsagesLogs,
        ServiceNumbers,
        ServicePackages,
        Services,
    )


class ServiceManager(Manager):
    def get_queryset(self):
        return ServiceQuerySet(self.model, using=self._db)

    def product(self, product_id):
        return self.get_queryset().product(product_id)

    def billing_account(
        self, billing_account: "BillingAccounts", is_corporate: bool = None
    ):
        return self.get_queryset().billing_account(
            billing_account, is_corporate
        )

    def total_current_usage(self):
        return self.get_queryset().total_current_usage()


class LiveServiceManager(ServiceManager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(
                live_status__in=[
                    constants.SERVICE_LIVE_STATUS_PREPAID,
                    constants.SERVICE_LIVE_STATUS_POSTPAID,
                ]
            )
        )


class ActiveServiceManager(ServiceManager):
    def get_queryset(self):
        return (
            super().get_queryset().filter(status=ServiceStatusEnum.ACTIVE.value)
        )


class SuspendedServiceManager(ServiceManager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(status=ServiceStatusEnum.SUSPENDED.value)
        )


class InactiveServiceManager(ServiceManager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(status=ServiceStatusEnum.INACTIVE.value)
        )


class ServiceRentalManager(Manager):
    def get_queryset(self):
        return ServiceRentalQuerySet(self.model, using=self._db)

    def service(self, service_id: str):
        return self.get_queryset().service(service_id)

    def active(self):
        return self.get_queryset().active()

    def billing_account(
        self, billing_account: "BillingAccounts", is_corporate: bool = None
    ):
        return self.get_queryset().billing_account(
            billing_account, is_corporate
        )

    def total_pending_rental(self):
        return self.get_queryset().total_pending_rental()


class ActiveServiceRentalManager(ServiceRentalManager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(status=ServiceRentalStatusEnum.ACTIVE.value)
        )


class ServicePackageManager(Manager):
    def get_queryset(self):
        return ServicePackageQuerySet(self.model, using=self._db)

    def service(self, service):
        return self.get_queryset().service(service)

    def active(self, time):
        return self.get_queryset().active(time)

    def active_packages(self, time):
        return self.get_queryset().active_packages(time)

    def current_package(
        self, service: "Services", reference_time: datetime
    ) -> t.Optional["ServicePackages"]:
        """
        Fetches the current active package for a given service.

        - service: Service instance or service ID.
        - reference_time: The time to check against (defaults to now).
        """

        # First, try to fetch an active package within the reference time
        package = self.service(service).active_packages(reference_time).first()

        # If no active package exists, return the latest package for the service
        if not package:
            package = self.service(service).order_by("-created").first()

        return package

    def upcoming_package(
        self, service: "Services", reference_time: datetime
    ) -> t.Optional["ServicePackages"]:
        """
        Fetches the upcoming package for a given service.

        - service: Service instance or service ID.
        - reference_time: The time to check against (defaults to now).
        """
        package = self.service(service).future_packages(reference_time).first()
        return package


class OtherChargesManager(Manager):
    def get_queryset(self):
        return OtherChargesQuerySet(self.model, using=self._db)

    def billing_account(
        self, billing_account: "BillingAccounts", is_corporate: bool = None
    ):
        return self.get_queryset().billing_account(
            billing_account, is_corporate
        )

    def total_other_charges(self) -> Decimal:
        return self.get_queryset().total_other_charges()


class UnpaidOtherChargesManager(OtherChargesManager):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(status=OtherChargesStatusEnum.UNPAID.value)
        )


class ServiceNumberManager(Manager):
    def get_queryset(self):
        return ServiceNumberQuerySet(self.model, using=self._db)

    def billing_account(
        self, billing_account: "BillingAccounts", is_corporate: bool = None
    ) -> "ServiceNumberQuerySet":
        """
        Returns a queryset of service numbers filtered by billing account.
        Args:
            billing_account (BillingAccounts): The billing account to filter by.
            is_corporate (bool, optional): If True, filters for corporate accounts.
        Returns:
            ServiceNumberQuerySet: A queryset of service numbers associated with the billing account.
        """
        return self.get_queryset().billing_account(
            billing_account, is_corporate
        )

    def total_unpaid_number_cost(self) -> Decimal:
        return self.get_queryset().total_unpaid_number_cost()

    def is_allocated_to_other_service(
        self, service_id: str, service_number: str
    ) -> bool:
        """
        Checks if a service number is allocated to other service.
        Args:
            service_id (str): ID of the service to exclude from the check.
            service_number (str): Service Number.
        Returns:
            bool: True if the number is allocated to other service, False otherwise.
        """
        return self.get_queryset().is_allocated_to_other_service(
            service_id, service_number
        )

    def last_allocated_number(self, service: "Services") -> "ServiceNumbers":
        """
        Returns the last allocated number for a service.
        Args:
            service (Services): The service.
        Returns:
            ServiceNumbers: The last allocated number for the service.
        """
        return self.get_queryset().last_allocated_number(service)

    def unpaid(self):
        """
        Returns all unpaid service numbers.
        """
        return self.get_queryset().unpaid()


class FeatureUsageManager(Manager):
    def get_queryset(self):
        return FeatureUsageQuerySet(self.model, using=self._db)

    def service(self, service: "Services"):
        return self.get_queryset().service(service)

    def find_max_leg_a_usage_instance(
        self,
        service: "Services",
        package_feature: "PackageFeatures",
        start_date: datetime,
    ) -> "FeatureUsagesLogs":
        return self.get_queryset().find_max_leg_a_usage_instance(
            service, package_feature, start_date
        )
