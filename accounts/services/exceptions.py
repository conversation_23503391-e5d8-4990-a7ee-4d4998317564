from accounts.exceptions import BaseException
from rest_framework import status
from accounts.error_codes import (
    VALIDATION_ERROR,
    RESOURCE_CHARGE_INSUFFICIENT_BALANCE,
    RESOURCE_CHARGE_ALREADY_EXISTS,
    SERVICE_NOT_DEMO,
)


class InvalidServiceException(BaseException):
    message = "Invalid Service"


class ServiceAlreadyRunningException(BaseException):
    http_status_code = status.HTTP_400_BAD_REQUEST
    error_code = VALIDATION_ERROR
    message = "Service is already running on the given service number"


class ServiceActivationFailedException(BaseException):
    message = "Service activation failed"


class ServiceAlreadyDeactivated(BaseException):
    message = "Service is already deactivated"


class ServiceNotDemoException(InvalidServiceException):
    http_status_code = status.HTTP_400_BAD_REQUEST
    error_code = SERVICE_NOT_DEMO
    message = "Service not in demo state"


class PendingUsageException(BaseException):
    message = "Pending Usage Error"


class ResourceChargeAlreadyExistsException(BaseException):
    http_status_code = status.HTTP_400_BAD_REQUEST
    message = "resource_id already exists"
    error_code = RESOURCE_CHARGE_ALREADY_EXISTS


class InsufficientCreditsException(BaseException):
    http_status_code = status.HTTP_400_BAD_REQUEST
    message = "Insufficient Balance"
    error_code = RESOURCE_CHARGE_INSUFFICIENT_BALANCE


class ResourceChargeRefundException(BaseException):
    message = "Resource charge refund failed."
    http_status_code = status.HTTP_424_FAILED_DEPENDENCY


class FeatureProcessingFailedException(BaseException):
    message = "Feature payment processing error"


class InvalidFeaturePaymentException(BaseException):
    message = "Invalid Payment"


class InvalidServicePackageException(BaseException):
    message = "Invalid Service Package"
