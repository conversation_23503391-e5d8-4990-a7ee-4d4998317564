import logging
from abc import ABC
from typing import Any, Optional
from django.core.cache import cache
from redis import Redis

logger = logging.getLogger(__name__)


class BaseCacheHandler(ABC):
    def __init__(self, key: str) -> None:
        self._key = key
        self.redis_client = cache.client.get_client(write=True)

    def key(self):
        return self._key

    def get(self):
        return cache.get(self._key)

    # @abstractmethod
    def save(self, data, timeout):
        return cache.set(self._key, data, timeout)

    def delete(self):
        cache.delete(self._key)

    def expire(self, time=0):
        cache.expire(self._key, time)

    def ttl(self):
        return cache.ttl(self._key)

    def exists(self):
        return True if cache.get(self._key, None) else False

    def incr(self, timeout=0) -> int:
        try:
            result = cache.incr(self._key)
            if timeout:
                cache.expire(self._key, timeout)
            return result
        except ValueError:
            cache.set(self._key, 1, timeout=timeout)
            return 1

    # ----------- HASH METHODS ------------

    def hget(self, field):
        """Get value of field in the Redis hash."""
        value = self.redis_client.hget(cache.make_key(self._key), field)
        return value.decode() if value is not None else None

    def hset(self, field, value):
        """Set field in the Redis hash to value."""
        return self.redis_client.hset(cache.make_key(self._key), field, value)

    def hmset(self, mapping: dict, timeout=None):
        """Set multiple fields in the Redis hash."""
        key_with_prefix = cache.make_key(self._key)
        result = self.redis_client.hset(key_with_prefix, mapping=mapping)
        if timeout is not None:
            self.redis_client.expire(key_with_prefix, timeout)
        return result

    def hgetall(self) -> dict:
        """Get all fields and values from the Redis hash, decoded as UTF-8 strings."""
        raw_data = self.redis_client.hgetall(cache.make_key(self._key))
        return {k.decode(): v.decode() for k, v in raw_data.items()}

    def hincrby(self, field, amount=1, timeout=None):
        """Increment the integer value of a hash field by the given amount."""
        key_with_prefix = cache.make_key(self._key)
        result = self.redis_client.hincrby(key_with_prefix, field, amount)
        if timeout is not None:
            self.redis_client.expire(key_with_prefix, timeout)
        return result

    def hdel(self, *fields):
        """Delete one or more hash fields."""
        return self.redis_client.hdel(cache.make_key(self._key), *fields)

    def hexists(self, field):
        """Determine if a hash field exists."""
        return self.redis_client.hexists(cache.make_key(self._key), field)


class BaseCacheHandler_V2(ABC):
    def get_redis_client(self) -> Redis:
        return cache.client.get_client(write=True)

    def ttl(self, key: str) -> int:
        return self.get_redis_client().ttl(cache.make_key(key))

    def get(self, key: str):
        return cache.get(key)

    def save(self, key: str, data, timeout: Optional[int] = None):
        return cache.set(key, data, timeout)

    def delete(self, key: str):
        cache.delete(key)

    def exists(self, key: str):
        return True if cache.get(key, None) else False

    def incr(self, key: str, timeout: Optional[int] = None) -> int:
        try:
            logger.info(f"Incrementing attempt for {key}")
            result = cache.incr(key)
            if timeout:
                cache.expire(key, timeout)
            return result
        except ValueError:
            logger.info(f"Setting attempt for {key} to 1")
            self.save(key, 1, timeout=timeout)
            return 1

    def hget(self, key: str, field: str):
        """Get value of field in the Redis hash."""
        value = self.get_redis_client().hget(cache.make_key(key), field)
        return value.decode() if value is not None else None

    def hset(self, key: str, field: str, value: Any):
        """Set field in the Redis hash to value."""
        return self.get_redis_client().hset(cache.make_key(key), field, value)

    def hmset(self, key: str, mapping: dict, timeout=None):
        """Set multiple fields in the Redis hash."""
        key_with_prefix = cache.make_key(key)
        result = self.get_redis_client().hset(key_with_prefix, mapping=mapping)
        if timeout is not None:
            self.get_redis_client().expire(key_with_prefix, timeout)
        return result

    def hgetall(self, key: str) -> dict:
        """Get all fields and values from the Redis hash, decoded as UTF-8 strings."""
        raw_data = self.get_redis_client().hgetall(cache.make_key(key))
        return {k.decode(): v.decode() for k, v in raw_data.items()}

    def hincrby(
        self,
        key: str,
        field: str,
        amount: int = 1,
        timeout: Optional[int] = None,
    ):
        """Increment the integer value of a hash field by the given amount."""
        key_with_prefix = cache.make_key(key)
        result = self.get_redis_client().hincrby(key_with_prefix, field, amount)
        if timeout is not None:
            self.get_redis_client().expire(key_with_prefix, timeout)
        return result

    def hdel(self, key: str, *fields):
        """Delete one or more hash fields."""
        return self.get_redis_client().hdel(cache.make_key(key), *fields)

    def hexists(self, key: str, field: str):
        """Determine if a hash field exists."""
        return self.get_redis_client().hexists(cache.make_key(key), field)


class BaseOtpCacheHandler(BaseCacheHandler_V2):
    MAX_ATTEMPTS = 10

    def get_max_send_attempts(self) -> int:
        return self.MAX_ATTEMPTS

    def send_attempt_allowed(self) -> bool:
        raise NotImplementedError("Subclass must implement this method")

    def get_max_verification_attempts(self) -> int:
        return self.MAX_ATTEMPTS

    def verify_attempt_allowed(self) -> bool:
        raise NotImplementedError("Subclass must implement this method")

    def attempt_allowed(self, attempt: int, max_attempts=MAX_ATTEMPTS) -> bool:
        if attempt is not None and attempt >= max_attempts:
            logger.info(
                f"Attempt not allowed with no_of_attempts: {attempt} and max_attempts: {max_attempts}"
            )
            return False
        logger.info(
            f"Attempt allowed with no_of_attempts: {attempt} and max_attempts: {max_attempts}"
        )
        return True
