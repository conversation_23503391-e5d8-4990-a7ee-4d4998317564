# accounts/utils/file_handlers/base_file_handler.py

import io
import logging
import typing as t

from accounts.utils.api_services.file_service import FileService
from django.conf import settings

from accounts.utils.aws.s3 import S3Client

logger = logging.getLogger(__name__)


class FileHandler:
    def __init__(self, bucket: str = settings.AWS_STORAGE_BUCKET_NAME) -> None:
        self.file_service = FileService()
        self.s3_client = S3Client(bucket)

    def download_file_from_url(
        self,
        url: str,
        timeout: t.Optional[int] = None,
        output_path: t.Optional[str] = None,
    ) -> t.Union[str, io.BytesIO]:
        """Downloads a file from the given URL and saves it to the specified output path or returns it in memory.

        Args:
            url (str): The URL to download the file from.
            timeout (t.Optional[int], optional): The timeout for the request in seconds. Defaults to None.
            output_path (t.Optional[str], optional): The path where the file should be saved. If None, the file is returned in memory. Defaults to None.

        Returns:
            t.Union[str, io.BytesIO]: The path to the saved file if output_path is provided, otherwise a BytesIO object containing the file content.
        """
        logger.info(
            f"[FileHandler] Downloading file from URL: {url} with timeout: {timeout} and output path: {output_path}"
        )

        response = self.file_service.get_file_from_url(url=url, timeout=timeout)

        if output_path:
            with open(output_path, "wb") as f:
                f.write(response.content)
            return output_path
        return io.BytesIO(response.content)

    def upload_file_to_s3(self, file_data: io.BytesIO, key: str):
        logger.info(f"[FileHandler] Uploading file to S3 with key: {key}")
        self.s3_client.upload_file_obj(file_data, key)
