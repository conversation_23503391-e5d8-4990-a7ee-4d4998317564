import re

from accounts.utils.constants import AADHAAR_NUMBER_REGEX


def is_valid_aadhaar_number(aadhaar_number: str) -> bool:
    """Checks if the provided Aadhaar number is valid.

    Args:
        aadhaar_number (str): The Aadhaar number to be validated.

    Returns:
        bool: True if the Aadhaar number is valid, False otherwise.
    """

    aadhaar_number = aadhaar_number.strip()

    if not aadhaar_number:
        return False

    return re.fullmatch(AADHAAR_NUMBER_REGEX, aadhaar_number) is not None
