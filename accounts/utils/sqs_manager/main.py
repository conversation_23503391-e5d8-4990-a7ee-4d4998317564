import boto3

from django.conf import settings
from .exceptions import SqsManagerException, NoMessagesInSQS


class SQSManager:
    def __init__(self, queue_url: str):
        self._client = boto3.client(
            "sqs",
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_DEFAULT_REGION,
        )
        self._queue_url = queue_url

    def receive_messages(self, limit: int = 10, wait_time: int = 20) -> list:
        """
        Receives a list of messages from the SQS queue.

        Args:
            limit (int): Maximum number of messages to receive.
            wait_time (int, optional): The duration (in seconds) for which the call waits for a message to arrive. Defaults to 20 seconds.

        Returns:
            list: A list of messages received from the SQS queue.

        Raises:
            SqsManagerException: If an error occurs while receiving messages.
        """
        try:
            messages = self._client.receive_message(
                QueueUrl=self._queue_url,
                WaitTimeSeconds=wait_time,
                AttributeNames=["All"],
                MaxNumberOfMessages=limit,
            )
            if "Messages" in messages:
                return messages["Messages"]
            return []
        except Exception as e:
            raise SqsManagerException(str(e))

    def sqs_delete_message(self, message_handle: str):
        """
        Deletes the message with the specified message handle from the SQS queue.

        Args:
            message_handle (str): The message handle of the message to delete.

        Returns:
            dict: The response from SQS, which includes the message ID.

        Raises:
            SqsManagerException: If an error occurs while deleting the message.
        """
        try:
            return self._client.delete_message(
                QueueUrl=self._queue_url, ReceiptHandle=message_handle
            )
        except Exception as e:
            raise SqsManagerException(str(e))

    def fetch_message_sp(self, limit: int = 10) -> list:
        """
        Receives a list of messages from the SQS queue in short polling mode.

        Args:
            limit (int, optional): Maximum number of messages to receive. Defaults to 10.

        Returns:
            list: A list of messages received from the SQS queue.

        Raises:
            SqsManagerException: If an error occurs while receiving messages.
        """
        return self.receive_messages(limit, 0)
