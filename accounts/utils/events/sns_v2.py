import json
import logging
import boto3
from botocore.exceptions import Client<PERSON>rror
from django.conf import settings
from django.utils import timezone

from .base_event import BaseEvent
from .exceptions import ValidationException, EventException

logger = logging.getLogger(__name__)


class SnsEventV2(BaseEvent):

    service_type = None
    arn = None
    features = []
    version = "0.1.1"
    namespace = None

    def __init__(self):
        self.sns = boto3.client(
            "sns",
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_DEFAULT_REGION,
        )
        super().__init__()

    def add_feature(self, feature):
        self.features.append(feature)
        return self

    def prepare_data(self):
        message = {
            "Message": {
                "uid": str(self.uid()),
                "version": self.version,
                "namespace": self.namespace,
                "action": self.EVENT_ACTION,
                "service_type": self.service_type,
                "etag": self.etag(),
                "created_at": timezone.now().strftime("%Y-%m-%d %H:%M:%S"),
                "features": self.features,
                "data": self.get_data(),
            }
        }
        return json.dumps(message)

    def validate(self):
        if not self.REQUIRED_PARAMS:
            return True

        missing_params = [
            param
            for param in self.REQUIRED_PARAMS
            if param not in self.get_data()
        ]
        if missing_params:
            raise ValidationException(
                f"Missing required parameters: {', '.join(missing_params)}"
            )

        return True

    def send(self):
        try:
            self.validate()

            if self.service_type is None:
                raise ValueError(f"Invalid service_type: {self.service_type}")

            if self.arn is None:
                raise ValueError(f"Invalid arn: {self.arn}")

            if self.features is None:
                raise ValueError(f"Invalid features: {self.features}")

            message = self.prepare_data()

            logger.title("SNS Payload").info(
                {
                    "arn": self.arn,
                    "action": self.EVENT_ACTION,
                    "message": message,
                }
            )
            response = self.sns.publish(TopicArn=self.arn, Message=message)
            logger.title("SNS Response").info(response)
            return response["MessageId"]
        except ClientError as e:
            logger.title(
                f"Couldn't publish message to topic {self.arn}."
            ).critical(e, exc_info=True)
            raise EventException(f"Event Failed: {str(e)}")
