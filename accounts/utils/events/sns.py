import json
import logging
import boto3
from botocore.exceptions import ClientError
from django.conf import settings
from django.utils import timezone
from accounts import __version__

from .base_event import BaseEvent
from .exceptions import ValidationException, EventException

logger = logging.getLogger(__name__)


class SnsEvent(BaseEvent):

    service_type = None
    arn = None

    def __init__(self):
        self.sns = boto3.client(
            "sns",
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_DEFAULT_REGION,
        )
        super().__init__()

    def prepare_attributes(self):
        return {
            "uid": {"StringValue": str(self.uid()), "DataType": "String"},
            "version": {"StringValue": __version__, "DataType": "String"},
            "namespace": {"StringValue": self.NAMESPACE, "DataType": "String"},
            "action": {"StringValue": self.EVENT_ACTION, "DataType": "String"},
            "service_type": {
                "StringValue": self.service_type,
                "DataType": "String",
            },
            "etag": {"StringValue": self.etag(), "DataType": "String"},
            "created_at": {
                "StringValue": timezone.now().strftime("%Y-%m-%d %H:%M:%S"),
                "DataType": "String",
            },
        }

    def prepare_data(self):
        data = self.get_data()
        if not bool(data):
            raise ValidationException("Event data can not be None or Empty")

        if not isinstance(data, dict):
            raise ValidationException(
                f"Event require dict but {type(data)} provided"
            )

        return json.dumps(data)

    def send(self):
        try:
            self.validate()

            if self.service_type is None:
                raise ValueError(f"Invalid service_type: {self.service_type}")

            if self.arn is None:
                raise ValueError(f"Invalid arn: {self.arn}")

            attributes = self.prepare_attributes()
            message = self.prepare_data()

            logger.title("SNS Payload").info(
                {
                    "arn": self.arn,
                    "action": self.EVENT_ACTION,
                    "attributes": attributes,
                    "message": message,
                }
            )
            response = self.sns.publish(
                TopicArn=self.arn, Message=message, MessageAttributes=attributes
            )
            logger.title("SNS Response").info(response)
            return response["MessageId"]
        except ClientError as e:
            logger.title(
                f"Couldn't publish message to topic {self.arn}."
            ).critical(e, exc_info=True)
            raise EventException(f"Event Failed: {str(e)}")

    def validate(self):
        if not self.REQUIRED_PARAMS:
            return True

        missing_params = [
            param for param in self.REQUIRED_PARAMS if param not in self.get_data()
        ]
        if missing_params:
            raise ValidationException(
                f"Missing required parameters: {', '.join(missing_params)}"
            )

        return True
