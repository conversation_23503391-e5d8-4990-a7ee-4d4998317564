import abc
import uuid


class BaseEvent(metaclass=abc.ABCMeta):
    NAMESPACE = "accounts"
    REQUIRED_PARAMS = None
    data = {}

    def __init__(self) -> None:
        self.data = {}
        self.REQUIRED_PARAMS = None

    @abc.abstractmethod
    def prepare_data(self):
        raise NotImplementedError("prepare_data() is not implemented")

    @abc.abstractmethod
    def send(self):
        raise NotImplementedError("send() is not implemented")

    @abc.abstractmethod
    def validate(self):
        raise NotImplementedError("validate() is not implemented")

    def uid(self):
        return uuid.uuid4()

    def etag(self):
        return "qwerty"

    def set_data(self, key, value):
        self.data[key] = value

    def get_data(self):
        return self.data
