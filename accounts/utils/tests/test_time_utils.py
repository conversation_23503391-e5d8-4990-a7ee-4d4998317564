import pytest
from django.test import TestCase
from django.utils import timezone
from dateutil import tz
from accounts.utils.time import (
    calculate_local_hour,
    calc_end_date_by_months,
    calc_end_date_by_days,
    convert_utc_to_local,
    convert_local_to_utc,
    next_billing_date_utc,
    calc_prev_billing_date,
    billing_day_from_activation_date,
)
from unittest.mock import patch


class TestTimeUtils(TestCase):
    def test_calculate_local_hour_success(self):
        utc_now = timezone.now()
        tzone = tz.gettz("Asia/Kolkata")
        expected_hour = utc_now.astimezone(tzone).strftime("%H")
        calculated_hour = calculate_local_hour("Asia/Kolkata")
        assert calculated_hour == expected_hour

    def test_calc_end_date_by_months_success(self):
        start_date = timezone.datetime(
            2023, 1, 1, 10, 20, 30, tzinfo=tz.gettz("UTC")
        )
        expected_end_date = timezone.datetime(
            2023, 4, 1, 10, 20, 30, tzinfo=tz.gettz("UTC")
        )
        assert calc_end_date_by_months(start_date, 3) == expected_end_date

    def test_calc_end_date_by_months_failure(self):
        with pytest.raises(TypeError):
            calc_end_date_by_months("2023-04-21 10:00:00", 3)

    def test_calc_end_date_by_days_success(self):
        start_date = timezone.datetime(
            2023, 4, 1, 10, 20, 30, tzinfo=tz.gettz("UTC")
        )
        expected_end_date = timezone.datetime(
            2023, 4, 6, 10, 20, 30, tzinfo=tz.gettz("UTC")
        )
        assert calc_end_date_by_days(start_date, 5) == expected_end_date

    def test_calc_end_date_by_days_failure(self):
        with pytest.raises(TypeError):
            calc_end_date_by_days("2023-04-21 10:00:00", 5)

    def test_convert_utc_to_local_success(self):
        date_time = timezone.datetime(
            2023, 4, 21, 10, 0, 0, tzinfo=tz.gettz("UTC")
        )
        expected_local_datetime = timezone.datetime(
            2023, 4, 21, 15, 30, 0, tzinfo=tz.gettz("Asia/Kolkata")
        )
        assert (
            convert_utc_to_local(date_time, "Asia/Kolkata")
            == expected_local_datetime
        )
        assert convert_utc_to_local(date_time, "Asia/Kolkata") is not date_time

        # For US
        pst_tz = tz.gettz("America/Los_Angeles")
        local_date = date_time.astimezone(pst_tz)
        if local_date.dst():
            expected_local_datetime = timezone.datetime(
                2023, 4, 21, 3, 0, 0, tzinfo=tz.gettz("America/Los_Angeles")
            )
        else:
            expected_local_datetime = timezone.datetime(
                2023, 4, 21, 2, 0, 0, tzinfo=tz.gettz("America/Los_Angeles")
            )
        assert (
            convert_utc_to_local(date_time, "America/Los_Angeles")
            == expected_local_datetime
        )
        assert (
            convert_utc_to_local(date_time, "America/Los_Angeles")
            is not date_time
        )

    def test_convert_utc_to_local_exception(self):
        with pytest.raises(TypeError):
            convert_utc_to_local("2023-04-21 10:00:00", "Asia/Kolkata")

    def test_convert_local_to_utc_success(self):
        local_datetime = timezone.datetime(
            2023, 4, 21, 15, 30, 0, tzinfo=tz.gettz("Asia/Kolkata")
        )
        expected_utc_datetime = timezone.datetime(
            2023, 4, 21, 10, 0, 0, tzinfo=tz.gettz("UTC")
        )
        assert convert_local_to_utc(local_datetime) == expected_utc_datetime
        assert convert_local_to_utc(local_datetime) is not local_datetime

        # For US
        local_datetime = timezone.datetime(
            2023, 4, 21, 10, 0, 0, tzinfo=tz.gettz("America/Los_Angeles")
        )
        if local_datetime.dst():
            expected_utc_datetime = timezone.datetime(
                2023, 4, 21, 17, 0, 0, tzinfo=tz.gettz("UTC")
            )
        else:
            expected_utc_datetime = timezone.datetime(
                2023, 4, 21, 18, 0, 0, tzinfo=tz.gettz("UTC")
            )
        assert convert_local_to_utc(local_datetime) == expected_utc_datetime
        assert convert_local_to_utc(local_datetime) is not local_datetime

    def test_convert_local_to_utc_exception(self):
        with pytest.raises(AttributeError):
            convert_local_to_utc("2023-04-21 10:00:00")

    def test_calc_next_billing_date(self):
        with patch("django.utils.timezone.now") as mock_now:

            activation_datetime = timezone.datetime.strptime(
                "2020-01-01 19:10:00", "%Y-%m-%d %H:%M:%S"
            ).replace(tzinfo=tz.gettz("UTC"))
            billing_day = 1
            # case 1: jan 1st activation date in leap year (29 days)
            mock_now.return_value = timezone.datetime(
                2020, 2, 10, 19, 10, 0, tzinfo=timezone.utc
            )
            result = next_billing_date_utc(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert isinstance(result, timezone.datetime)
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2020-02-29 18:30:00"
            assert result.strftime("%Y-%m-%d %H:%M:%S") != "2020-03-01 18:30:00"

            # case 2: jan 1st activation date in normal year for month of feb (28 days)
            mock_now.return_value = timezone.datetime(
                2023, 2, 10, 19, 10, 0, tzinfo=timezone.utc
            )
            result = next_billing_date_utc(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2023-02-28 18:30:00"
            assert result.strftime("%Y-%m-%d %H:%M:%S") != "2023-03-01 18:30:00"

            # case 3: jan 1st activation date in normal year for month of march (31 days)
            mock_now.return_value = timezone.datetime(
                2023, 3, 10, 15, 10, 0, tzinfo=timezone.utc
            )
            result = next_billing_date_utc(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2023-03-31 18:30:00"
            assert result.strftime("%Y-%m-%d %H:%M:%S") != "2023-04-01 18:30:00"

            # case 3: jan 1st activation date in normal year for month of April (30 days)
            mock_now.return_value = timezone.datetime(
                2023, 4, 10, 15, 10, 0, tzinfo=timezone.utc
            )
            result = next_billing_date_utc(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2023-04-30 18:30:00"
            assert result.strftime("%Y-%m-%d %H:%M:%S") != "2023-05-01 18:30:00"

            activation_datetime = timezone.datetime.strptime(
                "2020-01-31 19:10:00", "%Y-%m-%d %H:%M:%S"
            ).replace(tzinfo=tz.gettz("UTC"))
            billing_day = 31
            # case 5: jan 31st activation date in leap year (29 days)
            mock_now.return_value = timezone.datetime(
                2020, 2, 10, 19, 10, 0, tzinfo=timezone.utc
            )
            result = next_billing_date_utc(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2020-02-28 18:30:00"
            assert result.strftime("%Y-%m-%d %H:%M:%S") != "2020-02-29 18:30:00"

            # case 6: jan 31st activation date in normal year for month of feb (28 days)
            mock_now.return_value = timezone.datetime(
                2023, 2, 10, 19, 10, 0, tzinfo=timezone.utc
            )
            result = next_billing_date_utc(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2023-02-27 18:30:00"
            assert result.strftime("%Y-%m-%d %H:%M:%S") != "2023-02-28 18:30:00"

            # case 7: jan 31st activation date in normal year for month of march (31 days)
            mock_now.return_value = timezone.datetime(
                2023, 3, 10, 15, 10, 0, tzinfo=timezone.utc
            )
            result = next_billing_date_utc(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2023-03-30 18:30:00"
            assert result.strftime("%Y-%m-%d %H:%M:%S") != "2023-03-31 18:30:00"

            # case 8: jan 31st activation date in normal year for month of April (30 days)
            mock_now.return_value = timezone.datetime(
                2023, 4, 10, 15, 10, 0, tzinfo=timezone.utc
            )
            result = next_billing_date_utc(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2023-04-29 18:30:00"
            assert result.strftime("%Y-%m-%d %H:%M:%S") != "2023-04-30 18:30:00"

            activation_datetime = timezone.datetime.strptime(
                "2020-02-29 19:10:00", "%Y-%m-%d %H:%M:%S"
            ).replace(tzinfo=tz.gettz("UTC"))
            billing_day = 29
            # case 9: 29th feb activation date in leap year (29 days)
            mock_now.return_value = timezone.datetime(
                2020, 2, 10, 19, 10, 0, tzinfo=timezone.utc
            )
            result = next_billing_date_utc(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2020-03-28 18:30:00"
            assert result.strftime("%Y-%m-%d %H:%M:%S") != "2020-03-29 18:30:00"

            # case 10: 29th feb activation date in normal year for month of feb (28 days)
            mock_now.return_value = timezone.datetime(
                2023, 2, 10, 19, 10, 0, tzinfo=timezone.utc
            )
            result = next_billing_date_utc(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2023-02-27 18:30:00"
            assert result.strftime("%Y-%m-%d %H:%M:%S") != "2023-02-28 18:30:00"

            # case 11: 29th feb activation date in normal year for month of march (31 days)
            mock_now.return_value = timezone.datetime(
                2023, 3, 10, 15, 10, 0, tzinfo=timezone.utc
            )
            result = next_billing_date_utc(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2023-03-28 18:30:00"
            assert result.strftime("%Y-%m-%d %H:%M:%S") != "2023-03-29 18:30:00"

            # case 12: 29th feb activation date in normal year for month of April (30 days)
            mock_now.return_value = timezone.datetime(
                2023, 4, 10, 15, 10, 0, tzinfo=timezone.utc
            )
            result = next_billing_date_utc(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2023-04-28 18:30:00"
            assert result.strftime("%Y-%m-%d %H:%M:%S") != "2023-04-29 18:30:00"

    def test_calc_next_billing_date_exception(self):
        with pytest.raises(AttributeError):
            next_billing_date_utc(1, "2023-01-01 15:10:00", "Asia/Kolkata")

        with pytest.raises(TypeError):
            activation_datetime = timezone.datetime.strptime(
                "2020-01-31 19:10:00", "%Y-%m-%d %H:%M:%S"
            ).replace(tzinfo=tz.gettz("UTC"))
            next_billing_date_utc("31", activation_datetime, "Asia/Kolkata")

    def test_calc_prev_billing_date(self):
        with patch("django.utils.timezone.now") as mock_now:

            activation_datetime = timezone.datetime.strptime(
                "2023-01-10 12:28:41", "%Y-%m-%d %H:%M:%S"
            ).replace(tzinfo=tz.gettz("UTC"))
            billing_day = 10
            # case 1: activation date is same as current date
            mock_now.return_value = timezone.datetime(
                2023, 1, 10, 19, 10, 0, tzinfo=timezone.utc
            )
            result = calc_prev_billing_date(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert isinstance(result, timezone.datetime)
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2023-01-09 18:30:00"
            assert result.strftime("%Y-%m-%d %H:%M:%S") != "2023-01-10 18:30:00"

            # case 2: current date is greater than activation date but of the same month and billing day is smaller than current day
            mock_now.return_value = timezone.datetime(
                2023, 1, 20, 19, 10, 0, tzinfo=timezone.utc
            )
            result = calc_prev_billing_date(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2023-01-09 18:30:00"
            assert result.strftime("%Y-%m-%d %H:%M:%S") != "2023-01-10 18:30:00"

            # case 3: current date is greater than activation date but of different month and billing day is greater than current day
            activation_datetime = timezone.datetime.strptime(
                "2022-12-05 10:00:00", "%Y-%m-%d %H:%M:%S"
            ).replace(tzinfo=tz.gettz("UTC"))
            billing_day = 5
            mock_now.return_value = timezone.datetime(
                2023, 2, 1, 19, 10, 0, tzinfo=timezone.utc
            )
            result = calc_prev_billing_date(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2023-01-04 18:30:00"
            assert result.strftime("%Y-%m-%d %H:%M:%S") != "2023-01-05 18:30:00"

            # case 4: current date is greater than activation date (feb normal year) but of different month and billing day is smaller than current day
            activation_datetime = timezone.datetime.strptime(
                "2023-02-06 19:10:00", "%Y-%m-%d %H:%M:%S"
            ).replace(tzinfo=tz.gettz("UTC"))
            billing_day = 6
            mock_now.return_value = timezone.datetime(
                2023, 3, 20, 19, 10, 0, tzinfo=timezone.utc
            )
            result = calc_prev_billing_date(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2023-03-05 18:30:00"
            assert (
                result.strftime("%Y-%m-%d %H:%M:%S") != "22023-03-06 18:30:00"
            )

            # case 5: current date is greater than activation date (feb leap year) but of different month and billing day is smaller than current day
            activation_datetime = timezone.datetime.strptime(
                "2020-02-29 19:10:00", "%Y-%m-%d %H:%M:%S"
            ).replace(tzinfo=tz.gettz("UTC"))
            billing_day = 29
            mock_now.return_value = timezone.datetime(
                2023, 3, 30, 19, 10, 0, tzinfo=timezone.utc
            )
            result = calc_prev_billing_date(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2023-03-28 18:30:00"
            assert (
                result.strftime("%Y-%m-%d %H:%M:%S") != "22023-03-29 18:30:00"
            )

            # case 6: current date is greater than activation date (feb leap year) but of different month and billing day is greater than current day
            activation_datetime = timezone.datetime.strptime(
                "2020-02-29 19:10:00", "%Y-%m-%d %H:%M:%S"
            ).replace(tzinfo=tz.gettz("UTC"))
            billing_day = 29
            mock_now.return_value = timezone.datetime(
                2023, 3, 20, 19, 10, 0, tzinfo=timezone.utc
            )
            result = calc_prev_billing_date(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2023-02-27 18:30:00"
            assert (
                result.strftime("%Y-%m-%d %H:%M:%S") != "22023-02-28 18:30:00"
            )

            # case 7: current date is greater than activation date but of different month and billing day is last day of the month
            activation_datetime = timezone.datetime.strptime(
                "2022-10-31 13:10:00", "%Y-%m-%d %H:%M:%S"
            ).replace(tzinfo=tz.gettz("UTC"))
            billing_day = 31
            mock_now.return_value = timezone.datetime(
                2023, 3, 1, 19, 10, 0, tzinfo=timezone.utc
            )
            result = calc_prev_billing_date(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2023-02-27 18:30:00"
            assert (
                result.strftime("%Y-%m-%d %H:%M:%S") != "22023-02-28 18:30:00"
            )

            # case 8: billing day is last day of the month and current date is last day of feb month
            activation_datetime = timezone.datetime.strptime(
                "2022-10-31 13:10:00", "%Y-%m-%d %H:%M:%S"
            ).replace(tzinfo=tz.gettz("UTC"))
            billing_day = 31
            mock_now.return_value = timezone.datetime(
                2023, 2, 28, 15, 10, 0, tzinfo=timezone.utc
            )
            result = calc_prev_billing_date(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2023-02-27 18:30:00"
            assert (
                result.strftime("%Y-%m-%d %H:%M:%S") != "22023-02-28 18:30:00"
            )

            # falls next day
            mock_now.return_value = timezone.datetime(
                2023, 2, 28, 19, 10, 0, tzinfo=timezone.utc
            )
            result = calc_prev_billing_date(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2023-01-30 18:30:00"
            assert (
                result.strftime("%Y-%m-%d %H:%M:%S") != "22023-01-31 18:30:00"
            )

            # case 8: billing day is last day of the month and current date is 2nd last day of feb month
            activation_datetime = timezone.datetime.strptime(
                "2022-10-31 13:10:00", "%Y-%m-%d %H:%M:%S"
            ).replace(tzinfo=tz.gettz("UTC"))
            billing_day = 31
            mock_now.return_value = timezone.datetime(
                2023, 2, 27, 15, 10, 0, tzinfo=timezone.utc
            )
            result = calc_prev_billing_date(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2023-01-30 18:30:00"
            assert (
                result.strftime("%Y-%m-%d %H:%M:%S") != "22023-01-31 18:30:00"
            )

            # falls next day
            mock_now.return_value = timezone.datetime(
                2023, 2, 27, 19, 10, 0, tzinfo=timezone.utc
            )
            result = calc_prev_billing_date(
                billing_day, activation_datetime, "Asia/Kolkata"
            )
            assert result.strftime("%Y-%m-%d %H:%M:%S") == "2023-02-27 18:30:00"
            assert (
                result.strftime("%Y-%m-%d %H:%M:%S") != "22023-02-28 18:30:00"
            )

    def test_billing_day_from_activation_date(self):

        # last day of the month normal year 31 days
        activation_datetime = timezone.datetime.strptime(
            "2023-10-31 19:10:00", "%Y-%m-%d %H:%M:%S"
        ).replace(tzinfo=tz.gettz("UTC"))
        day = billing_day_from_activation_date(
            activation_datetime, "Asia/Kolkata"
        )
        assert isinstance(day, int)
        assert day == 1
        assert day != 31

        # last day of the month normal year 30 days
        activation_datetime = timezone.datetime.strptime(
            "2023-11-30 19:10:00", "%Y-%m-%d %H:%M:%S"
        ).replace(tzinfo=tz.gettz("UTC"))
        day = billing_day_from_activation_date(
            activation_datetime, "Asia/Kolkata"
        )
        assert isinstance(day, int)
        assert day == 1
        assert day != 30

        # last day of the month normal year 28 days
        activation_datetime = timezone.datetime.strptime(
            "2023-02-28 19:10:00", "%Y-%m-%d %H:%M:%S"
        ).replace(tzinfo=tz.gettz("UTC"))
        day = billing_day_from_activation_date(
            activation_datetime, "Asia/Kolkata"
        )
        assert day == 1
        assert day != 28

        # second last day of the month leap year 29 days
        activation_datetime = timezone.datetime.strptime(
            "2020-02-28 19:10:00", "%Y-%m-%d %H:%M:%S"
        ).replace(tzinfo=tz.gettz("UTC"))
        day = billing_day_from_activation_date(
            activation_datetime, "Asia/Kolkata"
        )
        assert day == 29
        assert day != 28

    def test_billing_day_from_activation_date_exception(self):
        with pytest.raises(TypeError):
            billing_day_from_activation_date(
                "2023-01-01 15:10:00", "Asia/Kolkata"
            )
