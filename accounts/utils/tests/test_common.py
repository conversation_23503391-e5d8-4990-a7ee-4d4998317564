import hashlib
from datetime import timedelta

from django.utils import timezone

from django.test import TestCase

import pytest

from accounts.utils.common import (
    duration_to_seconds,
    get_aadhaar_hash,
    seconds_to_duration,
    add_months,
)


class TestCommon(TestCase):
    def test_get_aadhaar_hash(self):
        aadhaar_no = "*********"
        expected_hash = hashlib.md5(aadhaar_no.encode()).hexdigest()
        assert get_aadhaar_hash(aadhaar_no) == expected_hash

    def test_duration_to_seconds(self):
        # test with days, hours,mins, seconds
        day = 2
        hour = 3
        minutes = 4
        seconds = 5
        duration = f"{day}d{hour}h{minutes}m{seconds}s"
        expected_seconds = day * 86400 + hour * 3600 + minutes * 60 + seconds
        assert duration_to_seconds(duration) == expected_seconds

        # test with days, hours
        day = 90
        hour = 23
        duration = f"{day}d{hour}h"
        expected_seconds = day * 86400 + hour * 3600
        assert duration_to_seconds(duration) == expected_seconds

        # test with  hours
        hour = 12
        duration = f"{hour}h"
        expected_seconds = hour * 3600
        assert duration_to_seconds(duration) == expected_seconds

        # test with invalid duration format
        with pytest.raises(ValueError):
            duration_to_seconds("2h5d")

    def test_seconds_to_duration(self):
        # test with day,hour,minutes and seconds
        seconds = 2674943
        expected_duration = "30d23h2m23s"
        assert seconds_to_duration(seconds) == expected_duration

        # test with 1 day
        seconds = 86400
        expected_duration = "1d"
        assert seconds_to_duration(seconds) == expected_duration

    def test_add_months(self):
        # test adding 1 month
        date = timezone.datetime(2023, 1, 10)
        expected_date = timezone.datetime(2023, 2, 15)
        assert add_months(date, 1, 15) == expected_date

        # test adding multiple months
        date = timezone.datetime(2023, 1, 1)
        expected_date = timezone.datetime(2023, 4, 15)
        assert add_months(date, 3, 15) == expected_date

        # test adding months across year boundary
        date = timezone.datetime(2023, 12, 15)
        expected_date = timezone.datetime(2024, 2, 15)
        assert add_months(date, 2, 15) == expected_date

        # test adding months to a date at the end of a month
        date = timezone.datetime(2023, 1, 31)
        expected_date = timezone.datetime(2023, 2, 28)
        assert add_months(date, 1, 31) == expected_date

        # test adding months to a date at the end of a month (leap year)
        date = timezone.datetime(2024, 1, 31)
        expected_date = timezone.datetime(2024, 2, 29)
        assert add_months(date, 1, 31) == expected_date
