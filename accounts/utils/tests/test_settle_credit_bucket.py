import unittest.mock as mock
from decimal import Decimal
from unittest import skip
from unittest.mock import MagicMock

from django.test import TestCase

import pytest

from accounts.billing_accounts.enums import BillingAccountCreditStatusEnum
from accounts.billing_accounts.models import BillingAccountCredits
from accounts.billing_accounts.tests.factories import (
    BillingAccountCreditsFactory,
    BillingAccountFactory,
)
from accounts.discounts.enums import (
    DiscountBucketAppliedOnEnum,
    DiscountBucketStatusEnum,
)
from accounts.discounts.tests.factories import DiscountBucketsFactory
from accounts.packages.tests.factories import PackageFactory
from accounts.services.enums import (
    OtherChargesStatusEnum,
    ServiceNumberPaidEnum,
    ServiceRentalStatusEnum,
    ServiceRentalTransactionStatusEnum,
)
from accounts.services.models import (
    OtherCharges,
    ServiceNumbers,
    ServiceRentals,
    Services,
)
from accounts.services.tests.factories import (
    OtherChargesFactory,
    ServiceFactory,
    ServiceNumberFactory,
    ServicePackageFactory,
    ServiceRentalFactory,
)
from accounts.utils.settle_credit_bucket import (
    SettleCreditBucket,
)


def mock_current_package(rent=100, renew_cycle=1):
    mock_package = MagicMock()
    mock_package.package.rent_per_month = Decimal(rent)
    mock_package.package.renew_cycle = renew_cycle
    return mock_package


class TestSettleCreditBucket(TestCase):
    def setUp(self):
        self.billing_account = BillingAccountFactory.create()
        self.service = ServiceFactory.create(
            billing_account=self.billing_account
        )
        self.service_package = ServicePackageFactory(
            service=self.service,
            package__rent_per_month=100,
            package__renew_cycle=12,
        )
        ServiceRentalFactory.create(
            service=self.service,
            rental_amount=(
                self.service_package.package.rent_per_month
                * self.service_package.package.renew_cycle
            ),
            pending_rental=0,
        )

    def test_negative_amount_raises_error(self):
        scb = SettleCreditBucket(self.billing_account)
        scb.amount = Decimal(5)
        with pytest.raises(ValueError):
            scb._decrease_amount(Decimal(10))

    def test_apply_and_consume_discounts(self):
        rental_discount = DiscountBucketsFactory(
            billing_account=self.billing_account,
            value=100,
            apply_on=DiscountBucketAppliedOnEnum.RENTAL.value,
        )
        usages_discount = DiscountBucketsFactory(
            billing_account=self.billing_account,
            value=80,
            apply_on=DiscountBucketAppliedOnEnum.USAGES.value,
        )
        all_discount = DiscountBucketsFactory(
            billing_account=self.billing_account,
            value=50,
            apply_on=DiscountBucketAppliedOnEnum.ALL.value,
        )

        scb = SettleCreditBucket(self.billing_account)
        scb.apply_and_consume_discounts()
        assert scb.discount_on_rental == Decimal("100")
        assert scb.discount_on_usage == Decimal("80")
        assert scb.discount_on_all == Decimal("50")

        rental_discount.refresh_from_db()
        usages_discount.refresh_from_db()
        all_discount.refresh_from_db()

        assert rental_discount.status == DiscountBucketStatusEnum.INACTIVE.value
        assert usages_discount.status == DiscountBucketStatusEnum.INACTIVE.value
        assert all_discount.status == DiscountBucketStatusEnum.INACTIVE.value

    def test_amount_credit_with_no_pending_dues(self):
        """
        test with credit added of 1000, no discount, rental
        """
        # payment made = 1000
        # no discount, no rental, no outstanding amount
        BillingAccountCredits.add_credit_amount(
            self.billing_account, 1000, "1000 added to credit bucket"
        )
        SettleCreditBucket(self.billing_account).process()

        billing_credit = BillingAccountCredits.objects.filter(
            billing_account=self.billing_account,
            status=BillingAccountCreditStatusEnum.ACTIVE.value,
        ).first()

        self.assertEqual(billing_credit.credit_amount, Decimal(1000))
        self.assertEqual(
            billing_credit.description,
            "final amount after settlement internally",
        )
        self.assertEqual(BillingAccountCredits.objects.count(), 2)

    def test_settle_unpaid_number_cost(self):
        service_number = ServiceNumberFactory.create(
            service=self.service,
            service_number="************",
            number_cost=100,
            is_paid=ServiceNumberPaidEnum.UNPAID.value,
        )

        BillingAccountCreditsFactory.create(
            billing_account=self.billing_account, credit_amount=300
        )
        SettleCreditBucket(self.billing_account).process()

        bac = BillingAccountCredits.objects.get(
            billing_account=self.billing_account,
            status=BillingAccountCreditStatusEnum.ACTIVE.value,
        )
        service_number.refresh_from_db()
        self.assertEqual(
            service_number.is_paid, ServiceNumberPaidEnum.PAID.value
        )
        self.assertTrue(
            BillingAccountCredits.objects.filter(
                description="100 debited for number ************"
            ).exists()
        )
        # 100 should be deducted from credit bucket
        self.assertEqual(bac.credit_amount, 200)
        self.assertEqual(
            bac.description, "final amount after settlement internally"
        )

    def test_settle_unpaid_other_charges(self):
        BillingAccountCreditsFactory.create(
            billing_account=self.billing_account, credit_amount=500
        )
        other_charge_unpaid = OtherChargesFactory.create(
            service=self.service,
            charge=200,
            status=OtherChargesStatusEnum.UNPAID.value,
            description="other charges description",
        )

        SettleCreditBucket(self.billing_account).process()

        bac = BillingAccountCredits.objects.get(
            billing_account=self.billing_account,
            status=BillingAccountCreditStatusEnum.ACTIVE.value,
        )
        other_charge_unpaid.refresh_from_db()
        self.assertEqual(
            other_charge_unpaid.status, OtherChargesStatusEnum.PAID.value
        )
        self.assertEqual(
            BillingAccountCredits.objects.all()[1].description,
            "200.000 debited for other charges description",
        )
        # 200 should be deducted from credit bucket
        self.assertEqual(bac.credit_amount, 300)
        self.assertEqual(
            bac.description, "final amount after settlement internally"
        )

    def test_apply_usage_discount_on_outstanding_amount(self):
        """
        Usage type discount should apply on outstanding amount only
        """
        pass

    def test_apply_rental_discount_on_rental(self):
        pass

    def test_apply_all_discount_on(self):
        pass

    @skip("todo: fix")
    @mock.patch("accounts.services.models.ServicePackages.objects")
    def test_rental_fully_covered_by_discount_credit_remains_unchanged(
        self, mock_service_packages
    ):
        """
        Case:
        payment made = 500
        discount on rental = 500
        rental = 500
        final credit bucket = 500
        """
        mock_service_packages.current_package.return_value = (
            mock_current_package()
        )

        billing_account = BillingAccountFactory.create()
        service = ServiceFactory.create(billing_account=billing_account)
        BillingAccountCreditsFactory.create(
            billing_account=billing_account, credit_amount=500
        )
        DiscountBucketsFactory.create(
            billing_account=billing_account,
            apply_on=DiscountBucketAppliedOnEnum.RENTAL.value,
            value=500,
        )
        Services.objects.filter(billing_account=self.billing_account).first()
        ServiceRentalFactory.create(
            service=service,
            rental_amount=0,
            pending_rental=500,
            status=ServiceRentalStatusEnum.INACTIVE.value,
        )
        scb = SettleCreditBucket(billing_account)

        scb.settle_credit_bucket()
        billing_credit = BillingAccountCredits.objects.filter(
            billing_account=billing_account,
            status=BillingAccountCreditStatusEnum.ACTIVE.value,
        ).first()
        service_rental: ServiceRentals = ServiceRentals.objects.filter(
            service=service, status=ServiceRentalStatusEnum.ACTIVE.value
        ).first()

        assert billing_credit.credit_amount == Decimal(500)
        assert service_rental.rental_amount == Decimal(500)
        assert service_rental.pending_rental == Decimal(0)

    @skip("todo: fix")
    def test_rental_partially_covered_credit_fully_utilized(self):
        """
        Case:
        payment made = 1000
        discount on all = 1000
        rental = 1000
        final credit bucket = 100
        """
        BillingAccountCreditsFactory.create(
            billing_account=self.billing_account, credit_amount=500
        )
        DiscountBucketsFactory.create(
            billing_account=self.billing_account,
            apply_on=DiscountBucketAppliedOnEnum.RENTAL.value,
            value=500,
        )
        Services.objects.filter(billing_account=self.billing_account).first()
        ServiceRentalFactory.create(
            service=self.service,
            rental_amount=0,
            pending_rental=1000,
            status=ServiceRentalStatusEnum.ACTIVE.value,
        )
        SettleCreditBucket(self.billing_account).process()
        billing_credit = BillingAccountCredits.objects.filter(
            billing_account=self.billing_account,
            status=BillingAccountCreditStatusEnum.ACTIVE.value,
        ).first()
        service_rental = ServiceRentals.objects.filter(
            service=self.service, status=ServiceRentalStatusEnum.ACTIVE.value
        ).first()
        assert billing_credit.credit_amount == Decimal(0)
        assert service_rental.rental_amount == Decimal(500)
        assert service_rental.pending_rental == Decimal(0)

    @skip("todo: fix")
    @mock.patch("accounts.services.models.ServicePackages.objects")
    def test_rental_fully_covered_with_remaining_credit_after_discount_and_payment(
        self, mock_service_packages
    ):
        """
        Case:
        payment made = 1000
        discount on all = 1000
        rental = 1000
        final credit bucket = 1000
        """
        mock_service_packages.current_package.return_value = (
            mock_current_package()
        )
        billing_account = BillingAccountFactory.create()
        service = ServiceFactory.create(billing_account=billing_account)
        BillingAccountCreditsFactory.create(
            billing_account=billing_account, credit_amount=1000
        )
        DiscountBucketsFactory.create(
            billing_account=billing_account,
            apply_on=DiscountBucketAppliedOnEnum.ALL.value,
            value=1000,
        )
        Services.objects.filter(billing_account=self.billing_account).first()
        ServiceRentalFactory.create(
            service=service,
            rental_amount=0,
            pending_rental=1000,
            status=ServiceRentalStatusEnum.INACTIVE.value,
        )
        scb = SettleCreditBucket(billing_account)

        scb.settle_credit_bucket()
        billing_credit = BillingAccountCredits.objects.filter(
            billing_account=billing_account,
            status=BillingAccountCreditStatusEnum.ACTIVE.value,
        ).first()
        service_rental = ServiceRentals.objects.filter(
            service=service, status=ServiceRentalStatusEnum.ACTIVE.value
        ).first()
        assert billing_credit.credit_amount == Decimal(1000)
        assert service_rental.rental_amount == Decimal(1000)
        assert service_rental.pending_rental == Decimal(0)

    @skip("todo: fix")
    @mock.patch("accounts.services.models.ServicePackages.objects")
    def test_credit_bucket_covers_rental_with_usage_discount_and_payment(
        self, mock_service_packages
    ):
        """
        Case:
        current_credit = -100 (pending usages)
        payment made = 0
        discount on usages = 1000
        rental = 2000
        final credit bucket = 0
        pending_rental = 1100
        """
        mock_service_packages.current_package.return_value = (
            mock_current_package()
        )
        billing_account = BillingAccountFactory.create()
        service = ServiceFactory.create(billing_account=billing_account)
        BillingAccountCreditsFactory.create(
            billing_account=billing_account, credit_amount=-100
        )
        DiscountBucketsFactory.create(
            billing_account=billing_account,
            apply_on=DiscountBucketAppliedOnEnum.USAGES.value,
            value=1000,
        )
        Services.objects.filter(billing_account=self.billing_account).first()
        ServiceRentalFactory.create(
            service=service,
            rental_amount=0,
            pending_rental=2000,
            status=ServiceRentalStatusEnum.INACTIVE.value,
        )
        scb = SettleCreditBucket(billing_account)

        scb.settle_credit_bucket()
        billing_credit = BillingAccountCredits.objects.filter(
            billing_account=billing_account,
            status=BillingAccountCreditStatusEnum.ACTIVE.value,
        ).first()
        service_rental = ServiceRentals.objects.filter(
            service=service, status=ServiceRentalStatusEnum.ACTIVE.value
        ).first()
        assert billing_credit.credit_amount == Decimal(0)
        assert service_rental.rental_amount == Decimal(900)
        assert service_rental.pending_rental == Decimal(1100)

    def test_settle_pending_rental_from_credit_bucket(self):
        BillingAccountCreditsFactory.create(
            billing_account=self.billing_account, credit_amount=1000
        )
        ServiceRentals.objects.all().delete()
        ServiceRentalFactory.create(
            service=self.service, rental_amount=0, pending_rental=500
        )

        SettleCreditBucket(self.billing_account).process()

        bac = BillingAccountCredits.objects.get(
            billing_account=self.billing_account,
            status=BillingAccountCreditStatusEnum.ACTIVE.value,
        )
        service_rental = ServiceRentals.active.first()
        assert bac.credit_amount == Decimal(500)
        assert service_rental.rental_amount == Decimal(500)
        assert service_rental.pending_rental == Decimal(0)
        self.assertTrue(
            BillingAccountCredits.objects.filter(
                description="500 debited for rental"
            ).exists()
        )
        self.assertEqual(bac.credit_amount, 500)
        self.assertEqual(
            bac.description, "final amount after settlement internally"
        )

    def test_settle_non_existing_rental_from_credit_bucket(self):
        BillingAccountCreditsFactory.create(
            billing_account=self.billing_account, credit_amount=2200
        )
        ServiceRentals.objects.all().delete()

        SettleCreditBucket(self.billing_account).process()

        bac = BillingAccountCredits.objects.get(
            billing_account=self.billing_account,
            status=BillingAccountCreditStatusEnum.ACTIVE.value,
        )
        service_rental = ServiceRentals.active.first()
        assert bac.credit_amount == Decimal(1000)
        assert service_rental.rental_amount == Decimal(1200)
        assert service_rental.pending_rental == Decimal(0)
        self.assertTrue(
            BillingAccountCredits.objects.filter(
                description="1200.000 debited for rental"
            ).exists()
        )
        self.assertEqual(bac.credit_amount, 1000)
        self.assertEqual(
            bac.description, "final amount after settlement internally"
        )

    def test_settelment_sequence(self):
        """
        1st: Settle Service Number cost
        2nd: Settle Other Charges
        3rd: Settle Rental Bucket
        Test that these order are maintained
        """
        pass
