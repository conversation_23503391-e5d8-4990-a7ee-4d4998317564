import pytest
from django.test import TestCase
import unittest.mock as mock
from django.utils import timezone
from freezegun import freeze_time
from django.conf import settings
from accounts.utils.events.sns import SnsEvent
from accounts.utils.events.exceptions import ValidationException, EventException


class TestSnsEvent(TestCase):
    @classmethod
    def sns_client(cls):
        sns_client = mock.MagicMock()
        sns_client.publish.return_value = {"MessageId": "1234"}
        return sns_client

    @classmethod
    def now(cls):
        return timezone.now()

    def test_attributes(self):
        now = self.now()
        event = SnsEvent()
        event.EVENT_ACTION = "active"
        event.service_type = "myop"
        event.arn = settings.ACCOUNTS_SNS_ARN
        expected = {
            "uid": {"StringValue": mock.ANY, "DataType": "String"},
            "version": {"StringValue": mock.ANY, "DataType": "String"},
            "namespace": {"StringValue": "accounts", "DataType": "String"},
            "action": {"StringValue": event.EVENT_ACTION, "DataType": "String"},
            "service_type": {
                "StringValue": event.service_type,
                "DataType": "String",
            },
            "etag": {"StringValue": "qwerty", "DataType": "String"},
            "created_at": {
                "StringValue": now.strftime("%Y-%m-%d %H:%M:%S"),
                "DataType": "String",
            },
        }
        with freeze_time(now):
            self.assertEqual(event.prepare_attributes(), expected)

    def test_dict_to_string_data(self):
        event = SnsEvent()
        event.data = {"company_id": 1}
        data = event.prepare_data()
        assert data == '{"company_id": 1}'

    def test_no_validation_required(self):
        event = SnsEvent()
        event.data = {}
        event.REQUIRED_PARAMS = ()
        assert event.validate() is True

    def test_validate_missing_params(self):
        event = SnsEvent()
        event.data = {}
        event.REQUIRED_PARAMS = ("company_id",)
        with pytest.raises(
            ValidationException, match="Missing required parameters: company_id"
        ):
            event.validate()

    def test_validate_success(self):
        event = SnsEvent()
        event.data = {"company_id": 1}
        event.service_type = "myop"
        event.arn = settings.ACCOUNTS_SNS_ARN
        event.REQUIRED_PARAMS = ("company_id",)
        assert event.validate() is True

    def test_send_success(self):
        sns_client = self.sns_client()
        event = SnsEvent()
        event.data = {"company_id": 1}
        event.REQUIRED_PARAMS = ("company_id",)
        event.EVENT_ACTION = "active"
        event.service_type = "myop"
        event.arn = settings.ACCOUNTS_SNS_ARN
        event.sns = sns_client
        response = event.send()
        assert response == "1234"

    def test_send_validation_error(self):
        sns_client = self.sns_client()
        event = SnsEvent()
        event.data = {}
        event.REQUIRED_PARAMS = ("company_id",)
        event.service_type = "myop"
        event.EVENT_ACTION = "active"
        event.arn = settings.ACCOUNTS_SNS_ARN
        event.sns = sns_client
        with pytest.raises(
            ValidationException, match="Missing required parameters: company_id"
        ):
            event.send()

    def test_sns_client_error(self):
        sns_client = self.sns_client()
        event = SnsEvent()
        event.data = {"company_id": 1}
        event.REQUIRED_PARAMS = ("company_id",)
        event.EVENT_ACTION = "active"
        event.arn = settings.ACCOUNTS_SNS_ARN
        event.service_type = "myop"
        event.sns = sns_client
        sns_client.publish.side_effect = EventException("Event Failed: Error")
        with pytest.raises(EventException, match="Event Failed: Error"):
            event.send()

    def test_service_type_not_defined(self):
        event = SnsEvent()
        event.data = {"company_id": 1}
        event.REQUIRED_PARAMS = ("company_id",)
        event.arn = settings.ACCOUNTS_SNS_ARN
        with pytest.raises(ValueError, match="Invalid service_type: None"):
            event.send()

    def test_arn_not_defined(self):
        event = SnsEvent()
        event.data = {"company_id": 1}
        event.REQUIRED_PARAMS = ("company_id",)
        event.service_type = "myop"
        with pytest.raises(ValueError, match="Invalid arn: None"):
            event.send()

    def test_get_data_should_return_correct_data(self):
        event_1 = SnsEvent()
        event_1.REQUIRED_PARAMS = ("company_id", "ban")
        event_1.service_type = "myop"
        event_1.set_data("company_id", 1)
        event_1.set_data("ban", "abc123")
        assert event_1.get_data() == {"company_id": 1, "ban": "abc123"}

        event_2 = SnsEvent()
        event_2.REQUIRED_PARAMS = "company_id"
        event_2.service_type = "myop"
        event_2.set_data("company_id", 2)
        assert event_2.get_data() == {"company_id": 2}
