import pytest
from unittest import mock
from accounts.utils.permissions.billing_account import BillingAccount<PERSON>ermission
from accounts.billing_accounts.tests.factories import BillingAccountFactory


@pytest.fixture
def permission():
    return BillingAccountPermission()


@pytest.fixture
def mock_request():
    return mock.Mock()


@pytest.fixture
def mock_view():
    return mock.Mock()


@pytest.mark.unittest
@pytest.mark.django_db
def test_has_permission_with_valid_billing_account(
    permission, mock_request, mock_view
):
    # Create a test billing account
    billing_account = BillingAccountFactory.create()

    # Set up the request with a valid billing_account_id
    mock_request.billing_account_id = billing_account.id

    # Test the permission
    has_permission = permission.has_permission(mock_request, mock_view)

    # Assertions
    assert has_permission is True
    assert mock_request.billing_account == billing_account


@pytest.mark.unittest
@pytest.mark.django_db
def test_has_permission_with_invalid_billing_account(
    permission, mock_request, mock_view
):
    # Set up the request with an invalid billing_account_id
    mock_request.billing_account_id = 99999  # Non-existent ID

    # Test the permission
    has_permission = permission.has_permission(mock_request, mock_view)

    # Assertions
    assert has_permission is False
    assert mock_request.billing_account is None


@pytest.mark.unittest
@pytest.mark.django_db
def test_has_permission_without_billing_account_id(
    permission, mock_request, mock_view
):
    mock_request.billing_account_id = None
    has_permission = permission.has_permission(mock_request, mock_view)

    # Assertions
    assert has_permission is False
