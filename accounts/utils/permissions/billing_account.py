from rest_framework.permissions import BasePermission
from accounts.billing_accounts.models import BillingAccounts


class BillingAccountPermission(BasePermission):
    message = "Invalid billing_account_id in headers!"

    def has_permission(self, request, view):
        if (
            hasattr(request, "billing_account_id")
            and request.billing_account_id is not None
        ):
            request.billing_account = BillingAccounts.get_by_id(
                request.billing_account_id
            )
            return request.billing_account is not None
        return False
