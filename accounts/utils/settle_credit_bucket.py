import logging
import typing as t
from decimal import Decimal
from typing import Iterable

from django.db.models import Prefetch, Q
from django.utils import timezone

from accounts.billing_accounts.models import (
    BillingAccountCredits,
    BillingAccounts,
    DiscountBuckets,
)
from accounts.discounts.enums import (
    DiscountBucketAppliedOnEnum,
    DiscountBucketStatusEnum,
)
from accounts.packages.exceptions import InvalidPackageException
from accounts.services.enums import (
    OtherChargesStatusEnum,
    ServiceNumberPaidEnum,
    ServiceRentalStatusEnum,
    ServiceStatusEnum,
)
from accounts.services.models import (
    OtherCharges,
    ServiceNumbers,
    ServicePackages,
    ServiceRentals,
    Services,
)

logger = logging.getLogger(__name__)


class SettleCreditBucket:
    def __init__(self, billing_account: BillingAccounts):
        self.billing_account = billing_account
        self.is_corporate: bool = billing_account.is_parent_ban()
        self.discount_on_usage = Decimal(0)
        self.discount_on_rental = Decimal(0)
        self.discount_on_all = Decimal(0)
        self.amount = Decimal(0)

    def fetch_services(self) -> t.Iterable[Services]:
        """
        Fetches all services for the billing account that are active or suspended.
        """
        return Services.objects.billing_account(
            self.billing_account, self.is_corporate
        )

    def _decrease_amount(self, value: Decimal):
        if self.amount - value < 0:
            raise ValueError(
                f"Amount cannot be less than 0: {self.amount} - {value}"
            )
        self.amount -= value

    def apply_and_consume_discounts(self) -> "SettleCreditBucket":

        discounts = DiscountBuckets.objects.filter(
            billing_account=self.billing_account,
            status=DiscountBucketStatusEnum.ACTIVE.value,
        )

        discount_ids = []
        for discount in discounts:
            if discount.apply_on == DiscountBucketAppliedOnEnum.RENTAL.value:
                self.discount_on_rental += discount.value
            elif discount.apply_on == DiscountBucketAppliedOnEnum.USAGES.value:
                self.discount_on_usage += discount.value
            else:
                self.discount_on_all += discount.value
            discount_ids.append(discount.id)

        if discount_ids:
            DiscountBuckets.objects.filter(id__in=discount_ids).update(
                status=DiscountBucketStatusEnum.INACTIVE.value
            )

        return self

    def settle_number_cost(self):
        """
        Settles all service number cost for the given service.
        """
        if self.amount <= 0:
            return False

        # Fetch unpaid service numbers with cost greater than 0
        service_numbers: t.Iterable[ServiceNumbers] = (
            ServiceNumbers.entries.billing_account(
                self.billing_account, self.is_corporate
            )
            .unpaid()
            .filter(number_cost__gt=0)
        )
        for service_number in service_numbers:
            if service_number.number_cost <= self.amount:
                self._decrease_amount(service_number.number_cost)
                BillingAccountCredits.deduct_credit_amount(
                    billing_account=self.billing_account,
                    amount=service_number.number_cost,
                    description=f"{service_number.number_cost} debited for number {service_number.service_number}",
                )
                service_number.mark_as_paid()
        return self

    def settle_other_charges(self):
        """
        Settles all other charges for the given service.
        """
        if self.amount <= 0:
            return False

        # Fetch unpaid other charges
        other_charges: t.Iterable[
            OtherCharges
        ] = OtherCharges.unpaid.billing_account(
            self.billing_account, self.is_corporate
        )
        for other_charge in other_charges:
            charge = other_charge.charge
            if charge > 0 and charge <= self.amount:
                self._decrease_amount(charge)
                BillingAccountCredits.deduct_credit_amount(
                    billing_account=self.billing_account,
                    amount=charge,
                    description=f"{charge} debited for {other_charge.description}",
                )
                other_charge.mark_as_paid()
            elif charge <= 0:
                other_charge.mark_as_paid()

    def _settle_rental(self, service: Services, package_rental: Decimal):
        service_rental = ServiceRentals.active.service(service.id).first()
        if service_rental and service_rental.pending_rental > 0:
            self._process_pending_rental(service, service_rental)
        elif not service_rental:
            self._process_new_rental(service, package_rental)

    def _process_pending_rental(
        self, service: Services, service_rental: ServiceRentals
    ):
        # Try to settle pending rental from discount first
        remaining_rental = self._settle_rental_from_discount(
            service, Decimal(service_rental.pending_rental), service_rental
        )
        # If still remaining, settle from credit
        if remaining_rental > 0:
            self._settle_rental_from_credit(service, remaining_rental)

    def _process_new_rental(self, service: Services, package_rental: Decimal):
        if package_rental <= 0:
            ServiceRentals.add_rental(service, package_rental, 0)
            return

        # Try to settle from discount first
        remaining_rental = self._settle_rental_from_discount(
            service, package_rental
        )

        # If still remaining, settle from credit
        if remaining_rental > 0:
            self._settle_rental_from_credit(service, remaining_rental)

    def _settle_rental_from_discount(
        self, service: Services, rental_amount: Decimal, service_rental=None
    ) -> Decimal:
        return rental_amount

    def _settle_rental_from_credit(
        self, service: Services, rental_amount: Decimal
    ):
        if self.amount <= 0:
            return
        if self.amount >= rental_amount:
            self._decrease_amount(rental_amount)
            available_rental = rental_amount
            pending_rental = 0
        else:
            available_rental = self.amount
            pending_rental = rental_amount - available_rental
            self._decrease_amount(self.amount)  # deduct whole remaining amount

        ServiceRentals.add_rental(service, available_rental, pending_rental)
        if available_rental > 0:
            BillingAccountCredits.deduct_credit_amount(
                billing_account=self.billing_account,
                amount=available_rental,
                description=f"{available_rental} debited for rental",
            )

    def settle_credit_bucket(self):
        self.apply_and_consume_discounts()

        self.amount = BillingAccountCredits.entries.billing_account(
            self.billing_account
        ).credit_amount()

        # # add all type of discount to amount
        # if self.discount_on_all > 0:
        #     self.amount += self.discount_on_all
        #     BillingAccountCredits.add_credit_amount(
        #         billing_account=self.billing_account,
        #         amount=self.discount_on_all,
        #         description=f"discount added of {self.discount_on_all}",
        #     )
        credit = BillingAccountCredits.entries.get_credit_object(
            self.billing_account
        )

        credit_balance = credit.credit_amount if credit else Decimal(0)
        # add usage type of discount to amount
        if self.discount_on_usage > 0 and credit_balance < 0:
            self.amount += self.discount_on_usage

            BillingAccountCredits.add_credit_amount(
                billing_account=self.billing_account,
                amount=self.discount_on_usage,
                description=f"discount added of {self.discount_on_usage}",
            )
        # Settle sequentially
        # 1. Settle number costs
        self.settle_number_cost()
        # 2. Settle other charges
        self.settle_other_charges()
        # 3. Settle rental for each service

        current_time = timezone.now()
        for service in self.services:
            current_package = ServicePackages.objects.current_package(
                service, current_time
            )
            if not current_package:
                raise InvalidPackageException(
                    f"No current package found for service {service.id}"
                )

            package_rental: Decimal = (
                current_package.package.rent_per_month
                * Decimal(current_package.package.renew_cycle)
            )
            if self.amount > 0 or self.discount_on_rental > 0:
                self._settle_rental(service, package_rental)

        # add a final settelment message each time
        BillingAccountCredits.add_credit_amount(
            billing_account=self.billing_account,
            amount=0,
            description="final amount after settlement internally",
        )

    def process(self):
        self.services = self.fetch_services()
        self.settle_credit_bucket()
        # self.activate_suspended_services()
        # todo: trigger an django custom event for settlement completion and activate suspended services
