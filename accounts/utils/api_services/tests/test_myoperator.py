import json
import unittest
from unittest import mock
from unittest.mock import Mock

from django.conf import settings
from django.utils import timezone

import responses

from accounts.utils.api_services.myoperator import MyOperator


class MyoperatorTest(unittest.TestCase):
    def setUp(self):
        self.service_number = "123456"
        self.gsn = "12345"
        self.api_url = settings.MYOPERATOR_API_URL
        self.country_code = "IN"

    @responses.activate
    def test_update_service_number_success(self):
        expected_dict = json.dumps(
            {
                "status": "success",
                "data_str": "some_data_string",
                "code": 200,
            }
        )
        responses.add(
            responses.POST,
            f"{self.api_url}updatednid",
            body=expected_dict,
            status=200,
        )

        result = MyOperator().update_service_number(
            self.gsn,
            self.service_number,
            live_status=1,
            number_property=1,
            expiry=timezone.now().strftime("%Y-%m-%d %H:%M:%S"),
            dnid1="2345",
            dnid2="67890",
            country_code="IN",
        )
        assert result is True

    @responses.activate
    def test_update_service_number_failure(self):
        expected_result = False
        expected_dict = json.dumps(
            {
                "status": "error",
                "data": {},
                "code": 400,
            }
        )
        responses.add(
            responses.POST,
            f"{self.api_url}updatednid",
            body=expected_dict,
            status=400,
        )

        result = MyOperator().update_service_number(
            self.gsn,
            self.service_number,
            live_status=1,
            number_property=1,
            expiry=timezone.now().strftime("%Y-%m-%d %H:%M:%S"),
            dnid1="2345",
            dnid2="67890",
            country_code="IN",
        )
        assert result == expected_result

    @responses.activate
    @mock.patch("accounts.utils.events.sns.SnsEvent.send")
    def test_update_myoperator_permissions_success(self, mock_permission_event):
        mock_permission_event.return_value = True
        expected_dict = json.dumps(
            {
                "status": "success",
                "data_str": "some_data_string",
                "code": 200,
            }
        )

        responses.add(
            responses.POST,
            f"{self.api_url}feature/on_update",
            body=expected_dict,
            status=200,
        )
        data = {"gsn": self.gsn}
        country_short_code = "IN"
        result = MyOperator().update_permissions(data, country_short_code)
        assert result is True

    @mock.patch(
        "accounts.utils.api_services.base.Base.post_urlencoded_form_data"
    )
    @mock.patch("accounts.services.events.UpdatePermissionEvent.send")
    def test_update_myoperator_permissions_failure(self, mock_event, mock_post):
        mock_response = Mock()
        mock_response.text = '{"status": "error"}'
        mock_post.return_value = mock_response

        data = {"gsn": self.gsn}
        country_short_code = "IN"
        result = MyOperator().update_permissions(data, country_short_code)
        assert result is False

    @responses.activate
    def test_deactivate_company_success(self):
        expected_result = json.dumps(
            {
                "status": "success",
                "data": {},
                "code": 200,
            }
        )
        responses.add(
            responses.POST,
            f"{self.api_url}deactivate",
            body=expected_result,
            status=200,
        )

        result = MyOperator().deactivate_company(self.gsn, self.country_code)
        assert result is True

    @responses.activate
    def test_deactivate_company_failure(self):
        expected_result = json.dumps(
            {
                "status": "error",
                "data": {},
                "code": 200,
            }
        )
        responses.add(
            responses.POST,
            f"{self.api_url}deactivate",
            body=expected_result,
            status=400,
        )

        result = MyOperator().deactivate_company(self.gsn, self.country_code)
        assert result is not True
