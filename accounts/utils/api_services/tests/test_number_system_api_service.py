import json
import unittest
from unittest import mock
from unittest.mock import Mock
from urllib.parse import urlencode

from django.conf import settings

import pytest
import responses

from accounts.utils.api_services.exceptions import (
    NumberDetailsFetchException,
    NumberNotAvailableException,
)
from accounts.utils.api_services.number_system import NumberSystem


class NumberSystemTest(unittest.TestCase):
    def setUp(self):
        self.api_url = settings.NUMBER_SYSTEM_API_URL
        self._api_token = settings.NUMBER_SYSTEM_API_TOKEN
        self.service_number = "123456"
        self.gsn = "12345"

    @responses.activate
    def test_get_number_details_success(self):
        expected_result = "some_data_string"
        expected_dict = json.dumps(
            {
                "status": "success",
                "data_str": "some_data_string",
                "code": 200,
            }
        )
        responses.add(
            responses.POST,
            f"{self.api_url}account_apis/get_number_details?api_token={self._api_token}",
            body=expected_dict,
            status=200,
        )

        result = NumberSystem().get_number_details(
            self.gsn, self.service_number
        )
        assert result == expected_result

    @responses.activate
    def test_get_number_details_failure(self):
        expected_dict = json.dumps(
            {
                "status": "error",
                "data": {},
                "code": 200,
            }
        )
        responses.add(
            responses.POST,
            f"{self.api_url}account_apis/get_number_details?api_token={self._api_token}",
            body=expected_dict,
            status=200,
        )
        with pytest.raises(NumberDetailsFetchException):
            NumberSystem().get_number_details(self.gsn, self.service_number)

    @responses.activate
    def test_get_number_details_exception(self):
        expected_dict = json.dumps(
            {
                "status": "error",
                "data": {},
                "code": 400,
            }
        )
        responses.add(
            responses.POST,
            f"{self.api_url}account_apis/get_number_details?api_token={self._api_token}",
            body=expected_dict,
            status=400,
        )
        with pytest.raises(NumberDetailsFetchException):
            NumberSystem().get_number_details("1", "heyo2233")

    @responses.activate
    def test_free_demo_number_success(self):
        expected_result = True
        expected_dict = json.dumps(
            {
                "status": "success",
                "data_str": {},
                "code": 200,
            }
        )
        responses.add(
            responses.POST,
            f"{self.api_url}account_apis/ivr_demo_free?api_token={self._api_token}",
            body=expected_dict,
            status=200,
        )

        result = NumberSystem().free_demo_number(self.gsn, self.service_number)
        assert result == expected_result

    @responses.activate
    def test_free_demo_number_failure(self):
        expected_dict = json.dumps(
            {
                "status": "error",
                "data": {},
                "code": 200,
            }
        )
        responses.add(
            responses.POST,
            f"{self.api_url}account_apis/ivr_demo_free?api_token={self._api_token}",
            body=expected_dict,
            status=400,
        )

        result = NumberSystem().free_demo_number(self.gsn, self.service_number)
        assert result is not True

    @responses.activate
    def test_deactivate_service_number_success(self):
        expected_dict = json.dumps(
            {
                "status": "success",
                "data": {},
                "code": 200,
            }
        )
        responses.add(
            responses.POST,
            f"{self.api_url}account_apis/deactivate_service_number?api_token={self._api_token}",
            body=expected_dict,
            status=200,
        )

        result = NumberSystem().deactivate_service_number(self.service_number)
        assert result is True

    @responses.activate
    def test_deactivate_service_number_failure(self):
        expected_dict = json.dumps(
            {
                "status": "error",
                "data_str": {},
                "code": 200,
            }
        )
        responses.add(
            responses.POST,
            f"{self.api_url}account_apis/deactivate_service_number?api_token={self._api_token}",
            body=expected_dict,
            status=200,
        )

        result = NumberSystem().deactivate_service_number(self.service_number)
        assert result is not True

    @responses.activate
    def test_make_number_live_success(self):
        expected_result = {"code": 200, "data_str": {}, "status": "success"}
        expected_dict = json.dumps(
            {
                "status": "success",
                "data_str": {},
                "code": 200,
            }
        )
        responses.add(
            responses.POST,
            f"{self.api_url}account_apis/make_service_live?api_token={self._api_token}",
            body=expected_dict,
            status=200,
        )

        result = NumberSystem().make_number_live(
            self.service_number,
            self.gsn,
            service_number_old="7890",
            live_status=0,
            service_id="123",
            activation_date="2022-17-10",
        )
        assert result == expected_result

    @responses.activate
    def test_make_number_live_failure(self):
        expected_dict = json.dumps(
            {
                "status": "error",
                "message": "wrong input",
                "data_str": {},
                "code": 200,
            }
        )
        responses.add(
            responses.POST,
            f"{self.api_url}account_apis/make_service_live?api_token={self._api_token}",
            body=expected_dict,
            status=200,
        )
        with pytest.raises(NumberNotAvailableException):
            NumberSystem().make_number_live(
                self.service_number,
                self.gsn,
                service_number_old="7890",
                live_status=0,
                service_id="123",
                activation_date="2022-17-10",
            )

    @mock.patch(
        "accounts.utils.api_services.base.Base.post_urlencoded_form_data"
    )
    def test_make_number_live_exception(self, mock_post):
        mock_response = Mock()
        mock_response.text = '{"status": "error","message":"bad input"}'
        mock_post.return_value = mock_response
        with pytest.raises(NumberNotAvailableException):
            NumberSystem().make_number_live(
                self.service_number,
                self.gsn,
                service_number_old="7890",
                live_status=0,
                service_id="123",
                activation_date="2022-17-10",
            )

    @responses.activate
    def test_make_number_open_success(self):
        number = "**********"
        company_id = "abc123"
        query_params = {"number": number, "company_id": company_id}

        expected_result = {"code": 200, "data_str": {}, "status": "success"}
        expected_dict = json.dumps(
            {
                "status": "success",
                "data_str": {},
                "code": 200,
            }
        )
        responses.add(
            responses.GET,
            f"{self.api_url}AccountApis/number_open?{urlencode(query_params)}",
            body=expected_dict,
            status=200,
        )

        result = NumberSystem().make_number_open(number, company_id)
        assert result == expected_result

    @responses.activate
    def test_make_number_open_failure(self):
        number = "**********"
        company_id = "abc123"
        query_params = {"number": number, "company_id": company_id}
        expected_dict = json.dumps(
            {
                "status": "error",
                "data_str": {},
                "code": 200,
            }
        )
        responses.add(
            responses.GET,
            f"{self.api_url}AccountApis/number_open?{urlencode(query_params)}",
            body=expected_dict,
            status=200,
        )

        result = NumberSystem().make_number_open(number, company_id)
        assert result is False

    @responses.activate
    def test_reserve_demo_number_success(self):
        expected_result = {"code": 200, "data_str": {}, "status": "success"}
        expected_dict = json.dumps(
            {
                "status": "success",
                "data_str": {},
                "code": 200,
            }
        )
        responses.add(
            responses.POST,
            f"{self.api_url}myop_apis/is_customer",
            body=expected_dict,
            status=200,
        )

        result = NumberSystem().reserve_demo_number(
            self.service_number, self.gsn, service_id="123"
        )
        assert result == expected_result

    @responses.activate
    def test_reserve_demo_number_failure(self):
        expected_dict = json.dumps(
            {
                "status": "error",
                "message": "wrong input",
                "data_str": {},
                "code": 200,
            }
        )
        responses.add(
            responses.POST,
            f"{self.api_url}myop_apis/is_customer",
            body=expected_dict,
            status=200,
        )
        with pytest.raises(NumberNotAvailableException):
            NumberSystem().reserve_demo_number(
                self.service_number, self.gsn, service_id="123"
            )

    @mock.patch(
        "accounts.utils.api_services.base.Base.post_urlencoded_form_data"
    )
    def test_reserve_demo_number_exception(self, mock_post):
        mock_response = Mock()
        mock_response.text = '{"status": "error","message":"bad input"}'
        mock_post.return_value = mock_response
        with pytest.raises(NumberNotAvailableException):
            NumberSystem().reserve_demo_number(
                self.service_number, self.gsn, service_id="123"
            )
