import json
import pytest
from rest_framework import status

from accounts.utils.api_services.otp import OTPApiService, BaseOtpApiService
from accounts.utils.api_services.enums import OTPVia
from accounts.utils.api_services.exceptions import OTPApiException
from accounts.exceptions import ExternalAPIException


@pytest.fixture
def otp_test_data():
    return {
        "phone_number": "**********",
        "country_code": "91",
        "otp": "1234",
    }


@pytest.mark.unittest
def test_base_otp_api_service_send_otp():
    """Test that base class raises NotImplementedError."""
    service = BaseOtpApiService()
    with pytest.raises(NotImplementedError):
        service.send_otp()


@pytest.mark.unittest
def test_otp_api_service_get_url():
    """Test URL construction."""
    service = OTPApiService()
    url = service.get_url()
    assert url.endswith("/index-v3.php")


@pytest.mark.unittest
def test_otp_api_service_send_otp_success_sms(otp_test_data, mock_smsg_api):
    """Test successful SMS OTP sending."""
    service = OTPApiService()
    res = mock_smsg_api(
        expected_response={"success": True, "message": "OTP sent successfully"}
    )

    status_code, response = service.send_otp(
        phone_number=otp_test_data["phone_number"],
        country_code=otp_test_data["country_code"],
        otp=otp_test_data["otp"],
        via=OTPVia.sms,
    )

    assert status_code == status.HTTP_200_OK
    assert response == {"success": True, "message": "OTP sent successfully"}

    # Verify request payload
    assert res.call_count == 1
    payload = json.loads(res.calls[0].request.body)
    assert payload["template_slug"] == "myoperator-otp-sms"
    assert payload["app"] == "myoperator.otp"
    assert payload["county_code"] == otp_test_data["country_code"]
    assert payload["send_to"] == otp_test_data["phone_number"]
    assert payload["params"]["otp"] == otp_test_data["otp"]


@pytest.mark.unittest
def test_otp_api_service_send_otp_success_call(otp_test_data, mock_smsg_api):
    """Test successful Call OTP sending."""
    service = OTPApiService()
    res = mock_smsg_api(
        expected_response={"success": True, "message": "OTP sent successfully"}
    )

    status_code, response = service.send_otp(
        phone_number=otp_test_data["phone_number"],
        country_code=otp_test_data["country_code"],
        otp=otp_test_data["otp"],
        via=OTPVia.call,
    )

    assert status_code == status.HTTP_200_OK
    assert response == {"success": True, "message": "OTP sent successfully"}

    assert res.call_count == 1
    # Verify request payload
    payload = json.loads(res.calls[0].request.body)
    assert payload["template_slug"] == "myoperator-otp-call"
    assert payload["app"] == "myoperator.otp_call"
    assert payload["county_code"] == otp_test_data["country_code"]
    assert payload["send_to"] == otp_test_data["phone_number"]
    assert payload["params"]["otp"] == otp_test_data["otp"]


@pytest.mark.unittest
def test_otp_api_service_send_otp_international(otp_test_data, mock_smsg_api):
    """Test international OTP sending."""
    service = OTPApiService()
    res = mock_smsg_api(
        expected_response={"success": True, "message": "OTP sent successfully"}
    )

    status_code, response = service.send_otp(
        phone_number="2025550123",
        country_code="1",
        otp=otp_test_data["otp"],
        via=OTPVia.sms,
    )

    assert status_code == status.HTTP_200_OK
    assert response == {"success": True, "message": "OTP sent successfully"}

    assert res.call_count == 1
    # Verify request payload
    payload = json.loads(res.calls[0].request.body)
    assert payload["template_slug"] == "myoperator-otp-sms"
    assert payload["app"] == "myoperator.otp.international"
    assert payload["county_code"] == "1"
    assert payload["send_to"] == "2025550123"
    assert payload["params"]["otp"] == otp_test_data["otp"]


@pytest.mark.unittest
def test_otp_api_service_send_otp_rate_limit(otp_test_data, mock_smsg_api):
    """Test rate limit handling."""
    service = OTPApiService()
    res = mock_smsg_api(
        expected_response={"success": False, "message": "Rate limit exceeded"},
        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
    )

    with pytest.raises(OTPApiException, match="OTP attempt limit exceeded"):
        service.send_otp(
            phone_number=otp_test_data["phone_number"],
            country_code=otp_test_data["country_code"],
            otp=otp_test_data["otp"],
        )
    assert res.call_count == 1


@pytest.mark.unittest
def test_otp_api_service_send_otp_api_failure(otp_test_data, mock_smsg_api):
    """Test API failure handling."""
    service = OTPApiService()
    res = mock_smsg_api(
        expected_response={"success": False, "message": "API error"},
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
    )

    with pytest.raises(OTPApiException, match="Failed to send sms OTP"):
        service.send_otp(
            phone_number=otp_test_data["phone_number"],
            country_code=otp_test_data["country_code"],
            otp=otp_test_data["otp"],
        )
    assert res.call_count == 1


@pytest.mark.unittest
def test_otp_api_service_send_otp_invalid_json(otp_test_data, mock_smsg_api):
    """Test invalid JSON response handling."""
    service = OTPApiService()
    res = mock_smsg_api(
        expected_response=json.JSONDecodeError("Invalid JSON", "test", 0),
        status_code=status.HTTP_200_OK,
    )

    with pytest.raises(OTPApiException, match="Failed to decode JSON response"):
        service.send_otp(
            phone_number=otp_test_data["phone_number"],
            country_code=otp_test_data["country_code"],
            otp=otp_test_data["otp"],
        )
    assert res.call_count == 1


@pytest.mark.unittest
def test_otp_api_service_send_otp_connection_error(
    otp_test_data, mock_smsg_api
):
    """Test connection error handling."""
    service = OTPApiService()
    res = mock_smsg_api(
        ExternalAPIException("Connection failed"),
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
    )

    with pytest.raises(OTPApiException, match="API call failed"):
        service.send_otp(
            phone_number=otp_test_data["phone_number"],
            country_code=otp_test_data["country_code"],
            otp=otp_test_data["otp"],
        )

    assert res.call_count == 1
