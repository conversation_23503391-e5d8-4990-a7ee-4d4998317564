import json
import responses
from django.test import TestCase
from rest_framework import status
import pytest

from accounts.billing_accounts.exceptions import InvalidGstNumberException
from accounts.billing_accounts.utils.gst_parser import SurepassGSTParser
from accounts.kyc.cache_handler import GstCacheHandler
from accounts.utils.api_services.surepass import SurepassApi
from accounts.kyc.exceptions import KycFileDownloadException


class TestSurepassApi(TestCase):
    def setUp(self):
        self.surepass_api = SurepassApi()
        self.gst_number = "27**********1ZV"
        self.mock_response_success = {
            "data": {
                "contact_details": {
                    "principal": {
                        "address": "123 Test Street, 400001",
                        "email": "<EMAIL>",
                        "mobile": "**********",
                        "nature_of_business": "Recipient of Goods or Services",
                    },
                    "additional": [],
                },
                "gstin": "27**********1ZV",
                "pan_number": "**********",
                "business_name": "Test Company",
                "legal_name": "Test Company",
                "gstin_status": "Active",
            },
            "status_code": 200,
            "success": True,
        }
        self.mock_response_error = {
            "success": False,
            "status_code": 400,
            "message": "Invalid GSTIN number",
        }
        self.api_url = (
            f"{self.surepass_api.HOST}/api/v1/corporate/gstin-advanced"
        )

    @responses.activate
    def test_gstin_details_success(self):
        # Setup mock response
        responses.add(
            responses.POST,
            self.api_url,
            json=self.mock_response_success,
            status=status.HTTP_200_OK,
        )

        # Call the method
        gst_detail = self.surepass_api.gstin_details(self.gst_number)

        # Verify the response
        self.assertEqual(gst_detail.gstin, "27**********1ZV")
        self.assertEqual(gst_detail.legal_name, "Test Company")
        self.assertEqual(gst_detail.address, "123 Test Street, 400001")
        self.assertEqual(gst_detail.state_code, "27")
        self.assertEqual(gst_detail.pincode, "400001")
        self.assertEqual(gst_detail.pan, "**********")

        # Verify API call
        self.assertEqual(len(responses.calls), 1)
        self.assertEqual(
            json.loads(responses.calls[0].request.body),
            {"id_number": self.gst_number},
        )

    @responses.activate
    def test_gstin_details_error(self):
        # Setup mock response
        responses.add(
            responses.POST,
            self.api_url,
            json=self.mock_response_error,
            status=status.HTTP_400_BAD_REQUEST,
        )

        # Verify exception is raised
        with self.assertRaises(InvalidGstNumberException) as context:
            self.surepass_api.gstin_details(self.gst_number)

        self.assertEqual(str(context.exception), "Invalid GSTIN number")

        # Verify API call
        self.assertEqual(len(responses.calls), 1)
        self.assertEqual(
            json.loads(responses.calls[0].request.body),
            {"id_number": self.gst_number},
        )

    @responses.activate
    def test_gstin_details_500_error(self):
        # Setup mock response for 500 error
        responses.add(
            responses.POST,
            self.api_url,
            json={
                "success": False,
                "status_code": 500,
                "message": "Internal Server Error",
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )

        # Verify exception is raised
        with self.assertRaises(InvalidGstNumberException) as context:
            self.surepass_api.gstin_details(self.gst_number)

        self.assertEqual(str(context.exception), "Internal Server Error")

        # Verify API call
        self.assertEqual(len(responses.calls), 1)
        self.assertEqual(
            json.loads(responses.calls[0].request.body),
            {"id_number": self.gst_number},
        )

    def test_gstin_details_with_cache_hit(self):
        """Test gstin_details method when cache has data."""
        # Setup cache hit
        cached_data = self.mock_response_success["data"]
        gst_cache_handler = GstCacheHandler(self.gst_number)
        gst_cache_handler.set(SurepassGSTParser(cached_data).parse())

        # Call the method
        gst_detail = self.surepass_api.gstin_details(self.gst_number)

        # Verify the response
        self.assertEqual(gst_detail.gstin, "27**********1ZV")
        self.assertEqual(gst_detail.legal_name, "Test Company")

    @responses.activate
    def test_gstin_details_cache_parse_error_fallback_to_api(self):
        """Test gstin_details method when cached data parsing fails."""
        # Setup cache hit but with invalid data that will cause parsing to fail
        cached_data = {"invalid": "data"}  # This will cause parsing to fail
        gst_cache_handler = GstCacheHandler(self.gst_number)
        gst_cache_handler.save(cached_data, None)

        # Setup mock API response
        responses.add(
            responses.POST,
            self.api_url,
            json=self.mock_response_success,
            status=status.HTTP_200_OK,
        )

        # Call the method
        gst_detail = self.surepass_api.gstin_details(self.gst_number)

        # Verify the response
        self.assertEqual(gst_detail.gstin, "27**********1ZV")
        self.assertEqual(gst_detail.legal_name, "Test Company")
        # Verify API was called as fallback
        self.assertEqual(len(responses.calls), 1)


@pytest.mark.unittest
def test_download_gst_pdf_success(mock_surepass_api_gst_pdf, load_json):
    """Test successful GST PDF download."""
    # Setup mock response using fixture
    success_response = load_json(
        "accounts/kyc/tests/fixtures/gst_pdf_success.json"
    )
    mock_surepass_api_gst_pdf(success_response)

    # Call the method
    result = SurepassApi().download_gst_pdf("27**********1ZV")

    # Verify the response
    assert result.pdf_url == success_response["data"]["pdf_report"]
    assert result.client_id == success_response["data"]["client_id"]
    assert result.gstin == success_response["data"]["gstin"]


@pytest.mark.unittest
def test_download_gst_pdf_json_decode_error(mock_surepass_api_gst_pdf):
    """Test GST PDF download with JSON decode error."""
    # Setup mock response with invalid JSON
    mock_surepass_api_gst_pdf({"invalid": "json"}, status_code=500)

    # Verify exception is raised
    with pytest.raises(KycFileDownloadException) as exc_info:
        SurepassApi().download_gst_pdf("27**********1ZV")

    assert (
        str(exc_info.value) == 'Failed to download GST PDF: {"invalid": "json"}'
    )


@pytest.mark.unittest
def test_download_gst_pdf_non_200_response(
    mock_surepass_api_gst_pdf, load_json
):
    """Test GST PDF download with non-200 response."""
    # Setup mock response using fixture
    error_response = load_json(
        "accounts/kyc/tests/fixtures/gst_pdf_invalid_gst_number.json"
    )
    mock_surepass_api_gst_pdf(error_response, status_code=400)

    # Verify exception is raised
    with pytest.raises(KycFileDownloadException) as exc_info:
        SurepassApi().download_gst_pdf("27**********1ZV")

    assert (
        str(exc_info.value)
        == f"Failed to download GST PDF: {json.dumps(error_response)}"
    )
