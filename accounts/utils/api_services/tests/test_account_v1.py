import json
from unittest.mock import Mock, patch

from django.conf import settings
from django.test import TestCase

import pytest
import responses

from accounts.exceptions import AccountApiException
from accounts.utils.api_services.account_v1 import AccountApiV1


class TestAccountApiService(TestCase):
    def setUp(self):
        self.api_url = settings.ACCOUNT_API_V1_HOST
        self.payment_url = settings.ACCOUNT_PAYMENT_GENERATION_API_URL
        self.smsg_url = settings.SMSG_API_HOST

    @responses.activate
    def test_add_credit_success(self):
        billing_account_id = "abc123"
        offer_amount = "100"
        description = "some test description"
        user_id = "123456"

        # test with success
        expected_dict = json.dumps(
            {
                "status": "success",
                "message": "credit added successfully",
                "code": "200",
            }
        )
        responses.add(
            responses.POST,
            self.api_url + "discount/credit_add",
            body=expected_dict,
            status=200,
        )
        obj = AccountApiV1()
        result = obj.add_credit(
            billing_account_id, offer_amount, description, user_id
        )
        assert result["status"] == "success"

        # test with error
        expected_dict = json.dumps(
            {
                "status": "error",
                "message": "Invalid token",
                "code": "400",
            }
        )
        responses.add(
            responses.POST,
            self.api_url + "discount/credit_add",
            body=expected_dict,
            status=400,
        )
        obj = AccountApiV1()
        result = obj.add_credit(
            billing_account_id, offer_amount, description, user_id
        )
        assert result is False

    @responses.activate
    def test_update_memcache_success(self):
        gsn = "12345"

        # test with success
        expected_dict = json.dumps(
            {
                "status": "success",
                "message": "Memcache updated successfully",
                "code": "200",
            }
        )
        responses.add(
            responses.POST,
            self.api_url + "external/update_memcache_account",
            body=expected_dict,
            status=200,
        )
        obj = AccountApiV1()
        result = obj.update_memcache(gsn)
        assert result["status"] == "success"

        # test with error
        expected_dict = json.dumps(
            {
                "status": "error",
                "message": "Invalid token",
                "code": "400",
            }
        )
        responses.add(
            responses.POST,
            self.api_url + "external/update_memcache_account",
            body=expected_dict,
            status=400,
        )
        obj = AccountApiV1()
        result = obj.update_memcache(gsn)
        assert result is False

    @responses.activate
    def test_initiate_activation_success(self):
        company_id = "123"
        payment_id = "456"

        # test with success
        expected_dict = json.dumps(
            {
                "status": "success",
                "message": "service activation initiated",
                "code": "200",
            }
        )
        responses.add(
            responses.POST,
            self.api_url + "external/initiate_activation",
            body=expected_dict,
            status=200,
        )
        obj = AccountApiV1()
        result = obj.initiate_activation(company_id, payment_id)
        assert result["status"] == "success"

    @responses.activate
    def test_initiate_activation_error(self):
        expected_dict = json.dumps(
            {
                "status": "error",
                "message": "Invalid token",
                "code": "400",
            }
        )
        responses.add(
            responses.POST,
            self.api_url + "external/initiate_activation",
            body=expected_dict,
            status=400,
        )
        result = AccountApiV1().initiate_activation("11", "abc123")
        assert result is False

    @responses.activate
    def test_change_package_pending_amount_success(self):

        expected_dict = json.dumps(
            {
                "status": "success",
                "message": "package change pending amount",
                "code": "200",
            }
        )
        responses.add(
            responses.POST,
            self.api_url + "external/change_package_pending_amount",
            body=expected_dict,
            status=200,
        )
        result = AccountApiV1().change_package_pending_amount(
            "12345", "78956", "2"
        )
        assert result["status"] == "success"

    @responses.activate
    def test_change_package_pending_amount_failure(self):

        expected_dict = json.dumps(
            {
                "status": "error",
                "message": "Invalid token",
                "code": "400",
            }
        )
        responses.add(
            responses.POST,
            self.api_url + "external/change_package_pending_amount",
            body=expected_dict,
            status=400,
        )
        result = AccountApiV1().change_package_pending_amount(
            "18991", "56123", "2"
        )
        assert result is False

    @responses.activate
    def test_change_package_success(self):

        expected_dict = json.dumps(
            {
                "status": "success",
                "message": "package upgrade successfully",
                "code": "200",
            }
        )
        responses.add(
            responses.POST,
            self.api_url + "external/change_package",
            body=expected_dict,
            status=200,
        )
        result = AccountApiV1().change_package("12345", "78956", "2")
        assert result["status"] == "success"

    @responses.activate
    def test_change_package_failure(self):

        expected_dict = json.dumps(
            {
                "status": "error",
                "message": "Invalid token",
                "code": "400",
            }
        )
        responses.add(
            responses.POST,
            self.api_url + "external/change_package",
            body=expected_dict,
            status=400,
        )
        result = AccountApiV1().change_package("18991", "56123", "2")
        assert result is False

    @responses.activate
    def test_generate_recharge_link_success(self):
        pay_data = {
            "billing_account_id": "123",
            "amount": 100,
        }

        expected_dict = json.dumps(
            {
                "status": "success",
                "message": "Recharge link generated",
                "data": {"url": "http://example.com/recharge"},
            }
        )

        responses.add(
            responses.POST,
            self.payment_url,
            body=expected_dict,
            status=200,
        )

        result = AccountApiV1().generate_recharge_link(pay_data)

        self.assertEqual(result, "http://example.com/recharge")

    @responses.activate
    def test_generate_recharge_link_error(self):
        pay_data = {
            "billing_account_id": "123",
            "amount": 100,
        }

        expected_dict = json.dumps(
            {
                "status": "error",
                "message": "Failed to generate link",
                "code": "400",
            }
        )

        responses.add(
            responses.POST,
            self.payment_url,
            body=expected_dict,
            status=400,
        )

        result = AccountApiV1().generate_recharge_link(pay_data)

        self.assertFalse(result)

    @responses.activate
    def test_account_suspension_success(self):
        data = {"account_id": "12345", "reason": "Violation of terms"}

        expected_dict = json.dumps(
            {
                "status": "success",
                "message": "Account suspended",
            }
        )

        responses.add(
            responses.POST,
            self.api_url + "account/suspend",
            body=expected_dict,
            status=200,
        )

        result = AccountApiV1().account_suspension(
            self.api_url + "account/suspend", data
        )

        self.assertEqual(result["status"], "success")

    @responses.activate
    def test_account_suspension_error(self):
        data = {"account_id": "12345", "reason": "Violation of terms"}

        expected_dict = json.dumps(
            {
                "status": "error",
                "message": "Suspension failed",
            }
        )

        responses.add(
            responses.POST,
            self.api_url + "account/suspend",
            body=expected_dict,
            status=400,
        )

        result = AccountApiV1().account_suspension(
            self.api_url + "account/suspend", data
        )

        self.assertFalse(result)

    @responses.activate
    def test_auto_deduct_success(self):
        billing_account_id = "12345"
        amount = 50

        expected_dict = json.dumps(
            {
                "status": "success",
                "message": "Amount deducted successfully",
            }
        )

        responses.add(
            responses.POST,
            self.api_url + "auto_deduct_amount",
            body=expected_dict,
            status=200,
        )

        result = AccountApiV1().auto_deduct(billing_account_id, amount)

        self.assertEqual(result["status"], "success")

    @responses.activate
    def test_auto_deduct_error(self):
        billing_account_id = "12345"
        amount = 50

        expected_dict = json.dumps(
            {
                "status": "error",
                "message": "Auto deduction failed",
            }
        )

        responses.add(
            responses.POST,
            self.api_url + "auto_deduct_amount",
            body=expected_dict,
            status=400,
        )

        result = AccountApiV1().auto_deduct(billing_account_id, amount)

        self.assertEqual(result["status"], "error")

    @responses.activate
    def test_send_sms_success(self):
        data = {"phone_number": "**********", "message": "Test message"}

        expected_dict = json.dumps(
            {
                "status": "success",
                "message": "SMS sent successfully",
            }
        )

        responses.add(
            responses.POST,
            self.smsg_url + "index-v3.php",
            body=expected_dict,
            status=200,
        )

        result = AccountApiV1().send_sms(data)

        self.assertTrue(result)

    @responses.activate
    def test_send_sms_error(self):
        data = {"phone_number": "**********", "message": "Test message"}

        expected_dict = json.dumps(
            {
                "status": "error",
                "message": "Failed to send SMS",
            }
        )

        responses.add(
            responses.POST,
            self.smsg_url + "index-v3.php",
            body=expected_dict,
            status=400,
        )

        result = AccountApiV1().send_sms(data)

        self.assertFalse(result)
