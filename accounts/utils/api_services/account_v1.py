import json
import logging
from hashlib import md5
from django.conf import settings
from django.utils import timezone
from accounts.utils.api_services.base import Base
from accounts.utils.api_services.exceptions import PayableAmountException

logger = logging.getLogger(__name__)


class AccountApiV1(Base):
    def __init__(self):
        Base.__init__(self)
        self.HOST = settings.ACCOUNT_API_V1_HOST

    def _token(self):
        token_str = "{}$${}".format(
            timezone.now().strftime("%Y-%m-%d"),
            settings.ACCOUNT_API_V1_SALT,
        )
        return md5(token_str.encode()).hexdigest()

    def _prepare_data(self, data):
        return {
            "token": self._token(),
            "data": json.dumps(data),
        }

    def add_credit(self, billing_account_id, amount, description, user_id):
        url = f"{self.HOST}discount/credit_add"
        data = self._prepare_data(
            {
                "billing_account_id": billing_account_id,
                "amount": amount,
                "description": description,
                "user_id": user_id,
            }
        )
        response = self.post_urlencoded_form_data(
            url, data=data, timeout=settings.API_TIMEOUT
        )
        result = json.loads(response.text)
        if result["status"] == "success":
            return result
        logger.title("Account add credit api failed ").critical(result)
        return False

    def update_memcache(self, gsn):
        url = f"{self.HOST}external/update_memcache_account"
        data = self._prepare_data(
            {
                "gsn": gsn,
            }
        )
        response = self.post_urlencoded_form_data(
            url, data=data, timeout=settings.API_TIMEOUT
        )
        result = json.loads(response.text)
        if result["status"] == "success":
            return result

        logger.title("update memcache api failed ").critical(result)
        return False

    def payable_amount(self, gsn, package_id):
        url = f"{self.HOST}external/payable_amount_kyc"
        data = self._prepare_data(
            {
                "gsn": gsn,
                "package_id": package_id,
            }
        )
        response = self.post_urlencoded_form_data(
            url, data=data, timeout=settings.API_TIMEOUT
        )
        result = json.loads(response.text)
        if result["status"] != "success":
            logger.title("fetch payable amount api error").error(result)
            raise PayableAmountException("unable to fetch payable amount")
        return result["data"]

    def initiate_activation(self, company_id, payment_id):
        url = f"{self.HOST}external/initiate_activation"
        data = self._prepare_data(
            {
                "company_id": company_id,
                "payment_id": payment_id,
                "number_booked": "",
                "recharge_code": "",
                "ip": "",
            }
        )
        response = self.post_urlencoded_form_data(
            url, data=data, timeout=settings.API_TIMEOUT
        )
        result = json.loads(response.text)
        if result["status"] == "success":
            return result

        logger.title("Service activation api failed ").error(result)
        return False

    def change_package_pending_amount(self, package_id, gsn, mode):
        url = f"{self.HOST}external/change_package_pending_amount"
        data = self._prepare_data(
            {
                "gsn": gsn,
                "mode": mode,
                "package_id": package_id,
            }
        )
        response = self.post_urlencoded_form_data(
            url, data=data, timeout=settings.API_TIMEOUT
        )
        result = json.loads(response.text)
        if result["status"] == "success":
            return result
        logger.title("Change package pending amount api failed ").error(result)
        return False

    def change_package(self, package_id, gsn, mode):
        url = f"{self.HOST}external/change_package"
        data = self._prepare_data(
            {
                "gsn": gsn,
                "mode": mode,
                "package_id": package_id,
            }
        )
        response = self.post_urlencoded_form_data(
            url, data=data, timeout=settings.API_TIMEOUT
        )
        result = json.loads(response.text)
        if result["status"] == "success":
            return result
        logger.title("Change package api failed ").error(result)
        return False

    def generate_recharge_link(self, pay_data):
        url = settings.ACCOUNT_PAYMENT_GENERATION_API_URL
        response = self.post_json(
            url, data=json.dumps(pay_data), timeout=settings.API_TIMEOUT
        )
        result = json.loads(response.text)
        if result["status"] == "success":
            return result["data"]["url"]

        logger.title("recharge link generation failed ").critical(result)
        return False

    def account_suspension(self, url, data):
        response = self.post_urlencoded_form_data(
            url, data=data, timeout=settings.API_TIMEOUT
        )
        result = json.loads(response.text)
        if result["status"] == "success":
            return result

        logger.title("suspend normal case failed ").critical(result)
        return False

    def auto_deduct(self, billing_account_id, amount):
        url = f"{self.HOST}auto_deduct_amount"
        data = self._prepare_data(
            {
                "billing_account_id": billing_account_id,
                "amount": amount,
            }
        )
        response = self.post_urlencoded_form_data(
            url, data=data, timeout=settings.API_TIMEOUT
        )
        result = json.loads(response.text)
        if result["status"] != "success":
            logger.title("auto deduct api failed ").critical(result)
        return result

    def send_sms(self, data):
        url = settings.SMSG_API_HOST + "index-v3.php"
        res = self.post_json(url, data=data, timeout=settings.API_TIMEOUT)
        result = json.loads(res.text)
        if res.status_code == 200:
            return True
        logger.title("send sms api failed ").critical(result)
        return False
