import logging
from accounts.billing_accounts.utils.gst_parser import GSTDetail
from accounts.utils.api_services.surepass import Surepass<PERSON><PERSON>
from accounts.exceptions import ExternalApiDependencyFailedException

from accounts.billing_accounts.exceptions import InvalidGstNumberException

logger = logging.getLogger(__name__)


def get_gst_data(gst_number: str) -> GSTDetail:
    try:
        gst_data = SurepassApi().gstin_details(gst_number)
        if not gst_data.is_active():
            logger.info(f"Inactive GST number: {gst_number}")
            raise ExternalApiDependencyFailedException(
                "Invalid or inactive GST number. Please check and try again"
            )
        return gst_data
    except InvalidGstNumberException as e:
        logger.info(f"Invalid GST number: {e} ", exc_info=True)
        raise ExternalApiDependencyFailedException(
            "Invalid or inactive GST number. Please check and try again",
            errors=str(e),
        ) from e
