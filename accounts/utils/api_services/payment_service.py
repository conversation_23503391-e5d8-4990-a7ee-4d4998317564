import json
import logging

from django.conf import settings

from accounts.utils.api_services.base import Base
from accounts.utils.api_services.exceptions import (
    PaymentLinkGenerateException,
    CancelSubscriptionException,
)

logger = logging.getLogger(__name__)


class PaymentService(Base):
    API_URL = settings.ACCOUNTS_PAYMENT_API

    def _prepare_data(self, data: dict) -> dict:
        return json.dumps(data)

    def generate_payment_url(
        self,
        billing_account_id: str,
        ac_number: str,
        amount: float,
        country_id: str,
        state_id: str,
        request_of: str,
        name: str,
        email: str,
        phone: str,
        **kwargs,
    ) -> dict:
        url = f"{self.API_URL}recharge-link"
        payload = self._prepare_data(
            {
                "billing_account_id": billing_account_id,
                "ban_no": ac_number,
                "amount": float(amount),
                "country": country_id,
                "state_id": state_id,
                "request_of": request_of,
                "name": name,
                "email": email,
                "phone": phone,
                "recharge_type": kwargs.get("recharge_type", "fix"),
                "package_id": kwargs.get("package_id"),
                "service_number": kwargs.get("service_number"),
                "number_cost": kwargs.get("number_cost"),
                "number_type": kwargs.get("number_type"),
                "response_url": kwargs.get("response_url"),
                "payment_method": kwargs.get("payment_method"),
            }
        )
        response = self.post_json(
            url, data=payload, timeout=settings.API_TIMEOUT
        )
        result = json.loads(response.text)
        if result["status"] != "success":
            raise PaymentLinkGenerateException("unable to generate payment url")
        return result["data"]

    def cancel_subscription(
        self,
        billing_account_id: str,
    ):
        url = f"{self.API_URL}cancel-subscriptions"
        payload = self._prepare_data({"billing_account_id": billing_account_id})
        response = self.post_json(
            url, data=payload, timeout=settings.API_TIMEOUT
        )
        result = json.loads(response.text)
        if result["status"] != "success":
            raise CancelSubscriptionException("unable to cancel subscription")
        return result
