import json
import logging
from django.conf import settings
from accounts.utils.api_services.base import Base
from accounts.exceptions import Chat<PERSON>IException, ExternalAPIException
from typing import Any, Dict

logger = logging.getLogger(__name__)


class ChatApi(Base):
    def __init__(self) -> None:
        super().__init__()
        self.HOST: str = settings.CHAT_API_CONFIG["HOST"]
        self.TIMEOUT: int = settings.CHAT_API_CONFIG["TIMEOUT"]

    def get_campaign_detail(self, campaign_id: str) -> Dict[str, Any]:
        url = f"{self.HOST}campaigns/{campaign_id}"
        params = {
            "include": "billing",
        }
        response = self.get(url, params, timeout=self.TIMEOUT)
        result = json.loads(response.text)
        if result["status"] != "success":
            logger.title("Campaign get_details api error").error(result)
            raise ChatAPIException("unable to fetch campaign details")
        return result["data"]
