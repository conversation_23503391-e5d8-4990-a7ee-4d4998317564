import json
import logging
from django.conf import settings
import requests
from accounts.utils.api_services.base import Base

logger = logging.getLogger(__name__)


class Memcache(Base):
    def _prepare_data(self, data):
        return {"data": json.dumps(data)}

    def update_multiple(self, company_id, details, country_short_code):
        if not isinstance(details, list):
            return False

        for detail in details:
            if not all(key in detail for key in ["key", "value"]):
                return False

        if country_short_code == "IN":
            url = settings.MEMCACHE_API_URL
        else:
            url = settings.MEMCACHE_API_URL_US

        payload = self._prepare_data(
            {
                "company_id": company_id,
                "details": details,
            }
        )
        response = requests.post(url + "memcache/multi_update", data=payload)
        result = json.loads(response.text)
        if result["status"] != "success":
            logger.title("update_multiple api error").error(result)
            return False
        return True
