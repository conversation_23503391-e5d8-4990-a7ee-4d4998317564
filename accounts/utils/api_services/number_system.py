import datetime
import json
import logging
from django.conf import settings
from accounts.utils.api_services.base import Base
from accounts.utils.api_services.exceptions import (
    NumberDetailsFetchException,
    NumberNotAvailableException,
    NumberBookingFailedException,
)


logger = logging.getLogger(__name__)


class NumberSystem(Base):
    def __init__(self):
        super().__init__()
        self.HOST = settings.NUMBER_SYSTEM_API_URL
        self.TOKEN = settings.NUMBER_SYSTEM_API_TOKEN
        self.TIMEOUT = settings.NUMBER_SYSTEM_API_TIMEOUT

    def _prepare_data(self, data):
        return {"data_str": json.dumps(data)}

    def get_number_details(self, gsn, service_number):
        url = (
            f"{self.HOST}account_apis/get_number_details?api_token={self.TOKEN}"
        )
        payload = self._prepare_data(
            {
                "service_number": service_number,
                "company_id_myop": gsn,
            }
        )
        response = self.post_urlencoded_form_data(
            url, data=payload, timeout=self.TIMEOUT
        )
        result = json.loads(response.text)
        if result["status"] != "success":
            logger.title("get_number_details api error").error(result)
            raise NumberDetailsFetchException(
                "unable to fetch number details from number system"
            )
        return result["data_str"]

    def free_demo_number(self, gsn, service_number):
        url = f"{self.HOST}account_apis/ivr_demo_free?api_token={self.TOKEN}"
        payload = self._prepare_data(
            {
                "service_number": service_number,
                "company_id_myop": gsn,
            }
        )
        response = self.post_urlencoded_form_data(
            url, data=payload, timeout=self.TIMEOUT
        )
        result = json.loads(response.text)
        if result["status"] != "success":
            logger.title("free_demo_number api error").error(result)
            return False
        return True

    def deactivate_service_number(self, display_number: str) -> bool:
        """
        Deactivates a service number.

        Args:
            display_number (str): The display number of the service to be deactivated.

        Returns:
            bool: True if the deactivation was successful, False otherwise.
        """
        url = f"{self.HOST}account_apis/deactivate_service_number?api_token={self.TOKEN}"
        payload = {"display_number": display_number}
        response = self.post_urlencoded_form_data(
            url, data=payload, timeout=self.TIMEOUT
        )
        result = json.loads(response.text)
        if result["status"] != "success":
            logger.title("deactivate_service_number api error").error(result)
            return False
        return True

    def make_number_live(self, service_number, gsn, **kwargs):
        url = (
            f"{self.HOST}account_apis/make_service_live?api_token={self.TOKEN}"
        )
        payload = self._prepare_data(
            {
                "service_number_new": service_number,
                "service_number_old": kwargs["service_number_old"],
                "live_status": kwargs["live_status"],
                "service_id_account": kwargs["service_id"],
                "company_id_myop": gsn,
                "activation_date": kwargs["activation_date"],
            }
        )
        response = self.post_urlencoded_form_data(
            url, data=payload, timeout=self.TIMEOUT
        )
        result = json.loads(response.text)
        if result["status"] != "success":
            logger.title("make_number_live api error").error(result)
            raise NumberNotAvailableException(
                f"number system error: {result['message']}"
            )
        return result

    def make_number_open(self, number, company_id):
        url = f"{self.HOST}AccountApis/number_open"
        params = {
            "number": number,
            "company_id": company_id,
        }
        response = self.get(url, params, timeout=self.TIMEOUT)
        result = json.loads(response.text)
        if result["status"] == "success":
            return result
        logger.title("make_number_open api error").error(result)
        return False

    def reserve_demo_number(
        self, display_no, company_id, service_id, account_type="3"
    ):
        url = f"{self.HOST}myop_apis/is_customer"
        payload = {
            "key": "lead@@track",
            "display_no": display_no,
            "is_customer": "1",
            "company_id": company_id,
            "service_id": service_id,
            "customer_type": account_type,
        }
        response = self.post_urlencoded_form_data(
            url, data=payload, timeout=settings.API_TIMEOUT
        )
        result = json.loads(response.text)
        if result["status"] != "success":
            logger.title("reserve_demo_number api error").error(result)
            raise NumberNotAvailableException(
                f"number system error: {result['message']}"
            )
        return result

    def fetch_demo_number(
        self,
        c_short_code,
        package_for,
        number_type,
        number_cost=0,
        state_id=None,
    ):
        url = (
            f"{self.HOST}myop_apis/fetch_number_and_did?api_token={self.TOKEN}"
        )
        payload = {
            "c_short_code": c_short_code,
            "packge_for": package_for,
            "number_type": number_type,
            "state_id": state_id,
            "number_cost": number_cost,  # fetch only free numbers
        }
        response = self.post_urlencoded_form_data(
            url, data=payload, timeout=self.TIMEOUT
        )
        result = json.loads(response.text)
        if result["status"] != "success":
            logger.title("reserve_demo_number api error").error(result)
            raise NumberNotAvailableException(
                f"number system error: {result['message']}"
            )
        return result
