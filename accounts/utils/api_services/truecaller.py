import json
import logging
from django.conf import settings
from accounts.utils.api_services.base import Base
from accounts.utils.api_services.exceptions import (
    TruecallerApiException,
)


logger = logging.getLogger(__name__)


class Truecaller(Base):
    def __init__(self):
        super().__init__()
        self.HOST = settings.TREUCALLER_API_HOST
        self.TIMEOUT = settings.API_TIMEOUT

    def deactivate_feature(self, did: str, company_id: str, **kwargs) -> bool:
        url = f"{self.HOST}phonenumbers/{did}/cancel"
        payload = {
            "deactivation_date": kwargs["deactivation_date"].strftime(
                "%Y-%m-%d %H:%M:%S"
            ),
            "reason": kwargs["reason"],
        }
        self.set_header("X-MYOP-COMPANY-ID", company_id)
        response = self.post_json(
            url, data=json.dumps(payload), timeout=self.TIMEOUT
        )
        result = json.loads(response.text)
        if result["status"] != "success":
            logger.title("deactivate_feature api error").error(result)
            raise TruecallerApiException(
                f"Truecaller API error: {result['message']}"
            )
        return True
