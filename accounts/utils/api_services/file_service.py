import logging
import typing as t

import requests

from accounts.exceptions import ExternalAPIException
from accounts.utils.api_services.base import Base

logger = logging.getLogger(__name__)


class FileService(Base):
    def get_file_from_url(
        self, url: str, timeout: t.Optional[int] = None
    ) -> requests.Response:
        """Fetches a file from the given URL.

        Args:
            url (str): The URL to fetch the file from.
            timeout (t.Optional[int], optional): The timeout for the request in seconds. Defaults to None.

        Returns:
            requests.Response: The response object containing the file content.
        Raises:
            requests.HTTPError: If the HTTP request fails.
        """

        timeout = timeout or self.DEFAULT_READ_TIMEOUT

        try:
            response = self.get(
                url=url, params={}, timeout=timeout, raise_for_status=True
            )
        except ExternalAPIException as e:
            logger.critical(
                f"Failed to download file from URL: {url} with error: {e}",
                exc_info=True,
            )
            raise ExternalAPIException(e) from e

        return response
