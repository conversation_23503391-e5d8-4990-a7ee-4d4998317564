from rest_framework import status

from accounts.exceptions import BaseException


class NumberSystemException(BaseException):
    message = "Number system error"


class NumberNotAvailableException(NumberSystemException):
    message = "Number system number not available"


class NumberDetailsFetchException(NumberSystemException):
    message = "Number system number details fetch failed"


class NumberBookingFailedException(NumberSystemException):
    message = "Number system number booking failed"


class TruecallerApiException(BaseException):
    message = ""


class NumberFeatureActivationException(BaseException):
    message = ""


class PaymentLinkGenerateException(BaseException):
    message = ""


class PayableAmountException(BaseException):
    message = ""


class CampaignException(BaseException):
    message = ""


class CancelSubscriptionException(BaseException):
    message = ""


class BubbleApiException(BaseException):
    message = ""


class OTPApiException(BaseException):
    message = ""
    http_status_code = status.HTTP_424_FAILED_DEPENDENCY
