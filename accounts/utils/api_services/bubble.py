import requests
import logging
from accounts.utils.api_services.base import Base
from django.conf import settings
import json
from accounts.utils.api_services.exceptions import BubbleApiException
from typing import Dict, Any

logger = logging.getLogger(__name__)


class BubbleApiService(Base):
    def __init__(self, api_host: str) -> None:
        super().__init__()
        self.HOST = api_host
        self.TIMEOUT = settings.API_TIMEOUT

    def sync_contact(self, data: Dict[str, Any]) -> bool:
        url = f"{self.HOST}api/1.1/wf/crm_contact"

        """Sends a notification to the Bubble API when a new contact is added/updated."""

        try:
            response = self.post_json(
                url, data=json.dumps(data), timeout=self.TIMEOUT
            )
            result = json.loads(response.text)
            if result["status"] != "success":
                logger.title("Bubble API error").error(result)
                raise BubbleApiException(
                    f"Bubble API error: {result['message']}"
                )
            return True
        except requests.RequestException as e:
            logger.error(f"Bubble API call failed: {str(e)}", exc_info=True)
            return False
