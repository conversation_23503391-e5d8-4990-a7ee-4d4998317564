import json
import logging
import uuid
from abc import ABC

from django.conf import settings

import requests

from accounts import __version__
from accounts.exceptions import ExternalAPIException

logger = logging.getLogger(__name__)


class Base(ABC):
    CONNECTION_TIMEOUT = 5
    DEFAULT_READ_TIMEOUT = 10

    def __init__(self):
        self.HEADERS = {}

    def set_header(self, key, value):
        self.HEADERS[key] = value

    def headers(self):
        return self.HEADERS

    def log_request(self, res, title=None):
        if title is None:
            title = f"{self.__class__.__qualname__}"

        logger.title(f"{title} Request").info(
            {
                "method": res.request.method,
                "url": res.request.url,
                "body": res.request.body,
                "headers": res.request.headers,
            }
        )
        logger.title(f"{title} Response").info(
            {
                "status_code": res.status_code,
                "content": res.content,
                "headers": res.headers,
                "time_elapsed": res.elapsed.total_seconds(),
            }
        )

    def set_default_headers(self):
        if "User-Agent" not in self.HEADERS:
            user_agent = f"Account-API-Python/{__version__}"
            self.set_header("User-Agent", user_agent)

        if "X-MYOP-TRACE-ID" not in self.HEADERS:
            self.set_header("X-MYOP-TRACE-ID", self.get_myop_trace_id())

        return self

    def get_myop_trace_id(self):
        try:
            from myoperator.centrallog import config

            if config.get_config() is not None:
                return str(config.get_config().uid)
            else:
                return str(uuid.uuid4())
        except ImportError:
            return str(uuid.uuid4())

    def request(self, method, url, raise_for_status=False, **options):
        self.set_default_headers()
        if "timeout" not in options:
            timeout = self.DEFAULT_READ_TIMEOUT
        else:
            timeout = options["timeout"]

        options["headers"] = self.headers()

        options["timeout"] = (
            self.CONNECTION_TIMEOUT,
            timeout,
        )

        try:
            response = getattr(requests, method.lower())(url, **options)
            self.log_request(response)
            if raise_for_status:
                response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            raise ExternalAPIException(e)

    def post_json(self, url, data, timeout=None):
        self.set_header("Content-Type", "application/json")
        response = self.request("post", url=url, data=data, timeout=timeout)
        return response

    def post_form_data(self, url, data, timeout=None):
        self.set_header("Content-Type", "application/form-data")
        response = self.request("post", url=url, data=data, timeout=timeout)
        return response

    def post_urlencoded_form_data(self, url, data, timeout=None):
        self.set_header("Content-Type", "application/x-www-form-urlencoded")

        response = self.request("post", url=url, data=data, timeout=timeout)
        return response

    def post_multipart_form_data(self, url, files, data=None, timeout=None):
        """
        Sends multipart/form-data request with file(s).

        Args:
            url (str): The URL to send the request to.
            files (dict): File dictionary in format: {'field_name': (filename, fileobj, content_type)}
            data (dict): Optional additional form fields.
            timeout (float|tuple): Request timeout.

        Returns:
            Response: requests.Response object.
        """
        # Do NOT set Content-Type manually — requests will do it automatically for multipart
        headers = self.headers()  # custom headers without forcing Content-Type
        return self.request(
            "post",
            url=url,
            files=files,
            data=data,
            timeout=timeout,
            headers=headers,
        )

    def get(self, url, params, timeout, raise_for_status=False):
        response = self.request(
            "get",
            url=url,
            params=params,
            timeout=timeout,
            raise_for_status=raise_for_status,
        )
        return response


class SurepassBase(Base):
    def __init__(self):
        super().__init__()
        self.HOST = settings.SUREPASS_API_CONFIG["HOST"].rstrip("/")
        self.TIMEOUT = settings.SUREPASS_API_CONFIG["TIMEOUT"]
        self.set_header("Authorization", f"Bearer {self._token()}")

    def _token(self):
        return settings.SUREPASS_API_CONFIG["TOKEN"]
