import hashlib
import json
import logging
from django.conf import settings
from django.utils import timezone
from accounts.utils.api_services.base import Base

logger = logging.getLogger(__name__)


class MyOperator(Base):
    def _token(self):
        token = hashlib.md5(
            (
                timezone.now().strftime("%Y-%m-%d")
                + settings.MYOPERATOR_API_SALT
            ).encode("utf-8")
        ).hexdigest()
        return token

    def _prepare_data(self, data):
        return {"data": json.dumps(data)}

    def update_service_number(self, gsn, service_number, **kwargs):
        if kwargs["country_code"] == "IN":
            url = f"{settings.MYOPERATOR_API_URL}updatednid"
        else:
            url = f"{settings.MYOPERATOR_API_URL_US}updatednid"

        if kwargs["number_property"] is None or kwargs["number_property"] == "":
            number_type = 1
        else:
            number_type = kwargs["number_property"]

        payload = self._prepare_data(
            {
                "company_id": gsn,
                "display_number": service_number,
                "account_type": kwargs["live_status"],
                "number_type": number_type,
                "expiry": kwargs["expiry"],
                "destination1": kwargs["dnid1"],
                "destination2": kwargs["dnid2"],
                "status": kwargs.get("status", 1),
            }
        )
        response = self.post_urlencoded_form_data(
            url, data=payload, timeout=settings.API_TIMEOUT
        )
        result = json.loads(response.text)
        if result["status"] != "success":
            logger.title("update_service_number error").error(result)
            return False
        return True

    def update_permissions(self, data, country_short_code):
        if country_short_code == "IN":
            url = f"{settings.MYOPERATOR_API_URL}feature/on_update"
        else:
            url = f"{settings.MYOPERATOR_API_URL_US}feature/on_update"

        payload = self._prepare_data(
            {"company_id": data["gsn"], "token": self._token()}
        )
        response = self.post_urlencoded_form_data(
            url, data=payload, timeout=settings.API_TIMEOUT
        )
        result = json.loads(response.text)
        if result["status"] != "success":
            logger.title("update_permissions error").error(result)
            return False
        return True

    def deactivate_company(self, gsn, country_code):
        if country_code == "IN":
            url = f"{settings.MYOPERATOR_API_URL}deactivate"
        else:
            url = f"{settings.MYOPERATOR_API_URL_US}deactivate"

        payload = self._prepare_data({"company_id": gsn, "type": "deactivate"})
        response = self.post_urlencoded_form_data(
            url, data=payload, timeout=settings.API_TIMEOUT
        )
        result = json.loads(response.text)
        if result["status"] == "success":
            return True
        logger.title("Deactivate Myoperator Failed").error(
            f"GSN: {gsn}, Response: {result}"
        )
        return False
