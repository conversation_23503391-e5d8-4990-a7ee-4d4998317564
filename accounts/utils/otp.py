import logging
import random
from typing import Optional, <PERSON><PERSON>

import phonenumbers

from accounts.enums import BaseEnum
from accounts.utils.api_services.exceptions import OTPApiException
from accounts.utils.cache_handler import BaseOtpCacheHandler
from accounts.exceptions import OTPException, OTPMaxAttemptException
from accounts.utils.api_services.otp import BaseOtpApiService


logger = logging.getLogger(__name__)


class CountryCodeEnum(BaseEnum):
    IN = "IN"


class BaseOtpHandler:
    def __init__(self):
        self._otp_cache_handler: Optional[BaseOtpCacheHandler] = None
        self._api_service: Optional[BaseOtpApiService] = None

    # ------------ static methods ----------
    @staticmethod
    def generate_otp() -> str:
        # Generate a random 4-digit number
        random_number = random.randint(1000, 9999)
        return str(random_number)

    # ------------ properties(getters & setters) ----------

    @property
    def otp_cache_handler(self):
        if not self._otp_cache_handler:
            raise ValueError("OTP cache handler is not initialized.")
        return self._otp_cache_handler

    @property
    def api_service(self):
        if not self._api_service:
            raise ValueError("API service is not initialized.")
        return self._api_service

    @otp_cache_handler.setter
    def otp_cache_handler(self, handler: BaseOtpCacheHandler):
        self._otp_cache_handler = handler

    @api_service.setter
    def api_service(self, service: BaseOtpApiService):
        self._api_service = service

    # ------------ instance methods ----------

    def get_country_code_and_phone_number(self, number: str) -> Tuple[int, int]:
        try:
            phone_number = phonenumbers.parse(
                number, region=CountryCodeEnum.IN.value
            )
            if not phone_number or not phonenumbers.is_valid_number(
                phone_number
            ):
                raise OTPException("Invalid phone number")

            country_code = phone_number.country_code
            national_number = phone_number.national_number
            if country_code is None or national_number is None:
                raise OTPException("Invalid phone number")
            return country_code, national_number

        except phonenumbers.NumberParseException:
            raise OTPException("Invalid phone number")

    def validate_max_send_attempts(self):
        if not self.otp_cache_handler.send_attempt_allowed():
            msg = f"Send OTP limit exceeded. Maximum allowed: {self.otp_cache_handler.get_max_send_attempts()}"
            logger.info(msg)
            raise OTPMaxAttemptException(message=msg)

    def validate_max_verification_attempts(self):
        if not self.otp_cache_handler.verify_attempt_allowed():
            msg = f"OTP verification attempts exceeded. Maximum allowed: {self.otp_cache_handler.get_max_verification_attempts()}"
            logger.info(msg)
            raise OTPMaxAttemptException(message=msg)

    def send(self, *args, **kwargs):
        """Send OTP via the configured API service."""
        try:
            logger.info(
                f"Sending OTP args: {args}, kwargs: {kwargs} via {self.api_service.__class__.__name__}"
            )
            self.api_service.send_otp(*args, **kwargs)
        except OTPApiException as e:
            msg = "Failed to send OTP"
            logger.error(
                msg + f" via {self.api_service.__class__.__name__}",
                exc_info=True,
            )
            raise OTPException(message=msg) from e
