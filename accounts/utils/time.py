import logging
from django.utils import timezone
from dateutil import tz
from dateutil.relativedelta import relativedelta
import calendar

logger = logging.getLogger(__name__)


def calculate_start_date(billing_day, activation_date, timezone_name):
    start_date = ""

    activation_date = timezone.localtime(activation_date, timezone_name)
    current_date = timezone.localtime(timezone.now(), timezone_name)

    if activation_date.date() == current_date.date():
        start_date = activation_date.strftime("%Y-%m-%d")
    else:

        first_day_next_month = (
            current_date.replace(day=1) + timezone.timedelta(days=32)
        ).replace(day=1)
        last_day_of_current_month = first_day_next_month - timezone.timedelta(
            days=1
        )

        # in current month billing day has passed or billing day is today
        if billing_day <= current_date.day:
            start_date = current_date.strftime("%Y-%m-") + str(
                billing_day
            ).zfill(2)
        elif last_day_of_current_month.day == current_date.day:
            # billing day not exist this month so set it to last day
            if billing_day > last_day_of_current_month.day:
                start_date = last_day_of_current_month.strftime("%Y-%m-%d")
        # billing day has to come this month
        elif billing_day > current_date.day:
            last_billing_day = billing_day
            last_day_prev_month = current_date.replace(
                day=1
            ).date() - timezone.timedelta(days=1)

            if last_day_prev_month.day < billing_day:
                last_billing_day = last_day_prev_month.day

            start_date = last_day_prev_month.strftime("%Y-%m-") + +str(
                last_billing_day
            ).zfill(2)

    start_date = timezone.datetime.strptime(start_date, "%Y-%m-%d").replace(
        hour=0, minute=0, second=0, tzinfo=timezone_name
    )
    start_date = start_date.astimezone(timezone.utc).strftime(
        "%Y-%m-%d %H:%M:%S"
    )

    data = {
        "start_date": start_date,
        "end_date": timezone.now().strftime("%Y-%m-%d %H:%M:%S"),
    }

    return data


def calculate_local_hour(time_zone: str) -> str:
    tzone = tz.gettz(time_zone)
    date = timezone.now().astimezone(tzone)
    hour = date.strftime("%H")
    return hour


def calc_end_date_by_months(
    start_date: timezone.datetime, months: int
) -> timezone.datetime:
    end_date = start_date + relativedelta(months=months)
    return end_date


def calc_end_date_by_days(
    start_date: timezone.datetime, days: int
) -> timezone.datetime:
    end_date = start_date + timezone.timedelta(days=days)
    return end_date


def convert_utc_to_local(
    date_time: timezone.datetime, time_zone: str
) -> timezone.datetime:
    tzone = tz.gettz(time_zone)
    local_datetime = date_time.replace(tzinfo=tz.gettz("UTC")).astimezone(tzone)
    return local_datetime


def convert_local_to_utc(date_time: timezone.datetime) -> timezone.datetime:
    utc_datetime = date_time.astimezone(tz.gettz("UTC"))
    return utc_datetime


def next_billing_date_utc(
    billing_day: int, activation_datetime: timezone.datetime, time_zone: str
) -> timezone.datetime:
    current_datetime = timezone.now()
    current_datetime_local = convert_utc_to_local(current_datetime, time_zone)
    current_month_year = current_datetime_local.strftime("%Y-%m")
    next_month = current_datetime_local + relativedelta(months=1)
    next_month_year = next_month.strftime("%Y-%m")
    last_day_of_month = calendar.monthrange(
        int(current_datetime_local.strftime("%Y")),
        int(current_datetime_local.strftime("%m")),
    )[1]

    if (
        activation_datetime.date() == current_datetime.date()
        or activation_datetime.strftime("%Y-%m")
        == current_datetime.strftime("%Y-%m")
        or billing_day <= current_datetime_local.day
    ):
        start_date = f"{next_month_year}-{billing_day} 00:00:00"
        return convert_local_to_utc(
            timezone.datetime.strptime(start_date, "%Y-%m-%d %H:%M:%S").replace(
                tzinfo=tz.gettz(time_zone)
            )
        )

    if billing_day > last_day_of_month:
        start_date = f"{current_month_year}-{last_day_of_month} 00:00:00"
        return convert_local_to_utc(
            timezone.datetime.strptime(start_date, "%Y-%m-%d %H:%M:%S").replace(
                tzinfo=tz.gettz(time_zone)
            )
        )

    start_date = f"{current_month_year}-{billing_day} 00:00:00"
    return convert_local_to_utc(
        timezone.datetime.strptime(start_date, "%Y-%m-%d %H:%M:%S").replace(
            tzinfo=tz.gettz(time_zone)
        )
    )


def calc_prev_billing_date(
    billing_day: int, activation_datetime: timezone.datetime, time_zone: str
) -> timezone.datetime:
    start_date = ""
    current_datetime = timezone.now()

    if activation_datetime.date() == current_datetime.date():
        start_date = convert_utc_to_local(
            activation_datetime, time_zone
        ).strftime("%Y-%m-%d")
    else:
        current_datetime_local = convert_utc_to_local(
            current_datetime, time_zone
        )
        last_day_of_month = calendar.monthrange(
            int(current_datetime_local.strftime("%Y")),
            int(current_datetime_local.strftime("%m")),
        )[1]
        if billing_day <= current_datetime_local.day:
            # in current month billing day has passed or billing day is today
            start_date = current_datetime_local.strftime("%Y-%m-") + str(
                billing_day
            ).zfill(2)
        elif last_day_of_month == current_datetime_local.day:
            if billing_day > last_day_of_month:
                # billing day not exist this month so set it to last day
                start_date = current_datetime_local.strftime("%Y-%m-") + str(
                    last_day_of_month
                ).zfill(2)
        elif billing_day > current_datetime_local.day:
            # billing day has to come this month
            last_billing_day = billing_day
            last_day_prev_month = current_datetime.replace(
                day=1
            ).date() - timezone.timedelta(days=1)
            if last_day_prev_month.day < billing_day:
                last_billing_day = last_day_prev_month.day
            start_date = last_day_prev_month.strftime("%Y-%m-") + str(
                last_billing_day
            ).zfill(2)
    return convert_local_to_utc(
        timezone.datetime.strptime(start_date, "%Y-%m-%d").replace(
            hour=0, minute=0, second=0, tzinfo=tz.gettz(time_zone)
        )
    )


def billing_day_from_activation_date(
    activation_datetime: timezone.datetime, time_zone: str
) -> int:
    return convert_utc_to_local(activation_datetime, time_zone).day
