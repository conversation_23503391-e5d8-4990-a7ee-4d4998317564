from typing import Optional
from accounts.billing_accounts.models import BillingAccounts
from accounts.services.enums import ServiceStatusEnum
from accounts.services.models import Services


def get_root_billing_account_from_gsn(gsn: str) -> Optional[BillingAccounts]:
    """Get the root billing account from a GSN.

    Args:
        gsn (str): The GSN to get the root billing account from.

    Returns:
        Optional[BillingAccounts]: The root billing account.
    """
    if not gsn:
        return None
    try:
        service = (
            Services.objects.select_related("billing_account")
            .only("billing_account")
            .get(gsn=gsn, status=ServiceStatusEnum.ACTIVE.value)
        )  # Get the service with the GSN
    except Services.DoesNotExist:
        return None
    (
        billing_account,
        _,
    ) = service.billing_account.parent_ban()  # Get the root billing account
    return billing_account
