import time
import logging
from django.conf import settings
from django.core.management.base import BaseCommand
from django.db.models import Q
from django.db.models.query import QuerySet

from accounts.billing_accounts.models import BillingAccounts
from accounts.background_jobs.credit_limit.exceptions import (
    CreditLimitExceededException,
    LowBalanceException,
)
from accounts.services.enums import ServiceStatusEnum
from accounts.background_jobs.credit_limit.credit_limit_verifier import (
    CreditLimitVerifier,
)

logger = logging.getLogger("credit_limit")


class Command(BaseCommand):
    help = "Run credit limit check for all active billing accounts"

    def handle(self, *args, **options):
        start_time = time.time()
        billing_accounts = self.fetch_billing_accounts()
        logger.title("Credit Limit Started").info(
            f"Total Billing Accounts: {billing_accounts.count()}"
        )
        batch_size = 1000
        for idx, billing_account in enumerate(
            billing_accounts.iterator(chunk_size=batch_size), start=1
        ):
            self._reset_logging_uid()
            try:
                self._process(billing_account)
            except Exception as e:
                logger.title("Credit Limit Check").info(
                    f"billing_account_id: {billing_account.id}"
                )
                logger.title("Credit Limit Check").critical(e, exc_info=True)
                raise

            # Pause execution after processing `batch_size` accounts
            if idx % batch_size == 0:
                time.sleep(0.01)

        end_time = time.time()
        logger.title("Credit Limit Check Finished").info(
            f"Total Time: {end_time - start_time} seconds"
        )

    def _process(self, billing_account: BillingAccounts):
        credit_limit_verifier = CreditLimitVerifier(billing_account)
        try:
            credit_limit_verifier.verify()
        except CreditLimitExceededException:
            logger.title("Credit Limit Check").info(
                f"credit limit exceeded for billing_account_id: {billing_account.id}"
            )
            credit_limit_verifier.suspend_services()
            # todo: to be implemented when used for myop
            # credit_limit_verifier.notify_contacts("suspend")
        except LowBalanceException:
            logger.title("Credit Limit Check").info(
                f"low balance for billing_account_id: {billing_account.id}"
            )
            # todo: to be implemented when used for myop
            # credit_limit_verifier.notify_contacts("low_balance")

    def fetch_billing_accounts(self) -> QuerySet:
        queryset: QuerySet = BillingAccounts.active.filter(
            Q(parent_id="0") | Q(parent_id="") | Q(parent_id__isnull=True)
        )
        queryset = queryset.filter(
            service__product_id=settings.HEYO_PRODUCT_ID,
            service__status=ServiceStatusEnum.ACTIVE.value,
        )
        return queryset

    def _reset_logging_uid(self, uid=None):
        try:
            from myoperator.centrallog import config, helpers

            if uid is None:
                config.configure(uid=helpers.get_uuid())
            else:
                config.configure(uid=uid)
        except ImportError:
            pass
