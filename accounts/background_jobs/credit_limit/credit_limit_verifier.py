import typing
from decimal import Decimal
from django.conf import settings
from accounts.billing_accounts.models import (
    BillingAccounts,
    BillingAccountCredits,
)
from accounts.services.models import Services, OtherCharges
from accounts.services.utils.service_suspension import ServiceSuspension
from accounts.billing_accounts.enums import BillingStatusEnum
from accounts.services.enums import ServiceLiveStatusEnum
from .exceptions import (
    CreditLimitExceededException,
    LowBalanceException,
)


class CreditLimitVerifier:
    def __init__(self, billing_account: BillingAccounts):
        self._billing_account: BillingAccounts = billing_account
        self._services = None
        self._credit_limit_exceeded = False

    def verify(self) -> bool:
        """
        Verifies if the current usage of the billing account exceeds its credit limit
        or minimum balance.

        Raises:
            CreditLimitExceededException: If the current usage exceeds the credit limit.
            LowBalanceException: If the current usage is less than or equal to the minimum balance.
        """
        self._services = self.get_active_services()
        if not self._services:
            return False

        self._pending_amount = self.calc_pending_amount()
        self._check_credit_limit()
        return True

    def get_active_services(self):
        """Fetch active services for the billing account."""
        return (
            Services.active.billing_account(self._billing_account)
            .filter(billing_account__status=BillingStatusEnum.ACTIVE.value)
            .filter(
                live_status__in=(
                    ServiceLiveStatusEnum.POSTPAID.value,
                    ServiceLiveStatusEnum.PREPAID.value,
                    ServiceLiveStatusEnum.DEMO.value,
                )
            )
            .order_by("activation_date")
        )

    def calc_pending_amount(self) -> Decimal:
        is_corporate = self._billing_account.is_parent_ban()
        credit_amount: Decimal = BillingAccountCredits.entries.billing_account(
            self._billing_account
        ).credit_amount()

        pending_usages: Decimal = BillingAccountCredits.calc_outstanding_amount(
            credit_amount
        )
        current_usages: Decimal = (
            Services.objects.filter(
                billing_account__status=BillingStatusEnum.ACTIVE.value
            )
            .billing_account(self._billing_account, is_corporate)
            .total_current_usage()
        )
        other_charges: Decimal = (
            OtherCharges.unpaid.billing_account(
                self._billing_account, is_corporate
            )
            .filter(
                service__billing_account__status=BillingStatusEnum.ACTIVE.value
            )
            .total_other_charges()
        )

        advance: Decimal = BillingAccountCredits.calc_advance_amount(
            credit_amount
        )
        pending_amount = (
            pending_usages + current_usages + other_charges
        ) - advance
        return pending_amount if pending_amount > 0 else Decimal(0)

    def _check_credit_limit(self):
        """Check if pending amount exceeds credit limit or minimum balance."""
        if self._pending_amount > Decimal(self._billing_account.credit_limit):
            self._credit_limit_exceeded = True
            raise CreditLimitExceededException()

        if self._pending_amount >= Decimal(abs(self._billing_account.min_bal)):
            raise LowBalanceException()

    def suspend_services(self):
        """Suspend services if the credit limit is exceeded."""
        if not self._credit_limit_exceeded:
            raise RuntimeError(
                "Can't suspend services; credit limit is not exceeded"
            )

        for service in self._services:
            ServiceSuspension(service).suspend()

        # Need to add activity only once, in case of corporate account
        ServiceSuspension(self._services[0]).add_activity(settings.CL_CRON)

    def notify_contacts(self, notification_type):
        """Placeholder for notifying contacts (to be implemented)."""
        pass
