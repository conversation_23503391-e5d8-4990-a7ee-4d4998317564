from unittest import mock
from django.test import TestCase
from django.conf import settings

from accounts.billing_accounts.tests.factories import (
    BillingAccountFactory,
    BillingAccountCreditsFactory,
)
from accounts.users.tests.factories import UserProfileFactory
from accounts.services.tests.factories import (
    ServiceFactory,
    ServiceRentalFactory,
    ServiceNumberFactory,
    OtherChargesFactory,
)
from accounts.billing_accounts.enums import BillingStatusEnum
from accounts.services.enums import ServiceStatusEnum
from accounts.background_jobs.credit_limit.credit_limit_verifier import (
    CreditLimitVerifier,
)
from accounts.background_jobs.credit_limit.exceptions import (
    CreditLimitExceededException,
    LowBalanceException,
)


class TestCreditLimitVerifier(TestCase):
    def setUp(self):
        UserProfileFactory(id=settings.DD_CRON)

    def test_verify_true(self):
        billing_account = BillingAccountFactory.create(
            min_bal=-50,
            credit_limit=100,
        )
        ServiceFactory.create(
            billing_account=billing_account,
            current_usages=49,
            status=ServiceStatusEnum.ACTIVE.value,
        )
        self.assertTrue(CreditLimitVerifier(billing_account).verify())

    def test_verify_credit_limit_exceeded(self):
        billing_account = BillingAccountFactory.create(
            min_bal=-50,
            credit_limit=1000,
        )
        ServiceFactory.create(
            billing_account=billing_account, current_usages=1001, status=1
        )
        with self.assertRaises(CreditLimitExceededException):
            CreditLimitVerifier(billing_account).verify()

    def test_verify_credit_low_balance(self):
        billing_account = BillingAccountFactory.create(
            min_bal=-50,
            credit_limit=100,
        )
        service = ServiceFactory.create(
            billing_account=billing_account, current_usages=60, status=1
        )
        with self.assertRaises(LowBalanceException):
            CreditLimitVerifier(billing_account).verify()

        service.current_usages = 100
        with self.assertRaises(LowBalanceException):
            CreditLimitVerifier(billing_account).verify()

    def test_verify_credit_low_balance_positive_min_balance(self):
        billing_account = BillingAccountFactory.create(
            min_bal=50,
            credit_limit=100,
        )
        service = ServiceFactory.create(
            billing_account=billing_account, current_usages=60, status=1
        )
        with self.assertRaises(LowBalanceException):
            CreditLimitVerifier(billing_account).verify()

        service.current_usages = 100
        with self.assertRaises(LowBalanceException):
            CreditLimitVerifier(billing_account).verify()

    def test_calc_pending_amount_with_no_pending_amount(self):
        billing_account = BillingAccountFactory.create(
            min_bal=-50,
            credit_limit=100,
        )
        service = ServiceFactory.create(
            billing_account=billing_account, current_usages=0, status=1
        )
        BillingAccountCreditsFactory.create(
            billing_account=billing_account, credit_amount=10, status=1
        )
        OtherChargesFactory.create(charge=0, service=service, status=1)
        self.assertEqual(
            CreditLimitVerifier(billing_account).calc_pending_amount(), 0
        )

    def test_calc_pending_amount(self):
        billing_account = BillingAccountFactory.create(
            min_bal=-50,
            credit_limit=100,
        )
        service = ServiceFactory.create(
            billing_account=billing_account, current_usages=10, status=1
        )
        BillingAccountCreditsFactory.create(
            billing_account=billing_account, credit_amount=-200, status=1
        )
        OtherChargesFactory.create(charge=150, service=service, status=1)
        self.assertEqual(
            CreditLimitVerifier(billing_account).calc_pending_amount(), 360
        )

    def test_calc_pending_amount_with_current_usage_and_outstanding_amount(
        self,
    ):
        billing_account = BillingAccountFactory.create(
            min_bal=-50,
            credit_limit=100,
        )
        ServiceFactory.create(
            billing_account=billing_account, current_usages=60, status=1
        )
        BillingAccountCreditsFactory.create(
            billing_account=billing_account, credit_amount=-200, status=1
        )
        self.assertEqual(
            CreditLimitVerifier(billing_account).calc_pending_amount(), 260
        )

    def test_calc_pending_amount_with_outstanding_amount(self):
        billing_account = BillingAccountFactory.create(
            min_bal=-50,
            credit_limit=100,
        )
        ServiceFactory.create(
            billing_account=billing_account, current_usages=60, status=1
        )
        BillingAccountCreditsFactory.create(
            billing_account=billing_account, credit_amount=-200, status=1
        )
        self.assertEqual(
            CreditLimitVerifier(billing_account).calc_pending_amount(), 260
        )

    def test_calc_pending_amount_with_service_rental(self):
        billing_account = BillingAccountFactory.create(
            min_bal=-50,
            credit_limit=100,
        )
        service = ServiceFactory.create(
            billing_account=billing_account, current_usages=60, status=1
        )
        ServiceRentalFactory.create(
            pending_rental=500, service=service, status=1
        )
        self.assertEqual(
            CreditLimitVerifier(billing_account).calc_pending_amount(), 60
        )

    def test_calc_pending_amount_with_pending_service_number(self):
        billing_account = BillingAccountFactory.create(
            min_bal=-50,
            credit_limit=100,
        )
        service = ServiceFactory.create(
            billing_account=billing_account, current_usages=60, status=1
        )
        ServiceNumberFactory.create(
            number_cost=300, service=service, status=1, is_paid=0
        )
        self.assertEqual(
            CreditLimitVerifier(billing_account).calc_pending_amount(), 60
        )

    def test_calc_pending_amount_with_pending_other_charges(self):
        billing_account = BillingAccountFactory.create(
            min_bal=-50,
            credit_limit=100,
        )
        service = ServiceFactory.create(
            billing_account=billing_account, current_usages=60, status=1
        )
        OtherChargesFactory.create(charge=150, service=service, status=1)
        self.assertEqual(
            CreditLimitVerifier(billing_account).calc_pending_amount(), 210
        )

    @mock.patch("accounts.services.utils.service_suspension.sync_memcache")
    @mock.patch("accounts.services.events.ServiceSuspensionEvent.send")
    def test_suspend_non_corporate_account(
        self, mock_sns_event, mock_sync_memcache
    ):
        mock_sync_memcache.return_value = True
        billing_account = BillingAccountFactory.create(
            min_bal=-50,
            credit_limit=100,
        )
        service = ServiceFactory.create(
            billing_account=billing_account, current_usages=101, status=1
        )
        credit_limit = CreditLimitVerifier(billing_account)

        with self.assertRaises(CreditLimitExceededException):
            credit_limit.verify()

        credit_limit.suspend_services()
        service.refresh_from_db()
        self.assertEqual(service.status, ServiceStatusEnum.SUSPENDED.value)
        billing_account.refresh_from_db()
        self.assertEqual(billing_account.status, BillingStatusEnum.ACTIVE.value)
        mock_sync_memcache.assert_called_once_with(service.id)
        mock_sns_event.assert_called_once()

    @mock.patch("accounts.services.utils.service_suspension.sync_memcache")
    @mock.patch("accounts.services.events.ServiceSuspensionEvent.send")
    def test_suspend_corporate_account(
        self, mock_sns_event, mock_sync_memcache
    ):
        mock_sync_memcache.return_value = True

        corporate_ban = BillingAccountFactory.create(
            min_bal=-50,
            credit_limit=100,
        )
        ban_1 = BillingAccountFactory.create(
            parent=corporate_ban,
        )
        ban_2 = BillingAccountFactory.create(
            parent=corporate_ban,
        )
        service_1 = ServiceFactory.create(
            billing_account=ban_1, current_usages=50
        )
        service_2 = ServiceFactory.create(
            billing_account=ban_2, current_usages=51
        )
        credit_limit = CreditLimitVerifier(corporate_ban)
        with self.assertRaises(CreditLimitExceededException):
            credit_limit.verify()

        credit_limit.suspend_services()
        ban_1.refresh_from_db()
        ban_2.refresh_from_db()
        service_1.refresh_from_db()
        service_2.refresh_from_db()
        self.assertEqual(service_1.status, ServiceStatusEnum.SUSPENDED.value)
        self.assertEqual(service_2.status, ServiceStatusEnum.SUSPENDED.value)
        self.assertEqual(ban_1.status, BillingStatusEnum.ACTIVE.value)
        self.assertEqual(ban_2.status, BillingStatusEnum.ACTIVE.value)
        self.assertEqual(mock_sync_memcache.call_count, 2)
        self.assertEqual(mock_sns_event.call_count, 2)
