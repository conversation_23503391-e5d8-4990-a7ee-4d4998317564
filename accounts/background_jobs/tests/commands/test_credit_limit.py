from unittest.mock import patch
from django.conf import settings
from django.core.management import call_command
from django.test import TestCase
from accounts.billing_accounts.tests.factories import BillingAccountFactory
from accounts.services.tests.factories import ServiceFactory, ProductFactory
from accounts.billing_accounts.enums import BillingStatusEnum
from accounts.background_jobs.management.commands.credit_limit import Command


class CreditLimitCommandTest(TestCase):
    def setUp(self):
        self.heyo_product = ProductFactory(id=settings.HEYO_PRODUCT_ID)

    @patch(
        "accounts.background_jobs.credit_limit.credit_limit_verifier.CreditLimitVerifier.verify"
    )
    @patch("accounts.background_jobs.management.commands.credit_limit.logger")
    @patch("time.sleep", return_value=None)
    def test_handle_success(
        self, mock_sleep, mock_logger, mock_credit_verifier
    ):
        """Test command runs successfully and processes billing accounts"""
        # Create billing accounts
        billing_1 = BillingAccountFactory(status=BillingStatusEnum.ACTIVE.value)
        ServiceFactory(billing_account=billing_1, product=self.heyo_product)
        billing_2 = BillingAccountFactory(status=BillingStatusEnum.ACTIVE.value)
        ServiceFactory(billing_account=billing_2, product=self.heyo_product)
        billing_3 = BillingAccountFactory(status=BillingStatusEnum.ACTIVE.value)
        ServiceFactory(
            billing_account=billing_3
        )  # service with other product id

        # Mock verifier behavior
        mock_credit_verifier.return_value = True

        call_command("credit_limit")

        # Ensure each account was processed
        self.assertEqual(mock_credit_verifier.call_count, 2)

        # Check if logs were called
        mock_logger.title.assert_any_call("Credit Limit Started")
        mock_logger.title.assert_any_call("Credit Limit Check Finished")

        # Ensure sleep is called correctly
        mock_sleep.assert_not_called()  # Sleep only occurs after batch_size accounts

    @patch(
        "accounts.background_jobs.credit_limit.credit_limit_verifier.CreditLimitVerifier.suspend_services"
    )
    @patch("accounts.background_jobs.management.commands.credit_limit.logger")
    def test_handle_credit_limit_exceeded(
        self, mock_logger, mock_suspend_services
    ):
        """Test if CreditLimitExceededException is handled correctly"""
        billing_account = BillingAccountFactory(credit_limit=10)
        ServiceFactory(
            billing_account=billing_account,
            product=self.heyo_product,
            current_usages=11,
        )

        # Mock verifier behavior
        mock_suspend_services.return_value = True

        call_command("credit_limit")

        # Ensure suspend_services is called
        mock_suspend_services.assert_called_once()

    @patch("accounts.background_jobs.management.commands.credit_limit.logger")
    def test_handle_low_balance_exception(self, mock_logger):
        """Test if LowBalanceException is handled correctly"""
        billing_account = BillingAccountFactory(credit_limit=100, min_bal=-10)
        ServiceFactory(
            billing_account=billing_account,
            product=self.heyo_product,
            current_usages=11,
        )

        call_command("credit_limit")

        # Ensure logging occurs
        mock_logger.title.assert_any_call("Credit Limit Check")
        # todo: add mocking when notification is implemented

    def test_fetch_billing_accounts(self):
        """Test fetch_billing_accounts method returns correct queryset"""
        billing_account1 = BillingAccountFactory()
        ServiceFactory(
            billing_account=billing_account1, product=self.heyo_product
        )
        billing_account2 = BillingAccountFactory(
            status=BillingStatusEnum.INACTIVE.value
        )
        ServiceFactory(
            billing_account=billing_account2, product=self.heyo_product
        )
        billing_account3 = BillingAccountFactory()
        billing_account4 = BillingAccountFactory(parent=billing_account3)

        accounts = Command().fetch_billing_accounts()

        self.assertIn(billing_account1, accounts)
        self.assertNotIn(billing_account2, accounts)
        # self.assertIn(billing_account3, accounts)
        self.assertNotIn(billing_account4, accounts)
