# Generated by Django 3.2.18 on 2025-06-03 12:37

import accounts.utils.common
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payments', '0002_auto_20250429_1222'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentActions',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('payment_id', models.CharField(max_length=36)),
                ('action_type', models.CharField(choices=[('service_activation', 'SERVICE_ACTIVATION')], max_length=50)),
                ('action_data', models.J<PERSON>NField(help_text='JSON data for the action')),
                ('failure_reason', models.TextField(blank=True, default=None, null=True)),
                ('status', models.SmallIntegerField(choices=[(0, 'PENDING'), (1, 'SUCCESS'), (2, 'FAILED')], default=0)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'payment_actions',
            },
        ),
        migrations.AddIndex(
            model_name='paymentactions',
            index=models.Index(fields=['payment_id'], name='payment_act_payment_5b85d9_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentactions',
            index=models.Index(fields=['action_type'], name='payment_act_action__ece3a0_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentactions',
            index=models.Index(fields=['status'], name='payment_act_status_7b9582_idx'),
        ),
    ]
