import logging
from rest_framework.response import Response
from rest_framework.serializers import ValidationError
from accounts.exceptions import BaseException
from accounts.payments.tasks import (
    process_payment_failure_attempt,
    update_credit_limit_zero,
)
from accounts.generics import SnsHandlerView

logger = logging.getLogger(__name__)


class ProcessPaymentFailureAttempt(SnsHandlerView):
    def notification_handler(self, message):
        try:
            billing_account_id = message["billing_account_id"]
            payment_id = message["payment_id"]
            task = (
                process_payment_failure_attempt.apply_async(
                    kwargs={
                        "payment_id": payment_id,
                        "billing_account_id": billing_account_id,
                    },
                ),
            )
            logger.title("Celery Task Scheduled").info(f"ID: {task[0]}")
            return Response(
                {"message": "Task Scheduled", "data": {"task_id": str(task[0])}}
            )
        except (TypeError, KeyError):
            raise ValidationError(detail="Params are missing")
        except BaseException as e:
            logger.error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )


class CancelPaymentSubscription(SnsHandlerView):
    def notification_handler(self, message):
        try:
            billing_account_id = message["billing_account_id"]
            task = (
                update_credit_limit_zero.apply_async(
                    kwargs={"billing_account_id": billing_account_id},
                ),
            )
            logger.title("Celery Task Scheduled").info(f"ID: {task[0]}")
            return Response(
                {"message": "Task Scheduled", "data": {"task_id": str(task[0])}}
            )
        except (TypeError, KeyError):
            raise ValidationError(detail="billing account id is missing.")
        except BaseException as e:
            logger.error(e, exc_info=True)
            return Response(
                {
                    "status": "error",
                    "code": e.get_error_code(),
                    "message": str(e),
                    "errors": e.get_errors(),
                },
                e.get_http_status_code(),
            )
