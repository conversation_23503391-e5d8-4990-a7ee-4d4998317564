from django.conf import settings

from accounts.utils.events.sns import SnsEvent
from accounts.services.utils.service import get_product_short_code_from_gsn
from accounts.billing_accounts.utils.billing_account import (
    get_product_short_code_from_ban,
)


class PaymentFailureEvent(SnsEvent):

    EVENT_ACTION = "payment_failed"
    REQUIRED_PARAMS = ("billing_account_id", "payment_id")

    def __init__(self):
        self.arn = settings.ACCOUNTS_SNS_ARN
        super().__init__()

    def payment_id(self, payment_id):
        self.set_data("payment_id", payment_id)
        return self

    def billing_account_id(self, billing_account_id):
        self.service_type = get_product_short_code_from_ban(billing_account_id)
        self.set_data("billing_account_id", billing_account_id)
        return self


class PaymentSuccessEvent(SnsEvent):

    EVENT_ACTION = "payment_succeeded"
    REQUIRED_PARAMS = ("billing_account_id", "payment_id")

    def __init__(self):
        self.arn = settings.ACCOUNTS_SNS_ARN
        super().__init__()

    def payment_id(self, payment_id):
        self.set_data("payment_id", payment_id)
        return self

    def billing_account_id(self, billing_account_id):
        self.service_type = get_product_short_code_from_ban(billing_account_id)
        self.set_data("billing_account_id", billing_account_id)
        return self
