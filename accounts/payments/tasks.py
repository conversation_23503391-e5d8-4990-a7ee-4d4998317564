import logging

from django.conf import settings
from django.utils import timezone

import celery
import celery.states

from accounts.billing_accounts.exceptions import (
    InvalidBillingAccountException,
)
from accounts.payments.exceptions import (
    InvalidPaymentException,
    InvalidPaymentSubscriptionException,
)
from accounts.payments.models import InvoiceQueues, PaymentTracks
from accounts.payments.utils.payment_failure import (
    process_payment_failure,
)
from accounts.payments.utils.payment_subscription import (
    set_credit_limit_zero,
)
from accounts.utils.api_services.exceptions import (
    CancelSubscriptionException,
)

logger = logging.getLogger(__name__)


@celery.shared_task(bind=True, queue=settings.CELERY_TASK_DEFAULT_QUEUE)
def process_payment_failure_attempt(
    task: celery.Task,
    payment_id: str,
    billing_account_id: str,
):
    task.update_state(state=celery.states.STARTED)
    logger.title("Process Payment Failure Attempt").info(
        {"payment_id": payment_id, "billing_account_id": billing_account_id}
    )
    try:
        process_payment_failure(payment_id, billing_account_id)
        task.update_state(state=celery.states.SUCCESS)
    except (
        InvalidPaymentSubscriptionException,
        InvalidBillingAccountException,
        CancelSubscriptionException,
    ) as e:
        logger.title("Process Payment Failure Attempt").error(e, exc_info=True)
        task.update_state(state=celery.states.FAILURE)
        raise
    except Exception as e:
        logger.title("Process Payment Failure Attempt").critical(
            e, exc_info=True
        )
        task.update_state(state=celery.states.FAILURE)
        raise


@celery.shared_task(bind=True, queue=settings.CELERY_TASK_DEFAULT_QUEUE)
def update_credit_limit_zero(
    task: celery.Task,
    billing_account_id: str,
):
    task.update_state(state=celery.states.STARTED)
    logger.title("Cancel Payment Subscription").info(
        {"billing_account_id": billing_account_id}
    )
    try:
        set_credit_limit_zero(billing_account_id)
        task.update_state(state=celery.states.SUCCESS)
    except (
        InvalidPaymentSubscriptionException,
        InvalidBillingAccountException,
    ) as e:
        logger.title("Cancel Payment Subscription").error(e, exc_info=True)
        task.update_state(state=celery.states.FAILURE)
        raise
    except Exception as e:
        logger.title("Cancel Payment Subscription").critical(e, exc_info=True)
        task.update_state(state=celery.states.FAILURE)
        raise


@celery.shared_task(bind=True, queue=settings.CELERY_TASK_DEFAULT_QUEUE)
def after_payment_settled_task(
    task: celery.Task,
    billing_account_id: str,
    payment_id: str,
):
    task.update_state(state=celery.states.STARTED)
    logger.title("After Payment Settement Task").info(
        {"billing_account_id": billing_account_id, "payment_id": payment_id}
    )
    try:
        payment_track = PaymentTracks.objects.get(payment_id=payment_id)
        if not payment_track.is_txn_settled():
            raise InvalidPaymentException("Payment has not been settled yet.")

        # todo: generate payment receipt

        # Create an invoice queue entry for the settled payment
        InvoiceQueues.objects.create(
            billing_account=billing_account_id,
            payment=payment_track,
            invoice_date=timezone.now(),
        )
        task.update_state(state=celery.states.SUCCESS)
    except Exception as e:
        logger.title("Cancel Payment Subscription").critical(e, exc_info=True)
        task.update_state(state=celery.states.FAILURE)
        raise
