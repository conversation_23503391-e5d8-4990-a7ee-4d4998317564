from decimal import Decimal
from unittest.mock import patch

from django.test import TestCase, override_settings

from accounts.billing_accounts.models import BillingAccountCredits
from accounts.billing_accounts.tests.factories import (
    BillingAccountCreditsFactory,
    BillingAccountFactory,
)
from accounts.core.tests.factories import StateCodeFactory, TaxVoicetreeFactory
from accounts.payments.enums import (
    TransactionClaimStatusEnum,
    TransactionClaimVerifyEnum,
    TransactionSettleStatusEnum,
)
from accounts.payments.exceptions import InvalidPaymentException
from accounts.payments.models import InvoiceQueues, TrackingSettlementHistories
from accounts.payments.tests.factories import (
    PaymentTracksFactory,
    TrackingSettlementHistoriesFactory,
)
from accounts.payments.utils.payment_settlement import PaymentSettlement


class TestPaymentSettlement(TestCase):
    def setUp(self):
        self.state_code = StateCodeFactory.create(country__id=99)
        TaxVoicetreeFactory.create_gst_tax(self.state_code)
        self.billing_account = BillingAccountFactory.create(
            state_id=self.state_code.id
        )
        BillingAccountCreditsFactory.create(
            billing_account=self.billing_account, credit_amount=0
        )

    @patch("accounts.utils.settle_credit_bucket.SettleCreditBucket.process")
    def test_settle_by_payment_code_success(
        self, mock_credit_bucket_settlement
    ):
        mock_credit_bucket_settlement.return_value = True
        payment = PaymentTracksFactory.create(
            amount=100,
            req_amount=100,
            claimed=TransactionClaimStatusEnum.CLAIMED.value,
            claim_verify=TransactionClaimVerifyEnum.VERIFIED.value,
        )
        PaymentSettlement(self.billing_account).settle_payment_code(
            payment.txn_setl_key
        )
        payment.refresh_from_db()
        self.assertTrue(payment.is_txn_settled())

        # Settlement history created
        tsh = TrackingSettlementHistories.objects.first()
        self.assertEqual(tsh.billing_account, self.billing_account)
        self.assertEqual(tsh.payment_id, payment.payment_id)
        self.assertEqual(tsh.amount, payment.amount)
        mock_credit_bucket_settlement.assert_called_once()

    @patch("accounts.utils.settle_credit_bucket.SettleCreditBucket.process")
    def test_settle_by_payment_id_success(self, mock_credit_bucket_settlement):
        mock_credit_bucket_settlement.return_value = True
        payment = PaymentTracksFactory.create(
            amount=588.82,
            req_amount=588.82,
            claimed=TransactionClaimStatusEnum.CLAIMED.value,
            claim_verify=TransactionClaimVerifyEnum.VERIFIED.value,
        )
        PaymentSettlement(self.billing_account).settle_payment_id(
            payment.payment_id
        )
        payment.refresh_from_db()
        self.assertTrue(payment.is_txn_settled())

        # Settlement history created
        tsh = TrackingSettlementHistories.objects.first()
        self.assertEqual(tsh.billing_account, self.billing_account)
        self.assertEqual(tsh.payment_id, payment.payment_id)
        self.assertEqual(tsh.amount, payment.amount)
        mock_credit_bucket_settlement.assert_called_once()
        bac = BillingAccountCredits.entries.get_credit_object(
            self.billing_account
        )
        self.assertEqual(
            bac.credit_amount, Decimal(499)
        )  # after deduction of 18% tax
        self.assertEqual(
            bac.description,
            f"received amount 588.82 for payment code #{payment.txn_setl_key} and tax deducted of 89.820",
        )
        # einvoce_queue = InvoiceQueues.objects.get(payment_id=payment.payment_id)
        # self.assertEqual(einvoce_queue.billing_account, self.billing_account)
        # self.assertEqual(einvoce_queue.data, "[]")
        # self.assertEqual(einvoce_queue.status, 0)

    def test_settle_invalid_payment_code(self):
        with self.assertRaises(InvalidPaymentException) as ctx:
            PaymentSettlement(self.billing_account).settle_payment_code(
                "INVALID_CODE"
            )
        self.assertEqual(str(ctx.exception), "Invalid payment code.")

    def test_settle_invalid_payment_id(self):
        with self.assertRaises(InvalidPaymentException) as ctx:
            PaymentSettlement(self.billing_account).settle_payment_id(
                "INVALIDID"
            )
        self.assertEqual(str(ctx.exception), "Invalid payment ID.")

    def test_settle_unverified_payment(self):
        payment = PaymentTracksFactory.create(
            amount=100,
            req_amount=100,
            claimed=TransactionClaimStatusEnum.NOT_CLAIMED.value,
            claim_verify=TransactionClaimVerifyEnum.NOT_VERIFIED.value,
        )
        with self.assertRaises(InvalidPaymentException) as ctx:
            PaymentSettlement(self.billing_account).settle_payment_id(
                payment.payment_id
            )
        self.assertIn("not claimed or verified", str(ctx.exception))

        with self.assertRaises(InvalidPaymentException) as ctx:
            PaymentSettlement(self.billing_account).settle_payment_code(
                payment.txn_setl_key
            )
        self.assertIn("not claimed or verified", str(ctx.exception))

    def test_settle_already_settled_payment(self):
        payment = PaymentTracksFactory.create(
            amount=100,
            req_amount=100,
            txn_settle_status=TransactionSettleStatusEnum.MANUALLY_SETTLED.value,
            claimed=TransactionClaimStatusEnum.CLAIMED.value,
            claim_verify=TransactionClaimVerifyEnum.VERIFIED.value,
        )
        with self.assertRaises(InvalidPaymentException) as ctx:
            PaymentSettlement(self.billing_account).settle_payment_id(
                payment.payment_id
            )
        self.assertIn("already been settled", str(ctx.exception))

        with self.assertRaises(InvalidPaymentException) as ctx:
            PaymentSettlement(self.billing_account).settle_payment_code(
                payment.txn_setl_key
            )
        self.assertIn("already been settled", str(ctx.exception))

    def test_already_settled_payment_with_another_ban(self):
        TrackingSettlementHistoriesFactory.create(setl_key="123abc")
        payment = PaymentTracksFactory.create(
            amount=100,
            req_amount=100,
            txn_setl_key="123abc",
            txn_settle_status=TransactionSettleStatusEnum.MANUALLY_SETTLED.value,
            claimed=TransactionClaimStatusEnum.CLAIMED.value,
            claim_verify=TransactionClaimVerifyEnum.VERIFIED.value,
        )
        with self.assertRaises(InvalidPaymentException) as ctx:
            PaymentSettlement(self.billing_account).settle_payment_code(
                "123abc"
            )
        self.assertIn("Payment has already been settled.", str(ctx.exception))

        with self.assertRaises(InvalidPaymentException) as ctx:
            PaymentSettlement(self.billing_account).settle_payment_id(
                payment.payment_id
            )
        self.assertIn("Payment has already been settled.", str(ctx.exception))

    def test_settle_payment_with_zero_amount(self):
        payment = PaymentTracksFactory.create(
            amount=0,
            req_amount=0,
            claimed=TransactionClaimStatusEnum.CLAIMED.value,
            claim_verify=TransactionClaimVerifyEnum.VERIFIED.value,
        )
        with self.assertRaises(InvalidPaymentException) as ctx:
            PaymentSettlement(self.billing_account).settle_payment_code(
                payment.txn_setl_key
            )

        self.assertEqual("amount received is 0", str(ctx.exception))

        with self.assertRaises(InvalidPaymentException) as ctx:
            PaymentSettlement(self.billing_account).settle_payment_id(
                payment.payment_id
            )
        self.assertEqual("amount received is 0", str(ctx.exception))
