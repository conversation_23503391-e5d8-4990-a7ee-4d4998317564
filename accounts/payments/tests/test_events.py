import pytest
from django.test import TestCase
import unittest.mock as mock

from accounts.payments.events import PaymentFailureEvent, PaymentSuccessEvent

from accounts.billing_accounts.tests.factories import BillingAccountFactory
from accounts.core.tests.factories import CountryFactory
from accounts.products.tests.factories import ProductFactory
from accounts.services.tests.factories import ServiceFactory


class TestEvents(TestCase):
    def setUp(self):
        self.country = CountryFactory.create()
        self.product = ProductFactory.create(
            name="MyOperator India", country=self.country, short_code="myopin"
        )
        self.product_2 = ProductFactory.create(
            name="Heyo India", country=self.country, short_code="heyoin"
        )
        self.account = BillingAccountFactory.create(ac_number="ABCXYZ")
        ServiceFactory.create(
            billing_account=self.account,
            product_id=self.product.id,
            gsn="abc123",
            status=1,
            live_status=1,
        )

        self.account_2 = BillingAccountFactory.create()
        ServiceFactory.create(
            billing_account=self.account_2,
            product_id=self.product_2.id,
        )

    @classmethod
    def sns_client(cls):
        sns_client = mock.MagicMock()
        sns_client.publish.return_value = {"MessageId": "1234"}
        return sns_client

    def test_payment_failure_event_success(self):
        sns_client = self.sns_client()
        event = PaymentFailureEvent()
        event.billing_account_id(self.account.id)
        event.payment_id(1)
        event.sns = sns_client
        response = event.send()
        assert response == "1234"
        assert event.EVENT_ACTION == "payment_failed"
        assert event.service_type == "myopin"
        assert event.data["payment_id"] == 1
        assert event.data["billing_account_id"] == self.account.id

    def test_payment_failure_event_failure(self):
        event = PaymentFailureEvent()
        with pytest.raises(TypeError):
            event.billing_account_id()
        with pytest.raises(TypeError):
            event.payment_id()

    def test_payment_success_event_success(self):
        sns_client = self.sns_client()
        event = PaymentSuccessEvent()
        event.billing_account_id(self.account.id)
        event.payment_id(1)
        event.sns = sns_client
        response = event.send()
        assert response == "1234"
        assert event.EVENT_ACTION == "payment_succeeded"
        assert event.service_type == "myopin"
        assert event.data["payment_id"] == 1
        assert event.data["billing_account_id"] == self.account.id

    def test_payment_success_event_failure(self):
        event = PaymentFailureEvent()
        with pytest.raises(TypeError):
            event.billing_account_id()
        with pytest.raises(TypeError):
            event.payment_id()
