from accounts.enums import BaseEnum


class SubscriptionPaymentAttemptStatusEnum(BaseEnum):
    PENDING = 0
    FAILED = 1
    SUCCESS = 2


class PaymentSubscriptionStatusEnum(BaseEnum):
    DISABLED = 0
    ENABLED = 1


class PaymentActionTypeEnum(BaseEnum):
    # OFFER = "offer"
    SERVICE_ACTIVATION = "service_activation"
    # AUTO_PAY_SETUP = "auto_pay_setup"  # future use


class PaymentActionStatusEnum(BaseEnum):
    PENDING = 0
    SUCCESS = 1
    FAILED = 2


class TransactionSettleStatusEnum(BaseEnum):
    NOT_SETTLED = 0
    MANUALLY_SETTLED = 1
    AUTO_SETTLED = 2
    RESERVED = 3


class TransactionClaimStatusEnum(BaseEnum):
    NOT_CLAIMED = 0
    UNDER_PROCESS = 1
    CLAIMED = 2


class TransactionClaimVerifyEnum(BaseEnum):
    NOT_VERIFIED = 0
    VERIFIED = 1
    REJECTED = 2
