import json

from django.db import models
from django.utils import timezone

from accounts.billing_accounts.models import BillingAccounts
from accounts.core.models import Countries, StateCodes
from accounts.packages.models import Packages
from accounts.payments.enums import (
    PaymentActionStatusEnum,
    PaymentActionTypeEnum,
    PaymentSubscriptionStatusEnum,
    SubscriptionPaymentAttemptStatusEnum,
    TransactionClaimVerifyEnum,
    TransactionSettleStatusEnum,
)
from accounts.products.models import Products
from accounts.users.models import UserProfiles
from accounts.utils.common import uuid


class CcavenueSiRequests(models.Model):
    id = models.AutoField(primary_key=True)
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE
    )
    si_creation_status = models.CharField(max_length=10)
    si_ref_no = models.CharField(max_length=50)
    si_sub_ref_no = models.CharField(max_length=50)
    email = models.CharField(max_length=250, null=True)
    mobile = models.BigIntegerField(null=True)
    business_name = models.CharField(max_length=250, null=True)
    business_address = models.TextField(null=True)
    business_city = models.CharField(max_length=250, null=True)
    business_state = models.CharField(max_length=250, null=True)
    business_pincode = models.CharField(max_length=30, null=True)
    request_api = models.TextField(null=True)
    response_api = models.TextField(null=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "ccavenue_si_requests"
        indexes = [models.Index(fields=["billing_account_id"])]


class Recharges(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE, default=None, null=True
    )
    country = models.ForeignKey(Countries, on_delete=models.CASCADE, default=99)
    state_code = models.ForeignKey(StateCodes, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    tds_amount = models.DecimalField(
        max_digits=10, decimal_places=2, default=0.00
    )
    selected_amount = models.DecimalField(
        max_digits=10, decimal_places=2, default=0.00
    )
    selected_amount_tax = models.DecimalField(
        max_digits=10, decimal_places=2, default=0.00
    )
    number_cost = models.DecimalField(
        max_digits=10, decimal_places=2, default=0.00
    )
    package = models.ForeignKey(
        Packages, on_delete=models.CASCADE, default=None, null=True
    )
    payment_id = models.CharField(max_length=36, unique=True)
    response_url = models.TextField(null=True)
    response_method = models.CharField(max_length=10, null=True)
    name = models.CharField(max_length=250, null=True)
    email = models.CharField(max_length=250)
    phone = models.CharField(max_length=200, default="0")
    service_number = models.CharField(max_length=200, null=True)
    agent_email = models.CharField(max_length=250, null=True)
    agent_phone = models.CharField(max_length=30, null=True)
    secure_hash = models.CharField(max_length=250)
    recharge_type = models.CharField(
        max_length=50,
        default="fix",
        help_text="fix => fix amount, val => variable amount, pick => 3 option to choose",
    )
    form_visibility = models.SmallIntegerField(default=1)
    tax_calculation = models.SmallIntegerField(
        default=1, help_text="1=> yes, 0=> no"
    )
    customer_identifier = models.CharField(max_length=70, null=True)
    payment_gateway = models.CharField(max_length=200, null=True)
    gateway_response = models.TextField(null=True)
    general_response = models.TextField(null=True)
    status = models.CharField(
        max_length=5,
        default="0",
        help_text="0 => not done, 1 => under process(requested), 2=> responsed(not processed) Y=> full Paid, P => Partial Paid, N => decline, B => Hold",
    )
    renewal_flag = models.SmallIntegerField(
        default=0, help_text="0- For All Other 1- For Renewal Automation"
    )
    renewal_services = models.TextField(null=True)
    number_type = models.IntegerField(default=0)
    request_of = models.CharField(max_length=50, null=True)
    payment_method = models.CharField(
        max_length=3, null=True, help_text="selected payment method"
    )
    page_load_seq = models.CharField(
        max_length=3, default="0", help_text="page number moving"
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "recharges"
        indexes = [
            models.Index(fields=["billing_account_id"]),
            models.Index(fields=["package_id"]),
            models.Index(fields=["payment_id"]),
            models.Index(fields=["email"]),
            models.Index(fields=["status"]),
            models.Index(fields=["recharge_type"]),
            models.Index(fields=["secure_hash"]),
        ]

    def is_completed(self):
        """
        Check if the payment is completed

        Returns:
            bool: True if the payment status is completed, False otherwise.
        """
        return self.status == "Y"


class RechargeTracks(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    recharge = models.ForeignKey(
        Recharges, on_delete=models.CASCADE, default=None, null=True
    )
    payment_gateway = models.CharField(max_length=200, null=True)
    payment_gateway_ref_id = models.CharField(max_length=50, null=True)
    status = models.IntegerField(
        default=1, help_text="1 => initiated, 2 => processed"
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "recharge_tracks"


class PaymentFailureReasonLogs(models.Model):
    id = models.AutoField(primary_key=True)
    user_profile = models.ForeignKey(UserProfiles, on_delete=models.CASCADE)
    search_string = models.TextField()
    created = models.DateTimeField(auto_now_add=True)

    objects = models.Manager()

    class Meta:
        db_table = "payment_failure_reason_logs"


class PaymentFailureReasons(models.Model):
    id = models.AutoField(primary_key=True)
    payment = models.ForeignKey(
        Recharges, to_field="payment_id", on_delete=models.CASCADE
    )
    amount = models.DecimalField(max_digits=10, decimal_places=3)
    name = models.CharField(max_length=250)
    phone = models.CharField(max_length=250)
    email = models.CharField(max_length=250)
    gateway = models.CharField(max_length=200)
    type = models.IntegerField(help_text="1-Normal, 2-Subscription")
    gateway_response = models.TextField()
    reason = models.TextField()
    created = models.DateTimeField(auto_now_add=True)

    objects = models.Manager()

    class Meta:
        db_table = "payment_failure_reasons"
        indexes = [
            models.Index(fields=["payment_id"]),
            models.Index(fields=["created"]),
        ]


class PaymentSubscriptions(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    gateway = models.CharField(
        max_length=50,
        default="stripe",
        help_text="name of gateway ccavenue or stripe as defined in config",
    )
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE
    )
    customer_ref = models.CharField(
        max_length=250,
        null=True,
        help_text="customer reference id received from gateway after registering customer, in case ccavenue it is subscription sub reference",
    )
    plan_ref = models.CharField(
        max_length=200,
        null=True,
        help_text="plan reference number received from gateway after registering plan, in case ccavenue it may not available or directly associated with account plan id",
    )
    subs_type = models.CharField(
        max_length=50,
        null=True,
        help_text="in case of stripe it will not be available but in case ccavenue it will be ONDEMAND",
    )
    subs_ref = models.CharField(
        max_length=250,
        help_text="reference number of subscription received from gateway in case ccavneue it is merchant reference",
    )
    setup_amount = models.DecimalField(
        max_digits=10,
        decimal_places=3,
        default=0.000,
        help_text="in case stripe it will be always zero",
    )
    currency = models.CharField(
        max_length=5, default="USD", help_text="currency INR or USD"
    )
    start_date = models.DateTimeField(help_text="subscription start date")
    frequency = models.IntegerField(
        default=1,
        help_text="number of frequecy type on which new invoices need to be created.in case of both gateway it will be 1",
    )
    frequency_type = models.CharField(
        max_length=50,
        default="months",
        help_text="in terms of months we need to generate invoices",
    )
    billing_cycle = models.IntegerField(
        default=12,
        help_text="plan billing cycle in terms of number of months example 12",
    )
    email = models.CharField(max_length=250)
    phone = models.CharField(max_length=250)
    name = models.CharField(max_length=250)
    addresss = models.TextField(null=True)
    city = models.CharField(max_length=200, null=True)
    country = models.CharField(max_length=200)
    state = models.CharField(max_length=200)
    zip = models.CharField(max_length=50)
    addon = models.TextField(null=True)
    status = models.CharField(
        max_length=5, default=PaymentSubscriptionStatusEnum.ENABLED.value
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "payment_subscriptions"


class PaymentTracks(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    account = models.CharField(max_length=250)
    ref_no = models.CharField(max_length=250)
    payment_id = models.CharField(max_length=36, unique=True)
    transaction_date = models.DateTimeField()
    value_date = models.DateTimeField()
    amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    tr_description = models.TextField(null=True)
    branch = models.CharField(max_length=250, null=True)
    tr_flag = models.CharField(
        max_length=5, default="cr", help_text="cr => credit, dr => debit"
    )
    tr_status = models.CharField(
        max_length=5,
        default="c",
        help_text="C => Clear, B => Bounce, U => under clear",
    )
    bank_sheet_id = models.CharField(max_length=36, null=True)
    tr_modify_cmnt = models.TextField(null=True)
    claimed = models.IntegerField(
        default=0, help_text="0 => not claimed, 1 => under process, 2=> claimed"
    )
    client_name = models.CharField(max_length=250, null=True)
    req_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True)
    tds = models.DecimalField(max_digits=10, decimal_places=2, null=True)
    tds_proof = models.CharField(max_length=255, null=True)
    tan = models.CharField(max_length=11, null=True)
    claim_verify = models.IntegerField(
        default=1, help_text="0=>not verified, 1=> verified"
    )
    claim_verify_by = models.CharField(max_length=36, default="0")
    claim_verify_message = models.CharField(max_length=250, null=True)
    remark = models.TextField()
    claimed_by = models.CharField(max_length=36, null=True)
    claimed_by_dept = models.CharField(max_length=36, default="0")
    aditional_charge = models.DecimalField(
        max_digits=10, decimal_places=2, default=0.00
    )
    txn_setl_key = models.CharField(max_length=10, null=True)
    txn_settle_status = models.IntegerField(
        choices=TransactionSettleStatusEnum.choices(),
        default=TransactionSettleStatusEnum.NOT_SETTLED.value,
        help_text="0=>not settled, 1=>settled manually, 2=>settled auto, 3=>reserved for account use only",
    )
    sub_trxn_status = models.IntegerField(default=0)
    parent_trxn = models.CharField(max_length=36, default="0")
    secure_hash = models.CharField(max_length=200, default="czku4v")
    tr_mode = models.CharField(max_length=36)
    gateway_through = models.CharField(max_length=250, null=True)
    gateway_payment_id = models.CharField(
        max_length=50, null=True, default=None
    )
    state_code = models.ForeignKey(
        StateCodes, on_delete=models.SET_NULL, null=True
    )
    payment_receipt_id = models.CharField(
        max_length=36, null=True, default=None
    )
    no_invoice = models.IntegerField(
        default=0,
        help_text="if no_invoice is set to 1 the do not generate invoice for such payments",
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "payment_tracks"
        indexes = [
            models.Index(fields=["ref_no"]),
            models.Index(fields=["payment_id"]),
            models.Index(fields=["transaction_date"]),
            models.Index(fields=["claimed"]),
            models.Index(fields=["claimed_by"]),
            models.Index(fields=["claimed_by_dept"]),
            models.Index(fields=["txn_setl_key"]),
            models.Index(fields=["txn_settle_status"]),
        ]

    def calculate_tr_modify_comment(self, comment: str) -> str:
        existing_comments = {}
        if self.tr_modify_cmnt:
            existing_comments = json.loads(self.tr_modify_cmnt)

        key = timezone.now().strftime("%Y-%m-%d %H:%M:%S")
        existing_comments[key] = comment
        return json.dumps(existing_comments)

    def mark_as_settled(self, comment=None):
        """
        Mark the payment track as settled.
        """
        self.txn_settle_status = (
            TransactionSettleStatusEnum.MANUALLY_SETTLED.value
        )
        if comment:
            self.tr_modify_cmnt = self.calculate_tr_modify_comment(comment)
        self.save()

    def is_txn_settled(self) -> bool:
        """
        Check if the payment track is settled.

        Returns:
            bool: True if the payment track is settled, False otherwise.
        """
        return self.txn_settle_status in (
            TransactionSettleStatusEnum.MANUALLY_SETTLED.value,
            TransactionSettleStatusEnum.AUTO_SETTLED.value,
        )


class PaymentReceipts(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    recipt_number = models.CharField(max_length=200)
    payment = models.OneToOneField(
        PaymentTracks,
        to_field="payment_id",
        on_delete=models.CASCADE,
        unique=True,
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_mode = models.CharField(max_length=200)
    pdf_url = models.CharField(max_length=250)
    status = models.SmallIntegerField(
        default=1, help_text="0 => receipt reserved, 1 => receipt generated"
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "payment_receipts"
        indexes = [models.Index(fields=["payment_id"])]


class PaymentSplits(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE
    )
    ac_number = models.CharField(
        max_length=10, help_text="billing account number"
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        help_text="amount from payment track",
    )
    user_profile = models.ForeignKey(
        UserProfiles, on_delete=models.CASCADE, null=True
    )
    agent_name = models.CharField(max_length=200, null=True)
    payment_flag = models.IntegerField(
        default=1, help_text="1 => active, 0 => inactive"
    )
    refund_flag = models.IntegerField(
        default=0,
        help_text="0 => no refund, 1 => partial refund, 2 => full refund",
    )
    invoice_id = models.CharField(max_length=36)
    invoice_number = models.CharField(max_length=200)
    payment_date = models.DateTimeField()
    parent_id = models.CharField(
        max_length=36,
        null=True,
        help_text="parent transaction is in case of splited transactions",
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "payment_splits"


class PaymentTypes(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    description = models.CharField(max_length=200)
    date = models.DateField()

    objects = models.Manager()

    class Meta:
        db_table = "payment_types"


class PaymentUrls(models.Model):
    id = models.AutoField(primary_key=True)
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE
    )
    agent_id = models.CharField(max_length=50)
    agent_name = models.CharField(max_length=100)
    cust_name = models.CharField(max_length=250, null=True)
    cust_email = models.CharField(max_length=100, null=True)
    cust_mobile = models.CharField(max_length=20, null=True)
    state = models.ForeignKey(StateCodes, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=10, decimal_places=3)
    pay_url = models.TextField()
    pay_short_url = models.CharField(max_length=50)
    payment = models.ForeignKey(
        Recharges, to_field="payment_id", on_delete=models.CASCADE
    )
    type = models.IntegerField(
        default=1, help_text="1 - Anonymous, 2 - BAN type"
    )
    created = models.DateTimeField(auto_now_add=True)

    objects = models.Manager()

    class Meta:
        db_table = "payment_urls"
        indexes = [models.Index(fields=["type"])]


class SubscriptionCharges(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    payment_subscription = models.ForeignKey(
        PaymentSubscriptions, on_delete=models.CASCADE
    )
    gateway = models.CharField(max_length=250)
    subs_id = models.CharField(max_length=200)
    subscription_activated = models.TextField(null=True)
    charge_status = models.CharField(
        max_length=5, help_text="charge current status Y=> success, N=> failed"
    )
    charge_response = models.TextField(null=True, help_text="charge response")
    charge_pid = models.CharField(
        max_length=200, null=True, help_text="subscription charge payment id"
    )
    charge_iid = models.CharField(
        max_length=200, null=True, help_text="charge invoice id"
    )
    charge_oid = models.CharField(
        max_length=200, null=True, help_text="charge order id"
    )
    charge_cid = models.CharField(max_length=200, null=True)
    payment_response = models.TextField(
        null=True, help_text="payment event response after charge"
    )
    order_response = models.TextField(
        null=True, help_text="order response after charge"
    )
    invoice_response = models.TextField(
        null=True, help_text="invoice response after charge"
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "subscription_charges"


class SubscriptionInvoices(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    customer_ref = models.CharField(max_length=200)
    gateway = models.CharField(max_length=50)
    invoice_ref = models.CharField(max_length=200)
    status = models.CharField(max_length=10)
    gateway_resposne = models.TextField()
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "subscription_invoices"


class VanDetails(models.Model):
    id = models.AutoField(primary_key=True)
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE
    )
    van_id = models.CharField(
        max_length=50, help_text="Virtual account id of razorpay"
    )
    receiver_id = models.CharField(max_length=50)
    receiver_entity = models.CharField(max_length=100)
    receiver_name = models.CharField(max_length=250)
    receiver_account_number = models.CharField(max_length=100)
    receiver_ifsc = models.CharField(max_length=50)
    status = models.IntegerField(default=1)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "van_details"
        indexes = [
            models.Index(fields=["van_id"]),
            models.Index(fields=["billing_account_id", "van_id"]),
        ]


class InvoiceNumbers(models.Model):
    id = models.AutoField(primary_key=True)
    product = models.ForeignKey(Products, on_delete=models.CASCADE)
    invo_spec = models.CharField(
        max_length=3, default="ol", help_text="ol => online, of => offline"
    )
    prefix = models.CharField(max_length=10, null=True)
    current_num = models.IntegerField(default=0)
    fin_year = models.CharField(max_length=10)
    valid_from = models.DateTimeField()
    valid_till = models.DateTimeField()

    objects = models.Manager()

    class Meta:
        db_table = "invoice_numbers"
        indexes = [
            models.Index(
                fields=["invo_spec", "valid_from", "valid_till", "product_id"]
            )
        ]


class TrackingSettlementHistories(models.Model):
    id = models.AutoField(primary_key=True)
    setl_key = models.CharField(max_length=200, unique=True)
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE
    )
    payment = models.OneToOneField(
        PaymentTracks,
        to_field="payment_id",
        on_delete=models.CASCADE,
        unique=True,
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    created_by = models.CharField(max_length=36, null=True)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "tracking_settlement_histories"
        indexes = [
            models.Index(fields=["billing_account_id"]),
            models.Index(fields=["payment_id"]),
        ]


class PaymentInvoices(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    payment = models.ForeignKey(
        PaymentTracks, to_field="payment_id", on_delete=models.CASCADE
    )
    invoice_number = models.CharField(max_length=250)
    invoice_date = models.DateTimeField()
    company_name = models.CharField(max_length=250)
    company_address = models.TextField()
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE
    )
    amount_details = models.TextField(
        help_text="amount middle desctiption in terms of json data"
    )
    total_details = models.TextField(
        help_text="json data including taxes and discount if available"
    )
    payable_amount = models.DecimalField(max_digits=10, decimal_places=3)
    pdf_url = models.CharField(max_length=250)
    state_id = models.IntegerField(null=True, default=None)
    gst_no = models.CharField(max_length=30, null=True, default=None)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "payment_invoices"
        indexes = [
            models.Index(fields=["billing_account_id"]),
            models.Index(fields=["created"]),
            models.Index(fields=["payment_id"]),
        ]


class InvoiceQueues(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    billing_account = models.ForeignKey(
        BillingAccounts, on_delete=models.CASCADE
    )
    payment = models.OneToOneField(
        PaymentTracks,
        to_field="payment_id",
        on_delete=models.CASCADE,
        unique=True,
    )
    data = models.TextField(default="[]")
    invoice_date = models.DateTimeField()
    action_response = models.TextField()
    unmature = models.SmallIntegerField(default=0)
    mature = models.SmallIntegerField(default=0)
    status = models.IntegerField(default=0)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "invoice_queues"
        indexes = [models.Index(fields=["billing_account_id"])]


class InvoiceQueueResponses(models.Model):

    id = models.CharField(
        max_length=30, primary_key=True, default=uuid, editable=False
    )
    invoice_queue = models.ForeignKey(InvoiceQueues, on_delete=models.CASCADE)
    response = models.TextField()
    created = models.DateTimeField(auto_now_add=True)

    objects = models.Manager()

    class Meta:
        db_table = "invoice_queue_responses"
        indexes = [models.Index(fields=["invoice_queue_id"])]


class SubscriptionPaymentAttempt(models.Model):
    STATUS_CHOICES = [
        (SubscriptionPaymentAttemptStatusEnum.PENDING.value, "pending"),
        (SubscriptionPaymentAttemptStatusEnum.FAILED.value, "failed"),
        (SubscriptionPaymentAttemptStatusEnum.SUCCESS.value, "success"),
    ]
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    order_id = models.CharField(max_length=200, unique=True)
    payment_subscription = models.ForeignKey(
        PaymentSubscriptions, on_delete=models.CASCADE
    )
    payment_id = models.CharField(max_length=200, null=True, blank=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    status = models.SmallIntegerField(
        choices=STATUS_CHOICES,
        default=SubscriptionPaymentAttemptStatusEnum.PENDING.value,
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "subscription_payment_attempts"
        indexes = [
            models.Index(fields=["payment_id"]),
            models.Index(fields=["payment_subscription"]),
        ]


class PaymentActions(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    payment_id = models.CharField(
        max_length=36
    )  # Foreign Key cant be defined bcz of multiple tables (recharges, subscriptions_auth_payments)
    action_type = models.CharField(
        max_length=50, choices=PaymentActionTypeEnum.choices()
    )
    action_data = models.JSONField(help_text="JSON data for the action")
    failure_reason = models.TextField(null=True, blank=True, default=None)
    status = models.SmallIntegerField(
        choices=PaymentActionStatusEnum.choices(),
        default=PaymentActionStatusEnum.PENDING.value,
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "payment_actions"
        indexes = [
            models.Index(fields=["payment_id"]),
            models.Index(fields=["action_type"]),
            models.Index(fields=["status"]),
        ]

    def mark_as_success(self):
        self.status = PaymentActionStatusEnum.SUCCESS.value
        self.failure_reason = None
        self.save()

    def mark_as_failed(self, failure_reason: str):
        self.status = PaymentActionStatusEnum.FAILED.value
        self.failure_reason = failure_reason
        self.save()
