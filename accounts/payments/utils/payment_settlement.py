import logging
from decimal import ROUND_HALF_UP, Decimal

from django.db import transaction

from accounts.billing_accounts.models import (
    BillingAccountCredits,
    BillingAccounts,
)
from accounts.core.utils.tax_calculation import calc_reverse_tax
from accounts.payments.enums import (
    TransactionClaimStatusEnum,
    TransactionClaimVerifyEnum,
)
from accounts.payments.exceptions import InvalidPaymentException
from accounts.payments.models import (
    PaymentTracks,
    TrackingSettlementHistories,
)
from accounts.payments.tasks import after_payment_settled_task
from accounts.utils.settle_credit_bucket import SettleCreditBucket

logger = logging.getLogger(__name__)


class PaymentSettlement:
    def __init__(self, billing_account: BillingAccounts):
        self.billing_account = billing_account

    def settle_payment_code(self, setl_key: str) -> PaymentTracks:
        """
        Settle the payment code for the billing account.
        :param setl_key: The settlement key for the payment code.
        """
        try:
            payment_track = PaymentTracks.objects.get(
                txn_setl_key=setl_key,
            )
        except PaymentTracks.DoesNotExist:
            raise InvalidPaymentException("Invalid payment code.")

        with transaction.atomic():
            self._settle(payment_track)

        transaction.on_commit(lambda: self._schedule_task(payment_track))
        return payment_track

    def settle_payment_id(self, payment_id: str) -> PaymentTracks:
        """
        Settle the payment by its ID for the billing account.
        :param payment_id: The ID of the payment to settle.
        """
        try:
            payment_track = PaymentTracks.objects.get(
                payment_id=payment_id,
            )
        except PaymentTracks.DoesNotExist:
            raise InvalidPaymentException("Invalid payment ID.")

        with transaction.atomic():
            self._settle(payment_track)

        transaction.on_commit(lambda: self._schedule_task(payment_track))
        return payment_track

    def _settle(self, payment_track: PaymentTracks) -> None:
        # Check if the payment track is already settled or has a settlement history
        if (
            TrackingSettlementHistories.objects.filter(
                setl_key=payment_track.txn_setl_key,
            ).exists()
            or payment_track.is_txn_settled()
        ):
            raise InvalidPaymentException("Payment has already been settled.")

        # Check if the payment track is claimed and verified
        if (
            payment_track.claimed != TransactionClaimStatusEnum.CLAIMED.value
            or payment_track.claim_verify
            != TransactionClaimVerifyEnum.VERIFIED.value
        ):
            raise InvalidPaymentException("Payment is not claimed or verified.")

        amount = payment_track.amount
        if payment_track.tds:
            amount = payment_track.amount + payment_track.tds

        if amount <= 0:
            raise InvalidPaymentException("amount received is 0")

        trns_desc = f"received amount {amount} for payment code #{payment_track.txn_setl_key}"
        if payment_track.tds:
            trns_desc += f" with tds {payment_track.tds} for tan number #{payment_track.tan}"

        # calculate tax from the amount
        amount_without_tax: Decimal = Decimal(
            calc_reverse_tax(99, amount)
        ).quantize(Decimal("0.000"), rounding=ROUND_HALF_UP)
        tax_amount = amount - amount_without_tax
        trns_desc += f" and tax deducted of {tax_amount}"

        BillingAccountCredits.add_credit_amount(
            billing_account=self.billing_account,
            amount=amount_without_tax,
            description=trns_desc,
        )

        TrackingSettlementHistories.objects.create(
            billing_account=self.billing_account,
            payment=payment_track,
            setl_key=payment_track.txn_setl_key,
            amount=amount,
        )
        payment_track.mark_as_settled("Payment settled")
        SettleCreditBucket(self.billing_account).process()

    def _schedule_task(self, payment_track: PaymentTracks) -> None:
        """
        This method should be called once the payment is
        """
        task = (
            after_payment_settled_task.apply_async(
                kwargs={
                    "billing_account_id": self.billing_account.id,
                    "payment_id": payment_track.payment_id,
                },
            ),
        )
        logger.title("After Payment Settled Task Scheduled").info(
            f"ID: {task[0]}"
        )
