import logging
from django.db import transaction
from typing import Optional
from accounts.billing_accounts.models import BillingAccounts
from accounts.billing_accounts.exceptions import (
    InvalidBillingAccountException,
)
from accounts.payments.models import (
    PaymentSubscriptions,
)
from accounts.payments.exceptions import InvalidPaymentSubscriptionException
from accounts.billing_accounts.utils.billing_account import (
    get_product_short_code_from_ban,
)
from accounts.payments.enums import PaymentSubscriptionStatusEnum

logger = logging.getLogger(__name__)


def get_billing_account_subscription(
    billing_account_id: str,
) -> Optional[PaymentSubscriptions]:

    return PaymentSubscriptions.objects.filter(
        billing_account_id=billing_account_id,
        status=PaymentSubscriptionStatusEnum.ENABLED.value,
    ).first()


def set_credit_limit_zero(billing_account_id):
    billing_account = BillingAccounts.objects.filter(
        id=billing_account_id
    ).first()
    if billing_account is None:
        raise InvalidBillingAccountException()

    payment_subscription = PaymentSubscriptions.objects.filter(
        billing_account=billing_account,
        status=PaymentSubscriptionStatusEnum.DISABLED.value,
    ).first()
    if payment_subscription is None:
        raise InvalidPaymentSubscriptionException()

    product_code = get_product_short_code_from_ban(billing_account_id)
    if product_code == "heyoin":
        with transaction.atomic():
            # Set credit limit to 0
            billing_account.credit_limit = 0
            billing_account.save()

    return True
