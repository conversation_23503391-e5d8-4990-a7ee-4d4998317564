import logging
from accounts.billing_accounts.models import BillingAccounts
from accounts.billing_accounts.exceptions import (
    InvalidBillingAccountException,
)
from accounts.payments.models import (
    PaymentSubscriptions,
    SubscriptionPaymentAttempt,
)
from accounts.payments.exceptions import InvalidPaymentSubscriptionException
from accounts.payments.enums import (
    SubscriptionPaymentAttemptStatusEnum,
    PaymentSubscriptionStatusEnum,
)
from accounts.utils.api_services.payment_service import PaymentService

logger = logging.getLogger(__name__)


def process_payment_failure(payment_id, billing_account_id):
    billing_account = BillingAccounts.objects.filter(
        id=billing_account_id
    ).first()
    if billing_account is None:
        raise InvalidBillingAccountException()

    payment_subscription = PaymentSubscriptions.objects.filter(
        billing_account=billing_account,
        status=PaymentSubscriptionStatusEnum.ENABLED.value,
    ).first()
    if payment_subscription is None:
        raise InvalidPaymentSubscriptionException()

    payment_attempt = (
        SubscriptionPaymentAttempt.objects.filter(
            payment_subscription=payment_subscription, payment_id=payment_id
        )
        .order_by("-created")
        .first()
    )

    if (
        payment_attempt
        and payment_attempt.status
        == SubscriptionPaymentAttemptStatusEnum.FAILED.value
    ):
        return PaymentService().cancel_subscription(billing_account_id)

    return True
