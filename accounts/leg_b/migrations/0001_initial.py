# Generated by Django 3.2.18 on 2025-03-19 11:54

import accounts.utils.common
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='LegBRates',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('series', models.CharField(max_length=30)),
                ('leg_b_rate_in', models.DecimalField(decimal_places=3, max_digits=10)),
                ('is_blacklist_in', models.SmallIntegerField(default=0)),
                ('leg_b_rate_gl', models.SmallIntegerField(default=0)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'leg_b_rates',
            },
        ),
        migrations.CreateModel(
            name='LegBRatesExceptions',
            fields=[
                ('id', models.CharField(default=accounts.utils.common.uuid, editable=False, max_length=36, primary_key=True, serialize=False)),
                ('series', models.CharField(max_length=30)),
                ('leg_b_rate', models.DecimalField(decimal_places=3, max_digits=10)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'leg_b_rates_exceptions',
            },
        ),
        migrations.CreateModel(
            name='LegBRatesSchedulers',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('file_name', models.CharField(max_length=250)),
                ('file_url', models.CharField(max_length=250)),
                ('update_time', models.DateTimeField()),
                ('type', models.IntegerField(default=1, help_text='1- Global rates file, 2 - Exception list')),
                ('status', models.IntegerField(default=0, help_text='0 => not processed, 1 => processed, 2 => processing')),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'leg_b_rates_scheduler',
            },
        ),
        migrations.AddIndex(
            model_name='legbratesschedulers',
            index=models.Index(fields=['status', 'update_time'], name='leg_b_rates_status_2ebef5_idx'),
        ),
        migrations.AddIndex(
            model_name='legbratesschedulers',
            index=models.Index(fields=['status'], name='leg_b_rates_status_0f13a0_idx'),
        ),
        migrations.AddField(
            model_name='legbratesexceptions',
            name='country',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.countries'),
        ),
        migrations.AddField(
            model_name='legbrates',
            name='country',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.countries'),
        ),
        migrations.AddIndex(
            model_name='legbratesexceptions',
            index=models.Index(fields=['country_id', 'series'], name='leg_b_rates_country_1b431b_idx'),
        ),
        migrations.AddIndex(
            model_name='legbratesexceptions',
            index=models.Index(fields=['series'], name='leg_b_rates_series_08e5c9_idx'),
        ),
        migrations.AddIndex(
            model_name='legbrates',
            index=models.Index(fields=['series'], name='leg_b_rates_series_2fa382_idx'),
        ),
        migrations.AddConstraint(
            model_name='legbrates',
            constraint=models.UniqueConstraint(fields=('country_id', 'series'), name='test_idx'),
        ),
    ]
