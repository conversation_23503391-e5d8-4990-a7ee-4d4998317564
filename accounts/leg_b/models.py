from django.db import models

from accounts.core.models import Countries
from accounts.utils.common import uuid


class LegBRates(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    country = models.ForeignKey(
        Countries,
        on_delete=models.CASCADE,
    )
    series = models.CharField(max_length=30)
    leg_b_rate_in = models.DecimalField(max_digits=10, decimal_places=3)
    is_blacklist_in = models.SmallIntegerField(default=0)
    leg_b_rate_gl = models.DecimalField(max_digits=10, decimal_places=3)
    leg_b_rate_gl = models.SmallIntegerField(default=0)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "leg_b_rates"
        indexes = [models.Index(fields=["series"])]
        constraints = [
            models.UniqueConstraint(
                fields=["country_id", "series"], name="test_idx"
            )
        ]


class LegBRatesExceptions(models.Model):
    id = models.CharField(
        max_length=36, primary_key=True, default=uuid, editable=False
    )
    country = models.ForeignKey(
        Countries,
        on_delete=models.CASCADE,
    )
    series = models.CharField(max_length=30)
    leg_b_rate = models.DecimalField(max_digits=10, decimal_places=3)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "leg_b_rates_exceptions"
        indexes = [
            models.Index(fields=["country_id", "series"]),
            models.Index(fields=["series"]),
        ]


class LegBRatesSchedulers(models.Model):
    id = models.AutoField(primary_key=True)
    file_name = models.CharField(max_length=250)
    file_url = models.CharField(max_length=250)
    update_time = models.DateTimeField()
    type = models.IntegerField(
        default=1, help_text="1- Global rates file, 2 - Exception list"
    )
    status = models.IntegerField(
        default=0,
        help_text="0 => not processed, 1 => processed, 2 => processing",
    )
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    objects = models.Manager()

    class Meta:
        db_table = "leg_b_rates_scheduler"
        indexes = [
            models.Index(fields=["status", "update_time"]),
            models.Index(fields=["status"]),
        ]
