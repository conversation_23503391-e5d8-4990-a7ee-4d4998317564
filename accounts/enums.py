from typing import Dict, Any, Iterable, List
from enum import Enum


class BaseEnum(Enum):
    @classmethod
    def dict(cls) -> Dict[str, Any]:
        return {e.name: e.value for e in cls}

    @classmethod
    def keys(cls) -> list:
        return [e.name for e in cls]

    @classmethod
    def choices(cls) -> list:
        return [(e.value, e.name) for e in cls]

    @classmethod
    def values(cls) -> list:
        return [i.value for i in cls]

    @classmethod
    def get_value(cls, key: str) -> Any:
        """
        Get the value of an enum key.
        If the key is a string, it will be converted to uppercase.
        If the key is not found, a KeyError will be raised.
        If the key is found, the value will be returned.
        """
        if isinstance(key, str):
            key = key.upper()

        if not hasattr(cls, key):
            raise KeyError(
                f'Invalid key {key}, valid keys are: {", ".join(cls.keys())}'
            )
        return getattr(cls, key).value

    @classmethod
    def get_name(cls, value):
        return cls(value).name

    @classmethod
    def get_values(cls, keys: Iterable[str]) -> List:
        values = []
        for key in keys:
            try:
                enum_val = cls[key].value
                values.append(enum_val)
            except KeyError:
                continue
        return values
